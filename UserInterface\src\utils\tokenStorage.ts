import { openDB } from "idb";

const memoryStorage = new Map<string, string>();
const isSecureContext = window.isSecureContext;
const isInIframe = window.self !== window.top;

let db: any = null;

const initDB = async () => {
  if (db) return db;

  try {
    if (!isSecureContext || isInIframe) {
      throw new Error("Not in a secure context or in an iframe");
    }

    db = await openDB("authDB", 1, {
      upgrade(db) {
        if (!db.objectStoreNames.contains("tokens")) {
          db.createObjectStore("tokens");
        }
      },
    });
    return db;
  } catch (error) {
    console.error("IndexedDB initialization failed:", error);
    return null;
  }
};

const retryAsync = async <T>(
  fn: () => Promise<T>,
  retries = 3,
  delayMs = 100
): Promise<T> => {
  let lastError: any;
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      await new Promise((resolve) => setTimeout(resolve, delayMs));
    }
  }
  throw lastError;
};

export const saveToken = async (key: string, value: string): Promise<void> => {
  try {
    if (key === "refresh_token") {
      const db = await initDB();
      if (db) {
        await db.put("tokens", value, "refresh_token");
        return;
      }
      localStorage.setItem("refresh_token", value);
      return;
    }
    localStorage.setItem(key, value);
  } catch (error) {
    console.error("Failed to save token:", error);
  }
};

export const getToken = async (key: string): Promise<string | null> => {
  try {
    if (key === "refresh_token") {
      const db = await initDB();
      if (db) {
        try {
          return (await retryAsync(() => db.get("tokens", "refresh_token"))) as
            | string
            | null;
        } catch (dbError) {
          return localStorage.getItem("refresh_token");
        }
      }
      return localStorage.getItem("refresh_token");
    }
    return localStorage.getItem(key);
  } catch (error) {
    return memoryStorage.get(key) || null;
  }
};

export const removeToken = async (key: string): Promise<void> => {
  try {
    if (key === "refresh_token") {
      const db = await initDB();
      if (db) {
        await db.delete("tokens", "refresh_token");
      }
    }
    localStorage.removeItem(key);
  } catch (error) {
    memoryStorage.delete(key);
  }
};

export const getRefreshToken = async (): Promise<string | null> => {
  return getToken("refresh_token");
};

export const getAccessToken = async (): Promise<string | null> => {
  return getToken("access_token");
};

export const verifyTokenStorage = async (): Promise<{
  accessToken: boolean;
  refreshToken: boolean;
}> => {
  try {
    const accessToken = localStorage.getItem("access_token");
    let hasRefreshToken = false;

    const db = await initDB();
    if (db) {
      try {
        const refreshToken = await retryAsync(() =>
          db.get("tokens", "refresh_token")
        );
        hasRefreshToken = !!refreshToken;
      } catch {
        hasRefreshToken = !!localStorage.getItem("refresh_token");
      }
    } else {
      hasRefreshToken = !!localStorage.getItem("refresh_token");
    }

    return { accessToken: !!accessToken, refreshToken: hasRefreshToken };
  } catch (error) {
    return { accessToken: false, refreshToken: false };
  }
};

// Helper function to compare server and client tokens
export const compareTokens = async (): Promise<boolean> => {
  try {
    const db = await initDB();
    let serverToken: string | null = null;
    if (db) {
      try {
        serverToken = await retryAsync(() => db.get("tokens", "refresh_token"));
      } catch {
        serverToken = localStorage.getItem("refresh_token");
      }
    } else {
      serverToken = localStorage.getItem("refresh_token");
    }

    const clientToken = localStorage.getItem("refresh_token");

    return serverToken === clientToken;
  } catch (error) {
    console.error("Error comparing tokens:", error);
    return false;
  }
};
