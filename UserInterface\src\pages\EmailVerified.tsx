import { useNavigate } from "react-router-dom";

const EmailVerified = () => {
  const navigate = useNavigate();

  const handleLoginRedirect = () => {
    navigate("/login");
  };

  return (
    <div className="flex justify-center items-center h-screen bg-gradient-to-r from-blue-300 to-purple-400">
      <div className="bg-white p-10 rounded-3xl shadow-2xl text-center max-w-md w-full mx-4">
        <div className="flex justify-center">
          <div className="bg-green-100 p-5 rounded-full">
            <span className="text-green-600 text-5xl">✔</span>
          </div>
        </div>
        <h2 className="text-3xl font-bold mt-6 mb-4">E-mail Vérifié !</h2>
        <p className="text-gray-600 text-lg mb-6">
          Votre compte a été vérifié avec succès.
        </p>
        <button
          onClick={handleLoginRedirect}
          className="mt-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-8 py-3 rounded-lg text-lg font-medium hover:opacity-90 transition-opacity"
        >
          Se connecter
        </button>
      </div>
    </div>
  );
};

export default EmailVerified;
