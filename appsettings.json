{"ConnectionStrings": {"DefaultConnection": "Server=${DB_SERVER};Database=${DB_NAME};User Id=${DB_USER};Password=${DB_PASSWORD};TrustServerCertificate=${DB_TRUST_SERVER_CERTIFICATE};MultipleActiveResultSets=${DB_MULTIPLE_ACTIVE_RESULT_SETS}"}, "AppSettings": {"FrontendBaseUrl": "${FRONTEND_BASE_URL}", "BackendBaseUrl": "${BACKEND_BASE_URL}"}, "SmtpSettings": {"Server": "${SMTP_SERVER}", "Port": "${SMTP_PORT}", "User": "${SMTP_USER}", "Password": "${SMTP_PASSWORD}"}, "AllowedHosts": "*", "Jwt": {"SecretKey": "${JWT_SECRET_KEY}", "Issuer": "${JWT_ISSUER}", "Audience": "${JWT_AUDIENCE}", "AccessTokenExpiryMinutes": "${JWT_ACCESS_TOKEN_EXPIRY_MINUTES}", "RefreshTokenExpiryDays": "${JWT_REFRESH_TOKEN_EXPIRY_DAYS}"}, "AdminSettings": {"SecretKey": "${ADMIN_SECRET_KEY}"}, "Cors": {"AllowedOrigins": "${CORS_ALLOWED_ORIGINS}"}, "Captcha": {"SecretKey": "${RECAPTCHA_SECRET_KEY}", "SecretKeyV2": "${RECAPTCHA_SECRET_KEY_V2}", "SiteKeyV2": "${RECAPTCHA_SITE_KEY_V2}"}}