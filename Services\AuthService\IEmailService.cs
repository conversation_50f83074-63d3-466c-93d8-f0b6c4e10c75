using System.Threading.Tasks;

namespace SiteCheckerApp.Services
{
    public interface IEmailService
    {
        Task SendVerificationEmailAsync(string email, string verificationLink); // Sends email verification link
        Task SendOtpEmail(string email, string otp); // Sends OTP for user authentication
        Task SendPasswordResetEmail(string email, string resetToken); // Sends password reset OTP
        Task SendAdminOtpEmail(string email, string otp); // Sends OTP specifically for admins
    }
}