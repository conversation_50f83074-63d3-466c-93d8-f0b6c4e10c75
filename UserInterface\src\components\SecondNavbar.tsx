import React, { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import Logo from "../components/ui-custom/Logo";

export default function SecondNavbar() {
  const location = useLocation();
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Rediriger vers le dashboard lorsqu'on clique sur le logo
  const logoPath = "/dashboard";

  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Prevent negative scroll values
      if (currentScrollY < 0) return;

      // Show navbar when at top of page
      if (currentScrollY < 50) {
        setIsVisible(true);
      }
      // Hide navbar when scrolling down significantly, show when scrolling up
      else if (currentScrollY > lastScrollY + 5 && currentScrollY > 150) {
        setIsVisible(false);
      } else if (currentScrollY < lastScrollY - 5) {
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    // Add throttling to improve performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledHandleScroll, { passive: true });
    return () => window.removeEventListener("scroll", throttledHandleScroll);
  }, [lastScrollY]);

  return (
    <header
      className="second-navbar-fixed"
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        width: "100%",
        zIndex: 9999,
        transform: isVisible ? "translateY(0)" : "translateY(-100%)",
        transition: "transform 0.3s ease-in-out",
        willChange: "transform",
      }}
    >
      <div className="w-full py-4 px-6 flex justify-between items-center bg-gradient-to-r from-blue-500/10 to-purple-500/10 navbar-blur hover:from-blue-600/15 hover:to-purple-600/15 transition-colors duration-300">
        <Logo to={logoPath} />
        <nav className="flex items-center gap-8">
          <a
            href="#"
            className="text-white hover:text-indigo-100 transition-colors duration-200"
          >
            Services
          </a>
          <a
            href="#"
            className="text-white hover:text-indigo-100 transition-colors duration-200"
          >
            À propos
          </a>
          <a
            href="#"
            className="text-white hover:text-indigo-100 transition-colors duration-200"
          >
            Avis
          </a>
          {/* Removed "Mon Profil" section from the navbar */}
        </nav>
      </div>
    </header>
  );
}
