import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { safeStorage } from "../utils/storage";
// No need for token storage imports with cookie-based auth
import Layout from "../components/Layout";
import Card from "../components/ui-custom/Card";
import Button from "../components/ui-custom/Button";
import VerificationInput from "../components/ui-custom/VerificationInput";
import { toast } from "sonner";
import axios from "axios";
import { useAuth } from "../contexts/AuthContext";

// Base API URL
const API_BASE_URL = "http://localhost:5012"; // Adjust this to match your API server URL

export default function VerifyCode() {
  const [code, setCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const [isAdmin, setIsAdmin] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [resendAttempts, setResendAttempts] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);
  const maxResendAttempts = 5;
  const resendCooldownInitial = 30; // seconds

  // Key for localStorage
  const RESEND_TIMER_KEY = "verifyCodeResendTimer";
  const RESEND_ATTEMPTS_KEY = "verifyCodeResendAttempts";

  const navigate = useNavigate();
  const location = useLocation();
  const { setIsAuthenticated, setUser, setAccessToken } = useAuth();

  // State to track toast messages
  const [toastMessage, setToastMessage] = useState<{
    type: "success" | "error" | "info";
    message: string;
  } | null>(null);

  // Effect to show toast messages
  useEffect(() => {
    if (toastMessage) {
      if (toastMessage.type === "success") {
        toast.success(toastMessage.message);
      } else if (toastMessage.type === "info") {
        toast.info(toastMessage.message);
      } else {
        toast.error(toastMessage.message);
      }
      setToastMessage(null);
    }
  }, [toastMessage]);

  useEffect(() => {
    // Check if this is an admin verification flow
    const searchParams = new URLSearchParams(location.search);
    let adminParam = searchParams.get("isAdmin");

    const checkAdminFlag = async () => {
      if (adminParam === null) {
        // Fallback: check safeStorage for isAdminLogin flag
        const isAdminLogin = await safeStorage.getItem("isAdminLogin");
        if (isAdminLogin === "true") {
          adminParam = "true";
        } else {
          adminParam = "false";
        }
      }
      setIsAdmin(adminParam === "true");

      // Get email from safeStorage - different keys for admin vs password reset
      let storedEmail: string | null = null;
      if (adminParam === "true") {
        storedEmail = await safeStorage.getItem("adminEmail");
        if (!storedEmail) {
          setToastMessage({
            type: "error",
            message: "Session expirée. Veuillez vous reconnecter.",
          });
          navigate("/login");
          return null;
        }
      } else {
        storedEmail = await safeStorage.getItem("resetEmail");
        if (!storedEmail) {
          setToastMessage({
            type: "error",
            message:
              "Adresse email manquante. Veuillez recommencer le processus.",
          });
          navigate("/forgot-password");
          return null;
        }
      }
      return storedEmail;
    };

    checkAdminFlag().then((email) => {
      if (email) setEmail(email);
    });
  }, [navigate, location.search]);

  useEffect(() => {
    // Verify token storage on mount and redirect if missing, except for admin 2FA flow
    const verifyStorage = async () => {
      const isAdminLogin = await safeStorage.getItem("isAdminLogin");
      if (isAdminLogin === "true") {
        return;
      }
    };
    verifyStorage();

    // Load saved countdown timer state
    const loadTimerState = () => {
      try {
        // Get stored end time
        const storedEndTimeStr = localStorage.getItem(RESEND_TIMER_KEY);
        if (storedEndTimeStr) {
          const endTime = parseInt(storedEndTimeStr, 10);
          const now = Date.now();

          // Calculate remaining time
          const remaining = Math.floor((endTime - now) / 1000);

          // Only set if there's time remaining
          if (remaining > 0) {
            setTimeLeft(remaining);
          } else {
            // Clear expired timer
            localStorage.removeItem(RESEND_TIMER_KEY);
          }
        }

        // Get stored attempts
        const storedAttemptsStr = localStorage.getItem(RESEND_ATTEMPTS_KEY);
        if (storedAttemptsStr) {
          const attempts = parseInt(storedAttemptsStr, 10);
          setResendAttempts(attempts);
        }
      } catch (error) {
        console.error("Error loading timer state:", error);
      }
    };

    loadTimerState();
  }, [navigate]);

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;

    if (timeLeft > 0) {
      // Save end time to localStorage
      const endTime = Date.now() + timeLeft * 1000;
      localStorage.setItem(RESEND_TIMER_KEY, endTime.toString());

      timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
    } else if (timeLeft === 0) {
      // Clear timer from localStorage when it reaches zero
      localStorage.removeItem(RESEND_TIMER_KEY);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [timeLeft]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (code.length !== 6) {
      setToastMessage({
        type: "error",
        message: "Veuillez entrer un code de vérification valide.",
      });
      return;
    }

    setLoading(true);

    try {
      if (isAdmin) {
        // Admin login with OTP
        const password = await safeStorage.getItem("adminPassword");
        if (!password) {
          setToastMessage({
            type: "error",
            message: "Session expirée. Veuillez vous reconnecter.",
          });
          navigate("/login");
          return;
        }

        // Call verify-otp API to verify OTP and get tokens
        const verifyResponse = await axios.post(
          `${API_BASE_URL}/api/auth/verify-otp`,
          {
            email,
            verifyCode: code,
          },
          { withCredentials: true }
        );

        // With cookie-based auth, we don't need to manually store tokens
        // The server sets the cookies automatically
        // We just need to verify the response is successful

        if (!verifyResponse.data || !verifyResponse.data.isValid) {
          throw new Error("OTP verification failed");
        }

        // Mark that the user has logged in - do this first
        await safeStorage.setItem("hasLoggedIn", "true");

        // Store admin login status to persist through refreshes
        await safeStorage.setItem("isAdminVerified", "true");

        // Clear temporary admin verification state
        await safeStorage.removeItem("adminPassword");

        // Keep adminEmail and isAdminLogin for a short time to handle page refreshes
        // We'll set a timeout to clear them after successful navigation
        setTimeout(async () => {
          await safeStorage.removeItem("isAdminLogin");
          await safeStorage.removeItem("adminEmail");
        }, 2000);

        // If we have an access token in the response, use it
        if (verifyResponse.data.accessToken) {
          // Store the token in the auth context
          setAccessToken(verifyResponse.data.accessToken);

          // Store the token in a cookie to ensure it's available for API requests
          document.cookie = `access_token=${verifyResponse.data.accessToken}; path=/; secure; samesite=strict`;
        }

        // Update user in context if provided
        if (verifyResponse.data.user) {
          setUser({
            id: verifyResponse.data.user.id,
            email: verifyResponse.data.user.email,
            username: verifyResponse.data.user.username,
            role: verifyResponse.data.user.role.toString(),
          });
        } else if (verifyResponse.data.userId) {
          // Fallback if user object is not provided but we have userId
          setUser({
            id: verifyResponse.data.userId,
            email: email,
            username: verifyResponse.data.username,
            role: String(verifyResponse.data.role || "3"),
          });
        }

        // Set user as authenticated in the context
        setIsAuthenticated(true);

        setToastMessage({
          type: "success",
          message: "Connexion administrateur réussie !",
        });

        // Clear timer and attempts from localStorage
        localStorage.removeItem(RESEND_TIMER_KEY);
        localStorage.removeItem(RESEND_ATTEMPTS_KEY);

        // Navigate to admin-email-verified template
        navigate("/admin-email-verified", { replace: true });
      } else {
        // Regular password reset OTP verification
        await axios.post(`${API_BASE_URL}/api/auth/verify-otp`, {
          email: email,
          verifyCode: code,
        });

        setToastMessage({
          type: "success",
          message: "Code de vérification validé avec succès !",
        });

        // Clear timer and attempts from localStorage
        localStorage.removeItem(RESEND_TIMER_KEY);
        localStorage.removeItem(RESEND_ATTEMPTS_KEY);

        navigate("/reset-password");
      }
    } catch (error: any) {
      console.error("OTP verification failed:", error);
      setToastMessage({
        type: "error",
        message:
          error.response?.data?.message ||
          "Échec de la vérification. Veuillez vérifier le code.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendAttempts >= maxResendAttempts) {
      setToastMessage({
        type: "error",
        message: "Trop de tentatives. Veuillez réessayer plus tard.",
      });
      return;
    }

    setResendLoading(true);

    try {
      if (isAdmin) {
        // For admin, re-attempt login to get a new OTP
        const password = await safeStorage.getItem("adminPassword");
        if (!password) {
          setToastMessage({
            type: "error",
            message: "Session expirée. Veuillez vous reconnecter.",
          });
          navigate("/login");
          return;
        }

        await axios.post(`${API_BASE_URL}/api/auth/login`, {
          email,
          password,
        });
      } else {
        // For password reset
        await axios.post(`${API_BASE_URL}/api/auth/request-password-reset`, {
          email,
        });
      }

      setToastMessage({
        type: "success",
        message: "Un nouveau code a été envoyé à votre adresse email.",
      });

      // Update and store resend attempts
      const newAttempts = resendAttempts + 1;
      setResendAttempts(newAttempts);
      localStorage.setItem(RESEND_ATTEMPTS_KEY, newAttempts.toString());

      // Set cooldown timer
      setTimeLeft(resendCooldownInitial);
    } catch (error: any) {
      setToastMessage({
        type: "error",
        message:
          error.response?.data?.message || "Erreur lors de l'envoi du code.",
      });
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <Layout showNav={true} navType="second">
      <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
        <Card className="w-full max-w-md animate-scale-in">
          <h1 className="text-2xl font-bold text-center text-gray-800 mb-6">
            {isAdmin ? "Vérification Administrateur" : "Vérification du Code"}
          </h1>

          <div className="text-center mb-6">
            <p className="text-gray-600">
              Un code de vérification a été envoyé à
              <br />
              <span className="font-medium">{email}</span>
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Code de vérification
              </label>
              <VerificationInput length={6} onChange={setCode} />
            </div>

            <Button type="submit" className="w-full" loading={loading}>
              {isAdmin ? "Vérifier et se connecter" : "Vérifier le code"}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Vous n'avez pas reçu le code ?{" "}
              <button
                className="text-sitechecker-blue hover:underline"
                onClick={handleResendCode}
                disabled={timeLeft > 0 || resendLoading}
              >
                {timeLeft > 0
                  ? `Renvoyer dans ${timeLeft}s`
                  : resendAttempts >= maxResendAttempts
                  ? "Trop de tentatives. Réessayez plus tard."
                  : resendLoading
                  ? "Envoi..."
                  : "Renvoyer le code"}
              </button>
            </p>
          </div>

          {isAdmin && (
            <div className="mt-4 text-center">
              <button
                className="text-sm text-sitechecker-blue hover:underline"
                onClick={async () => {
                  try {
                    await safeStorage.removeItem("adminEmail");
                    await safeStorage.removeItem("adminPassword");
                    await safeStorage.removeItem("isAdminLogin");

                    // Clear timer and attempts from localStorage
                    localStorage.removeItem(RESEND_TIMER_KEY);
                    localStorage.removeItem(RESEND_ATTEMPTS_KEY);

                    navigate("/login");
                  } catch (error) {
                    toast.error("Erreur lors de la déconnexion");
                  }
                }}
              >
                Retour à la connexion
              </button>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
}
