using System;
using System.Threading.Tasks;

namespace SiteCheckerApp.Services
{
    public interface IRefreshTokenService
    {
        Task<string> GenerateRefreshTokenAsync(Guid userId);
        Task<string> RotateRefreshTokenAsync(Guid userId, string currentRefreshToken);
        Task<bool> ValidateRefreshTokenAsync(Guid userId, string refreshToken);
        Task RevokeRefreshTokenAsync(Guid userId);
    }
}
