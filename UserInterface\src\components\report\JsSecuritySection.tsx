import React from "react";
import { FileCode, AlertTriangle } from "lucide-react";
import { JsLibrary } from "@/types/report";
import { TechnicalSectionItem } from "./interfaces";

interface JsSecuritySectionProps {
  libraries?: JsLibrary[];
  totalVulnerabilities?: number;
  criticalVulnerabilities?: number;
  highVulnerabilities?: number;
  mediumVulnerabilities?: number;
  lowVulnerabilities?: number;
  score?: number;
}

const JsSecuritySection = ({
  libraries = [],
  totalVulnerabilities = 0,
  criticalVulnerabilities = 0,
  highVulnerabilities = 0,
  mediumVulnerabilities = 0,
  lowVulnerabilities = 0,
  score = 0,
}: JsSecuritySectionProps): TechnicalSectionItem => {
  // Handle missing data gracefully
  if (!libraries || libraries.length === 0) {
    // Créer des détails techniques même si aucune bibliothèque n'est détectée
    const noLibrariesTechnicalDetails = [
      {
        parameter: "Bibliothèques analysées",
        value: "0",
        impact: "Aucune bibliothèque JavaScript n'a été détectée",
        tooltip:
          "Votre site pourrait ne pas utiliser de bibliothèques JavaScript ou l'analyse n'a pas pu les détecter",
        recommended:
          "Vérifiez si votre site utilise des bibliothèques JavaScript. Si c'est le cas, assurez-vous qu'elles sont correctement chargées et accessibles pour l'analyse.",
      },
      {
        parameter: "Raisons possibles",
        value: "Plusieurs",
        impact: "Plusieurs raisons peuvent expliquer l'absence de détection",
        tooltip: "L'analyse peut échouer pour diverses raisons techniques",
        recommended:
          "Votre site peut utiliser des techniques de chargement dynamique, des CDN non standards, ou des bibliothèques minifiées difficiles à identifier. Vérifiez comment vos ressources JavaScript sont chargées.",
      },
      {
        parameter: "Impact sur la sécurité",
        value: "Indéterminé",
        impact: "Impossible d'évaluer les risques de sécurité JavaScript",
        tooltip:
          "Sans analyse des bibliothèques, les vulnérabilités potentielles ne peuvent pas être identifiées",
        recommended:
          "Effectuez une analyse manuelle de vos bibliothèques JavaScript pour identifier d'éventuelles vulnérabilités ou versions obsolètes.",
      },
    ];

    return {
      id: "security-4",
      title: "JS Security",
      icon: <FileCode className="w-4 h-4" />,
      score: score, // Utiliser le score passé en paramètre au lieu de forcer à 0
      useUnifiedModel: true,
      impactStatement: {
        text: "La sécurité JavaScript analyse les bibliothèques et frameworks utilisés par votre site web pour détecter les vulnérabilités connues et les versions obsolètes qui pourraient exposer votre site à des risques.",
        positivePoints:
          score >= 75
            ? ["Aucun problème majeur détecté dans l'analyse JavaScript"]
            : [],
        negativePoints: [
          "Aucune bibliothèque JavaScript n'a été détectée ou l'analyse n'a pas pu être effectuée",
        ],
      },
      findings: [
        {
          status: "warning",
          label: "Analyse JavaScript",
          impact:
            "Impossible d'analyser les bibliothèques JavaScript de votre site",
        },
      ],
      technicalDetails: noLibrariesTechnicalDetails,
      actionReference: {
        count: 1, // Indiquer qu'il y a une action à effectuer
        sectionName: "JS Security",
        actionWord: "Vérifier",
        targetId: "js-security-actions",
      },
    };
  }

  // Sort libraries by severity and vulnerability count
  const sortedLibraries = [...libraries].sort((a, b) => {
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3, none: 4 };
    if (severityOrder[a.severity] !== severityOrder[b.severity]) {
      return severityOrder[a.severity] - severityOrder[b.severity];
    }
    return b.vulnerabilities - a.vulnerabilities;
  });

  // Generate positive and negative points based on JS libraries
  let positivePoints = [];

  // Check if there are no vulnerabilities
  if (totalVulnerabilities === 0) {
    positivePoints.push("Aucune vulnérabilité JavaScript détectée");
    positivePoints.push("Vos bibliothèques JavaScript sont à jour");
  }
  // If there are libraries but few or no vulnerabilities
  else if (libraries.length > 0 && totalVulnerabilities < 3) {
    positivePoints.push("Peu de vulnérabilités JavaScript détectées");
  }

  // Add positive point for secure libraries
  const secureLibraries = libraries.filter(
    (lib) => lib.vulnerabilities === 0 && !lib.outdated
  );
  if (
    secureLibraries.length > 0 &&
    secureLibraries.length === libraries.length
  ) {
    positivePoints.push(
      `Toutes vos bibliothèques JavaScript (${libraries.length}) sont sécurisées`
    );
  } else if (secureLibraries.length > 0) {
    positivePoints.push(
      `${secureLibraries.length} bibliothèques JavaScript sur ${libraries.length} sont sécurisées`
    );
  }

  const negativePoints = [
    criticalVulnerabilities > 0
      ? `${criticalVulnerabilities} vulnérabilités critiques dans vos bibliothèques JavaScript`
      : "",
    libraries.filter((lib) => lib.outdated).length > 0
      ? `${
          libraries.filter((lib) => lib.outdated).length
        } bibliothèques JavaScript obsolètes`
      : "",
  ].filter(Boolean);

  // Generate findings based on libraries
  const findings = [];

  // Add findings for critical vulnerabilities
  const criticalLibs = libraries.filter((lib) => lib.severity === "critical");
  for (const lib of criticalLibs.slice(0, 2)) {
    findings.push({
      status: "error",
      label: lib.name,
      impact: `Version ${lib.version} contient ${lib.vulnerabilities} vulnérabilités critiques`,
    });
  }

  // Add findings for outdated libraries
  const outdatedLibs = libraries.filter(
    (lib) => lib.outdated && lib.severity === "none"
  );
  for (const lib of outdatedLibs.slice(0, 2)) {
    findings.push({
      status: "warning",
      label: lib.name,
      impact: `Version obsolète ${lib.version} (dernière: ${lib.latestVersion})`,
    });
  }

  // Add findings for secure libraries if no vulnerabilities found
  if (findings.length === 0 && libraries.length > 0) {
    // Get the most popular libraries first (React, jQuery, etc.)
    const popularLibs = libraries
      .filter(
        (lib) =>
          lib.name.toLowerCase().includes("react") ||
          lib.name.toLowerCase().includes("jquery") ||
          lib.name.toLowerCase().includes("vue") ||
          lib.name.toLowerCase().includes("angular")
      )
      .slice(0, 2);

    // If we have popular libraries, show them
    if (popularLibs.length > 0) {
      for (const lib of popularLibs) {
        findings.push({
          status: "success",
          label: lib.name,
          impact: `Bibliothèque sécurisée détectée (version: ${
            lib.version || "inconnue"
          })`,
        });
      }
    }
    // Otherwise show any libraries
    else {
      for (const lib of libraries.slice(0, 2)) {
        findings.push({
          status: "success",
          label: lib.name,
          impact: `Bibliothèque sécurisée détectée (version: ${
            lib.version || "inconnue"
          })`,
        });
      }
    }

    // Add a summary finding if there are more than 2 libraries
    if (libraries.length > 2) {
      findings.push({
        status: "success",
        label: "Bibliothèques JavaScript",
        impact: `${libraries.length} bibliothèques analysées, toutes sécurisées`,
      });
    }
  }

  // We already defined secureLibraries above

  // Get the top 3 most common libraries for display
  const topLibraries = [...libraries]
    .sort((a, b) =>
      b.name.toLowerCase().includes("react") ||
      b.name.toLowerCase().includes("jquery")
        ? 1
        : -1
    )
    .slice(0, 3)
    .map((lib) => lib.name)
    .join(", ");

  // Generate technical details
  const technicalDetails = [
    {
      parameter: "Bibliothèques analysées",
      value: libraries.length.toString(),
      impact: "Nombre total de bibliothèques JavaScript détectées",
      tooltip: "Bibliothèques JavaScript utilisées par votre site web",
      recommended:
        libraries.length > 10
          ? "Votre site utilise un grand nombre de bibliothèques JavaScript. Envisagez de réduire ce nombre pour améliorer les performances et réduire la surface d'attaque."
          : "Le nombre de bibliothèques utilisées est raisonnable. Assurez-vous de n'utiliser que les bibliothèques nécessaires au fonctionnement de votre site.",
    },
    {
      parameter: "Bibliothèques principales",
      value: topLibraries || "N/A",
      impact: "Frameworks et bibliothèques principales détectées",
      tooltip:
        "Les bibliothèques JavaScript les plus importantes utilisées par votre site",
      recommended:
        "Assurez-vous de maintenir ces bibliothèques à jour et de suivre les meilleures pratiques de sécurité spécifiques à chacune.",
    },
    {
      parameter: "Bibliothèques sécurisées",
      value: secureLibraries.length.toString(),
      impact:
        secureLibraries.length === libraries.length
          ? "Toutes vos bibliothèques sont sécurisées"
          : `${secureLibraries.length} bibliothèques sur ${libraries.length} sont sécurisées`,
      tooltip: "Bibliothèques sans vulnérabilités connues et à jour",
      recommended:
        secureLibraries.length === libraries.length
          ? "Excellent! Continuez à maintenir vos bibliothèques à jour."
          : "Concentrez-vous sur la mise à jour des bibliothèques vulnérables ou obsolètes pour améliorer votre score de sécurité.",
    },
    {
      parameter: "Vulnérabilités critiques",
      value: criticalVulnerabilities.toString(),
      impact: "Nécessitent une correction immédiate",
      tooltip:
        "Vulnérabilités qui peuvent permettre l'exécution de code arbitraire",
      recommended:
        criticalVulnerabilities > 0
          ? "Mettez à jour immédiatement les bibliothèques affectées ou remplacez-les par des alternatives sécurisées. Ces vulnérabilités représentent un risque critique pour votre site."
          : "Aucune vulnérabilité critique détectée. Continuez à surveiller régulièrement vos bibliothèques JavaScript.",
    },
    {
      parameter: "Bibliothèques obsolètes",
      value: libraries.filter((lib) => lib.outdated).length.toString(),
      impact: "Mettez à jour vers les dernières versions",
      tooltip:
        "Les versions obsolètes peuvent contenir des vulnérabilités non corrigées",
      recommended:
        libraries.filter((lib) => lib.outdated).length > 0
          ? `Mettez à jour les ${
              libraries.filter((lib) => lib.outdated).length
            } bibliothèques obsolètes vers leurs dernières versions. Même sans vulnérabilités connues, les versions obsolètes peuvent présenter des risques de sécurité.`
          : "Toutes vos bibliothèques sont à jour. Continuez à maintenir cette bonne pratique.",
    },
  ];

  // Count issues for action reference
  const issueCount =
    criticalVulnerabilities + libraries.filter((lib) => lib.outdated).length;

  // Ensure we have positive points if score is high
  let finalPositivePoints = positivePoints;
  if (score >= 80 && positivePoints.length === 0) {
    finalPositivePoints = [
      "Vos bibliothèques JavaScript présentent peu ou pas de vulnérabilités critiques",
      "La plupart de vos bibliothèques JavaScript sont à jour",
    ];
  }

  // Return the unified model object
  return {
    id: "security-4",
    title: "JS Security",
    icon: <FileCode className="w-4 h-4" />,
    score: score,
    useUnifiedModel: true,
    impactStatement: {
      text: "La sécurité JavaScript analyse les bibliothèques et frameworks utilisés par votre site web pour détecter les vulnérabilités connues et les versions obsolètes qui pourraient exposer votre site à des risques.",
      positivePoints: finalPositivePoints,
      negativePoints: negativePoints,
    },
    findings: findings,
    technicalDetails: technicalDetails,
    actionReference: {
      count: issueCount,
      sectionName: "JS Security",
      actionWord: "Corriger",
      targetId: "js-security-actions",
    },
  };
};

export default JsSecuritySection;
