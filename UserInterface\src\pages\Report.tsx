import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Layout from "../components/Layout";
import {
  Shield,
  Search,
  AlertTriangle,
  X,
  Database,
  Smartphone,
  Gauge,
  FileText,
  Image as ImageIcon,
} from "lucide-react";
import axios from "axios";
import ReportNavigation from "../components/report/ReportNavigation";
import "../styles/sidebar.css";

import { ReportGenerator } from "../utils/ReportGenerator";
import { ReportDownloader } from "../utils/reportDownloader";
import { SecurityReport, SeoReport, ReportAction } from "../types/report";
import { useToast } from "../hooks/use-toast";
import { apiService } from "../services/apiService";
import { useAuth } from "../contexts/AuthContext";
import ScoreCard from "../components/report/ScoreCard";
import PriorityActions from "../components/report/PriorityActions";
import TechnicalSection from "../components/report/TechnicalSection";
import AdvancedSection from "../components/report/AdvancedSection";
import SslSecuritySection from "../components/report/SslSecuritySection";
import HttpHeadersSection from "../components/report/HttpHeadersSection";
import VulnerabilitiesSection from "../components/report/VulnerabilitiesSection";
import JsSecuritySection from "../components/report/JsSecuritySection";
import ApiSecuritySection from "../components/report/ApiSecuritySection";

import { safeStorage } from "../utils/storage";
import { useTranslation } from "react-i18next";

export default function Report() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [url, setUrl] = useState<string>("");
  const [securityReport, setSecurityReport] = useState<SecurityReport | null>(
    null
  );
  const [seoReport, setSeoReport] = useState<SeoReport | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [scanErrors, setScanErrors] = useState<string[]>([]);
  const [scanDuration, setScanDuration] = useState<number | null>(null);
  // These states are used for storing advanced scan results
  // We're not directly using them in the UI anymore as we've created dedicated components
  const [scanProgress, setScanProgress] = useState<number>(0);
  const [isExpanded, setIsExpanded] = useState<boolean>(() => {
    const savedState = localStorage.getItem("reportNavExpanded");
    return savedState !== null ? savedState === "true" : true;
  });
  const { toast } = useToast();
  // Initialize translation hook with proper configuration
  const { t } = useTranslation("translation", { useSuspense: false });

  // Immediately check if the backend is running before rendering anything
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates after unmount

    const checkBackendStatus = async () => {
      try {
        await axios.get(`${import.meta.env.VITE_SCAN_API_URL}/api/health`, {
          timeout: 1000,
        });
        // If we get here, the backend is running
      } catch (error) {
        // Only proceed if component is still mounted
        if (isMounted) {
          // Backend is not running, redirect to dashboard with error message
          toast({
            title: "Service indisponible",
            variant: "destructive",
          });
          navigate("/dashboard?error=backend_unavailable");
        }
      }
    };

    checkBackendStatus();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [navigate, toast]);

  // No need to log user role

  const normalizedRole = String(user?.role || "").toLowerCase();
  const isPremiumOrAdmin =
    Number(user?.role) === 2 ||
    Number(user?.role) === 3 ||
    normalizedRole === "admin" ||
    normalizedRole === "premium";

  // Track scan progress changes
  useEffect(() => {
    // Removed excessive logging
  }, [scanProgress]);

  // Add a class to the body element to ensure proper stacking context
  useEffect(() => {
    // Add classes to body for proper stacking context and report-specific styling
    document.body.classList.add("has-report-sidebar");
    document.body.classList.add("in-report-page");

    // Force repaint to ensure sidebar is visible
    const sidebar = document.getElementById("report-sidebar");
    if (sidebar) {
      sidebar.style.display = "none";
      // Force a reflow
      void sidebar.offsetHeight;
      sidebar.style.display = "flex";
    }

    return () => {
      document.body.classList.remove("has-report-sidebar");
      document.body.classList.remove("in-report-page");
    };
  }, []);

  // Update body class when sidebar expanded state changes
  useEffect(() => {
    if (isExpanded) {
      document.body.classList.remove("sidebar-collapsed");
    } else {
      document.body.classList.add("sidebar-collapsed");
    }

    // Set CSS variable for sidebar width
    document.documentElement.style.setProperty(
      "--sidebar-width",
      isExpanded ? "16rem" : "4rem"
    );

    // Force update of sidebar width
    const sidebar = document.getElementById("report-sidebar");
    if (sidebar) {
      sidebar.style.width = isExpanded ? "16rem" : "4rem";
    }

    return () => {
      document.body.classList.remove("sidebar-collapsed");
    };
  }, [isExpanded]);

  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates after unmount

    const initScan = async () => {
      const params = new URLSearchParams(location.search);
      const urlParam = params.get("url");

      if (!urlParam) {
        if (isMounted) navigate("/dashboard");
        return;
      }

      if (isMounted) setUrl(urlParam);
      // Removed excessive logging

      // Check if backend is running before starting the scan
      try {
        // Make a simple request to check if the backend is running
        await axios
          .get(`${import.meta.env.VITE_SCAN_API_URL}/api/health`, {
            timeout: 2000,
          })
          .catch(() => {
            // If we get any error, assume the backend is not running
            throw new Error("BACKEND_NOT_RUNNING");
          });

        // If we get here, the backend is running and component is still mounted
        if (isMounted) {
          await generateReports(urlParam);
        }
      } catch (error: any) {
        // Only proceed if component is still mounted
        if (isMounted) {
          // Check if it's a backend not running error
          if (
            error.message === "BACKEND_NOT_RUNNING" ||
            error.message === "Network Error" ||
            error.code === "ERR_NETWORK" ||
            error.code === "ERR_CONNECTION_REFUSED"
          ) {
            console.error("Backend service is not running");
            toast({
              title: "Service indisponible",
              variant: "destructive",
            });

            // Redirect back to dashboard with error parameter
            navigate("/dashboard?error=backend_unavailable");
            return;
          }

          // For other errors, continue to generate reports (which will handle the error)
          await generateReports(urlParam);
        }
      }
    };

    initScan();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [location.search, navigate, toast]);

  const generateReports = async (urlParam: string) => {
    const errors: string[] = [];
    const startTime = Date.now();

    try {
      // Add retry logic for batch scan
      let retryCount = 0;
      const maxRetries = 2;
      let scanResults = null;

      while (retryCount <= maxRetries && !scanResults) {
        try {
          // Starting batch scan
          scanResults = await apiService.batchScanUrl(urlParam, (progress) => {
            setScanProgress((prevProgress) => {
              // Only update if the new progress is greater than the current progress
              // This prevents the progress bar from going backwards during retries
              if (progress > prevProgress) {
                // Updating progress
                return progress;
              } else {
                // Keeping current progress
                return prevProgress;
              }
            });
          });
        } catch (scanError: any) {
          // Check for backend not running error - don't retry in this case
          if (
            scanError.message === "BACKEND_NOT_RUNNING" ||
            (scanError.message &&
              scanError.message.includes(
                "Le service d'analyse n'est pas disponible"
              ))
          ) {
            console.error("Backend service is not running, cannot retry");
            // Redirect back to dashboard with error message
            toast({
              title: "Service indisponible",
              variant: "destructive",
            });
            navigate("/dashboard?error=backend_unavailable");
            return null; // Return null to prevent further processing
          }
          // If it's a server error and we haven't exceeded max retries, try again
          else if (
            scanError.response?.status === 500 &&
            retryCount < maxRetries
          ) {
            retryCount++;
            console.warn(`Scan attempt ${retryCount} failed, retrying...`);
            // Wait a bit before retrying
            await new Promise((resolve) => setTimeout(resolve, 1000));
          } else {
            // For other errors or if we've exceeded max retries, throw the error
            throw scanError;
          }
        }
      }

      // If we still don't have scan results after all retries, throw an error
      if (!scanResults) {
        throw new Error("Failed to get scan results after multiple attempts");
      }

      // Check if we got a backend not running error
      if (
        scanResults.security &&
        !scanResults.security.success &&
        (scanResults.security.error === "BACKEND_NOT_RUNNING" ||
          (scanResults.security.error &&
            scanResults.security.error.includes(
              "Le service d'analyse n'est pas disponible"
            )))
      ) {
        // This is a backend not running error, redirect to dashboard
        toast({
          title: "Service indisponible",
          variant: "destructive",
        });
        navigate("/dashboard?error=backend_unavailable");
        return null;
      }

      // Check if we got a non-existent domain error
      if (
        scanResults.security &&
        !scanResults.security.success &&
        scanResults.security.error === "DOMAIN_DOES_NOT_EXIST"
      ) {
        // This is a non-existent domain error, redirect to dashboard
        toast({
          title: "Domaine introuvable",
          description:
            scanResults.security.errorMessage ||
            "Ce domaine n'existe pas ou n'est pas accessible.",
          variant: "destructive",
        });
        navigate("/dashboard?error=domain_not_found");
        return null;
      }

      // Add any scan errors to our errors array
      if (scanResults.errors && scanResults.errors.length > 0) {
        errors.push(...scanResults.errors);
      }

      // Process security report
      let securityReport = null;
      if (scanResults.security) {
        // Check if it's a fallback report
        const isFallbackReport =
          !scanResults.security.success &&
          scanResults.security.error &&
          (scanResults.security.error.includes("timeout") ||
            scanResults.security.error.includes("Server error") ||
            scanResults.security.error.includes("timed out"));

        if (isFallbackReport) {
          console.log("Received fallback security report, processing anyway");
          // Add a warning to scan errors
          errors.push(
            "La partie sécurité de l'analyse a rencontré des problèmes. Les résultats peuvent être incomplets."
          );
        }

        securityReport = ReportGenerator.generateSecurityReport(
          scanResults.security
        );
        setSecurityReport(securityReport);
      }

      // Process SEO report
      let seoReport = null;
      if (scanResults.seo) {
        // Check if it's a fallback report
        const isFallbackReport =
          !scanResults.seo.success &&
          scanResults.seo.error &&
          (scanResults.seo.error.includes("timeout") ||
            scanResults.seo.error.includes("Server error") ||
            scanResults.seo.error.includes("timed out"));

        if (isFallbackReport) {
          console.log("Received fallback SEO report, processing anyway");
          // Add a warning to scan errors
          errors.push(
            "La partie SEO de l'analyse a rencontré des problèmes. Les résultats peuvent être incomplets."
          );
        }

        // Pass the user role information to filter categories for free users

        seoReport = ReportGenerator.generateSeoReport(
          scanResults.seo,
          isPremiumOrAdmin
        );
        setSeoReport(seoReport);
      }

      // Process advanced scan results
      if (isPremiumOrAdmin) {
        // Advanced browser scan
        if (scanResults.advancedBrowser && seoReport) {
          // Integrate browser scan results into SEO report
          seoReport = {
            ...seoReport,
            browserDetails: scanResults.advancedBrowser,
            categories: seoReport.categories.map((category: any) => {
              if (category.name === "Performance") {
                return {
                  ...category,
                  details: [
                    ...category.details,
                    {
                      name: "JavaScript Execution Time",
                      value: scanResults.advancedBrowser.jsExecutionTime,
                    },
                  ],
                };
              }
              return category;
            }),
          };
          setSeoReport(seoReport);
        }

        // Add JS and API security data to security report if available
        if (securityReport) {
          // Add any advanced security data to the security report
          if (scanResults.jsSecurityData) {
            securityReport = {
              ...securityReport,
              js_libraries: scanResults.jsSecurityData.libraries || [],
              js_total_vulnerabilities:
                scanResults.jsSecurityData.totalVulnerabilities || 0,
              js_critical_vulnerabilities:
                scanResults.jsSecurityData.criticalVulnerabilities || 0,
              js_high_vulnerabilities:
                scanResults.jsSecurityData.highVulnerabilities || 0,
              js_medium_vulnerabilities:
                scanResults.jsSecurityData.mediumVulnerabilities || 0,
              js_low_vulnerabilities:
                scanResults.jsSecurityData.lowVulnerabilities || 0,
              js_security_score: scanResults.jsSecurityData.score || 0,
            };
          }

          if (scanResults.apiSecurityData) {
            securityReport = {
              ...securityReport,
              api_endpoints: scanResults.apiSecurityData.endpoints || [],
              api_auth_issues: scanResults.apiSecurityData.authIssues || [],
              api_detected: scanResults.apiSecurityData.apiDetected || false,
              api_security_score: scanResults.apiSecurityData.score || 0,
            };
          }

          setSecurityReport(securityReport);
        }
      }

      const duration = Math.round((Date.now() - startTime) / 1000);
      setScanDuration(duration);

      await saveToRecentReports(
        urlParam,
        securityReport?.overall_score || 0,
        seoReport?.overall_score || 0
      );

      // Store any errors for display in the UI
      if (errors.length > 0) {
        setScanErrors(errors);
      }
    } catch (error: any) {
      console.error("Scan failed:", error);

      // Provide more specific error messages based on the error type
      let errorMessage = "Échec de l'analyse complète";

      // Check for backend not running error
      if (
        error.message === "BACKEND_NOT_RUNNING" ||
        (error.message &&
          error.message.includes("Le service d'analyse n'est pas disponible"))
      ) {
        errorMessage =
          "Le service d'analyse n'est pas disponible. Veuillez vérifier que le backend est en cours d'exécution.";

        // Redirect back to dashboard with error message
        toast({
          title: "Service indisponible",
          variant: "destructive",
        });

        // Use setTimeout to avoid state updates during render
        setTimeout(() => {
          navigate("/dashboard?error=backend_unavailable");
        }, 0);

        return; // Exit early to prevent fallback report creation
      } else if (error.response) {
        // Server responded with an error status
        if (error.response.status === 500) {
          errorMessage =
            "Erreur serveur lors de l'analyse. Veuillez réessayer plus tard.";
        } else if (
          error.response.status === 401 ||
          error.response.status === 403
        ) {
          errorMessage = "Authentification requise pour cette analyse.";
        } else {
          errorMessage = `Erreur ${error.response.status}: ${
            error.response.data?.message || "Échec de l'analyse"
          }`;
        }
      } else if (error.request) {
        // Request was made but no response received
        errorMessage =
          "Aucune réponse du serveur. Vérifiez votre connexion internet ou si le backend est en cours d'exécution.";
      } else if (error.message) {
        // Something else happened
        errorMessage = error.message;
      }

      // Create fallback reports if needed
      if (!securityReport) {
        setSecurityReport({
          success: false,
          error: errorMessage,
          overall_score: 0,
          categories: [],
          ssl_details: {
            certificateType: "Unknown",
            expirationDate: "Unknown",
            issuer: "Unknown",
            cipherStrength: "0",
            forwardSecrecy: false,
            hstsEnabled: false,
            ocspStapling: false,
            vulnerabilities: [],
            supportsTls13: false,
            severity: "Unknown",
          },
          http_headers: [],
          vulnerabilities: [],
          recommended_actions: [
            {
              title: "Réessayer l'analyse",
              description:
                "L'analyse a échoué en raison d'un problème technique.",
              priority: 3, // 3 = High priority
              type: "security",
              implementationDifficulty: "medium",
              estimatedImpact: "high",
            },
          ],
          scan_date: new Date().toISOString(),
        });
      }

      if (!seoReport) {
        setSeoReport({
          success: false,
          error: errorMessage,
          overall_score: 0,
          categories: [],
          meta_tags: [],
          mobile_optimization: {
            responsiveDesign: false,
            viewportConfiguration: false,
            touchTargets: false,
            fontSizes: false,
            mobileUsability: false,
          },
          performance_metrics: {
            responseTime: "0ms",
            compressionRatio: "0%",
            cacheStatus: "unknown",
            pageSize: "0KB",
            firstContentfulPaint: "0ms",
            largestContentfulPaint: "0ms",
            timeToInteractive: "0ms",
            cumulativeLayoutShift: "0",
            speedIndex: "0",
          },
          url_structure: [],
          image_analysis: {
            totalImages: 0,
            imagesWithAlt: 0,
            imagesWithoutAlt: 0,
            imagesCompressed: 0,
            imagesUncompressed: 0,
            imagesWithLazyLoading: 0,
            largeImages: [],
            missingAltImages: [],
          },
          content_analysis: null,
          recommended_actions: [
            {
              title: "Réessayer l'analyse",
              description:
                "L'analyse SEO a échoué en raison d'un problème technique.",
              priority: 3, // 3 = High priority
              type: "seo",
              implementationDifficulty: "medium",
              estimatedImpact: "high",
            },
          ],
          scan_date: new Date().toISOString(),
        });
      }

      // Add the error to the scan errors list
      setScanErrors([errorMessage]);
    } finally {
      // Always set loading to false to ensure we exit the loading state
      setLoading(false);
    }
  };

  const saveToRecentReports = async (
    url: string,
    securityScore: number,
    seoScore: number
  ) => {
    try {
      const newReport = {
        url,
        date: new Date().toISOString(),
        securityScore: securityScore,
        seoScore: seoScore,
      };

      const existingReportsJson = await safeStorage.getItem("recentReports");
      let existingReports = [];

      if (existingReportsJson) {
        try {
          existingReports = JSON.parse(existingReportsJson);
        } catch (parseError) {
          console.error("Error parsing existing reports:", parseError);
          existingReports = [];
        }
      }

      const updatedReports = [
        newReport,
        ...existingReports.filter((r: any) => r.url !== url),
      ].slice(0, 5);

      await safeStorage.setItem(
        "recentReports",
        JSON.stringify(updatedReports)
      );

      // Get scan depth from URL parameters
      const params = new URLSearchParams(location.search);
      const scanDepth = params.get("depth") || "basic";

      // Record scan in database for admin statistics
      try {
        await apiService.recordScan(
          url,
          "security",
          scanDepth,
          securityScore,
          true
        );

        await apiService.recordScan(url, "seo", scanDepth, seoScore, true);
      } catch (recordError) {
        console.error("Error recording scan history:", recordError);
        // Don't throw - we don't want to interrupt the user experience
        // if scan recording fails
      }
    } catch (error) {
      console.error("Error saving report to history:", error);
    }
  };

  const recommendedActions: ReportAction[] = [
    ...(securityReport?.recommended_actions || []),
    ...(seoReport?.recommended_actions || []),
  ].sort((a, b) => b.priority - a.priority);

  const handleDownloadReport = (format: "pdf" | "html" = "pdf") => {
    // Check if user is premium or admin
    if (!isPremiumOrAdmin) {
      // Use setTimeout to avoid the React warning about updating during render
      setTimeout(() => {
        toast({
          title: "Fonctionnalité Premium",
          description:
            "Le téléchargement de rapports est réservé aux utilisateurs Premium",
          variant: "destructive",
        });
      }, 0);

      // Redirect to payment plans page
      navigate("/payment-plans");
      return;
    }

    // Use setTimeout to avoid the React warning about updating during render
    setTimeout(() => {
      toast({
        title: `Préparation du rapport ${format.toUpperCase()}...`,
        description: "Votre rapport est en cours de génération",
      });
    }, 0);

    try {
      if (format === "pdf") {
        generatePdfReport();
      } else {
        generateHtmlReport();
      }
    } catch (error) {
      console.error(`Error generating ${format} report:`, error);
      // Use setTimeout to avoid the React warning about updating during render
      setTimeout(() => {
        toast({
          title: "Erreur",
          description: `Échec de la génération du rapport ${format.toUpperCase()}`,
          variant: "destructive",
        });
      }, 0);
    }
  };

  // Generate and download PDF report
  const generatePdfReport = () => {
    try {
      // Use the ReportDownloader utility class to generate the PDF report
      ReportDownloader.generatePdfReport(
        url,
        securityReport,
        seoReport,
        scanDuration,
        recommendedActions
      )
        .then(() => {
          // Success notification
          setTimeout(() => {
            toast({
              title: "Succès",
              description: "Rapport PDF téléchargé avec succès!",
              variant: "default",
            });
          }, 0);
        })
        .catch((error: any) => {
          console.error("Error generating PDF:", error);
          throw error;
        });
    } catch (error) {
      console.error("Error generating PDF:", error);
      throw error;
    }
  };

  // Generate and download HTML report
  const generateHtmlReport = () => {
    try {
      // Use the ReportDownloader utility class to generate the HTML report
      ReportDownloader.generateHtmlReport(
        url,
        securityReport,
        seoReport,
        scanDuration,
        recommendedActions
      )
        .then(() => {
          // Success notification
          setTimeout(() => {
            toast({
              title: "Succès",
              description: "Rapport HTML téléchargé avec succès!",
              variant: "default",
            });
          }, 0);
        })
        .catch((error: any) => {
          console.error("Error generating HTML:", error);
          throw error;
        });
    } catch (error) {
      console.error("Error generating HTML:", error);
      throw error;
    }
  };

  // Vulnerabilities are now handled through the unified model

  // Progress is now directly updated from the API progress callback

  // Get current scan status message based on progress and user type
  const getScanStatusMessage = () => {
    if (isPremiumOrAdmin) {
      // For premium/admin users (comprehensive scan - 4 steps)
      if (scanProgress < 40) return "Analyse de sécurité en cours...";
      if (scanProgress < 70) return "Analyse SEO en cours...";
      if (scanProgress < 85) return "Analyse SSL avancée en cours...";
      if (scanProgress < 100) return "Analyse navigateur en cours...";
    } else {
      // For free users (basic scan - 2 steps)
      if (scanProgress < 50) return "Analyse de sécurité en cours...";
      if (scanProgress < 100) return "Analyse SEO en cours...";
    }
    return "Finalisation de l'analyse...";
  };

  // Handle cancel button click
  const handleCancelScan = () => {
    // Show confirmation dialog
    if (
      window.confirm("Êtes-vous sûr de vouloir annuler l'analyse en cours ?")
    ) {
      // Cancel the scan in the API service
      apiService.cancelScan();

      // Clear any stored scan data
      localStorage.removeItem("currentScanUrl");

      // Reset all state variables to ensure a clean slate for new scans
      setUrl("");
      setSecurityReport(null);
      setSeoReport(null);
      setLoading(false);
      setScanErrors([]);
      setScanProgress(0);

      // Redirect to dashboard with replace to prevent back navigation to the cancelled scan
      navigate("/dashboard", { replace: true });
    }
  };

  // Move the useEffect hook outside of the conditional rendering
  // This ensures hooks are called in the same order on every render
  React.useEffect(() => {
    if (loading) {
      // Removed excessive logging
    }
  }, [scanProgress, loading]);

  // Always define the useEffect hook for checking backend status when progress is low
  // This ensures hooks are called in the same order on every render
  useEffect(() => {
    // Only run the check if we're in loading state and progress is low
    if (loading && scanProgress <= 10) {
      const timeoutId = window.setTimeout(async () => {
        try {
          // Try to ping the backend
          await axios
            .get(`${import.meta.env.VITE_SCAN_API_URL}/api/health`, {
              timeout: 1000,
            })
            .catch(() => {
              throw new Error("Backend not running");
            });
        } catch (error) {
          // Backend is not responding, redirect to dashboard
          toast({
            title: "Service indisponible",
            variant: "destructive",
          });
          navigate("/dashboard?error=backend_unavailable");
        }
      }, 3000); // Check after 3 seconds if progress is still low

      return () => {
        window.clearTimeout(timeoutId);
      };
    }
  }, [scanProgress, loading, toast, navigate]);

  // Force exit from loading state when progress reaches 100% or after a maximum time
  useEffect(() => {
    // Case 1: Exit when progress reaches 100%
    if (scanProgress === 100 && loading) {
      // Add a small delay to ensure all data is processed
      const timer = setTimeout(() => {
        // Force exit from loading state
        setLoading(false);
      }, 1500);

      return () => clearTimeout(timer);
    }

    // Case 2: Safety timeout - if we've been loading for more than 2 minutes, force exit
    if (loading) {
      const maxLoadingTime = 120000; // 2 minutes
      const timer = setTimeout(() => {
        // Force exit after timeout
        setLoading(false);
        setScanErrors((prev) => [
          ...prev,
          "Scan timed out after 2 minutes. Results may be incomplete.",
        ]);
      }, maxLoadingTime);

      return () => clearTimeout(timer);
    }
  }, [scanProgress, loading, setScanErrors]);

  // Check if we're in a loading state
  if (loading) {
    const statusMessage = getScanStatusMessage();
    // Removed excessive logging

    return (
      <Layout showNav={false} showFooter={false}>
        <div className="container mx-auto px-4 py-12">
          <div className="flex flex-col justify-center items-center min-h-[50vh] space-y-4">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-blue-500 absolute top-0 left-0"></div>
              <div className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center">
                <div className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 w-3 rounded-full animate-pulse-opacity"></div>
              </div>
            </div>
            <p className="text-gray-700 font-medium">
              Analyse en cours pour {url}
            </p>
            <p className="text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded-full">
              {statusMessage}
            </p>
            <div className="w-full max-w-md bg-gray-200 rounded-full h-3 overflow-hidden">
              <div
                key={`progress-${scanProgress}`} // Add key to force re-render on progress change
                className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-500 ease-in-out relative"
                style={{
                  width: `${scanProgress}%`,
                  boxShadow: "0 0 8px rgba(59, 130, 246, 0.5)",
                }}
                data-testid="progress-bar"
              >
                {/* Add a pulsing animation inside the progress bar */}
                <div className="absolute inset-0 bg-white opacity-20 animate-pulse-opacity"></div>
              </div>
            </div>
            <p className="text-xs text-gray-500 font-medium">
              {scanProgress}% terminé
            </p>
            <div className="w-full max-w-md mt-4">
              {/* Security scan progress - adjusted based on user type */}
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Analyse de sécurité</span>
                <span
                  className={
                    isPremiumOrAdmin
                      ? scanProgress >= 40
                        ? "text-green-600 font-medium"
                        : ""
                      : scanProgress >= 50
                      ? "text-green-600 font-medium"
                      : ""
                  }
                >
                  {isPremiumOrAdmin
                    ? scanProgress >= 40
                      ? "✓ Terminé"
                      : "En cours..."
                    : scanProgress >= 50
                    ? "✓ Terminé"
                    : "En cours..."}
                </span>
              </div>

              {/* SEO scan progress - adjusted based on user type */}
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Analyse SEO</span>
                <span
                  className={
                    isPremiumOrAdmin
                      ? scanProgress >= 70
                        ? "text-green-600 font-medium"
                        : ""
                      : scanProgress >= 100
                      ? "text-green-600 font-medium"
                      : ""
                  }
                >
                  {isPremiumOrAdmin
                    ? scanProgress >= 70
                      ? "✓ Terminé"
                      : "En cours..."
                    : scanProgress >= 100
                    ? "✓ Terminé"
                    : "En cours..."}
                </span>
              </div>

              {/* Advanced scans - only for premium/admin users */}
              {isPremiumOrAdmin && (
                <>
                  <div className="flex justify-between text-sm text-gray-600 mb-1">
                    <span>Analyse SSL avancée</span>
                    <span
                      className={
                        scanProgress >= 85 ? "text-green-600 font-medium" : ""
                      }
                    >
                      {scanProgress >= 85 ? "✓ Terminé" : "En cours..."}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Analyse navigateur</span>
                    <span
                      className={
                        scanProgress >= 95 ? "text-green-600 font-medium" : ""
                      }
                    >
                      {scanProgress >= 95 ? "✓ Terminé" : "En cours..."}
                    </span>
                  </div>
                </>
              )}
            </div>

            {/* Cancel button */}
            <button
              onClick={handleCancelScan}
              className="mt-6 py-2 px-4 bg-gradient-blue-purple hover:opacity-80 rounded-md text-white transition-colors flex items-center"
            >
              Annuler l'analyse
              <X className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNav={false} showFooter={false}>
      <div
        className="min-h-screen overflow-hidden report-page-container"
        style={{
          background: "linear-gradient(to bottom, #d8b4fe, #bfdbfe)",
          contain: "content",
          position: "relative",
          height: "100vh",
          overflow: "hidden",
        }}
      >
        {/* Navigation sidebar */}
        <ReportNavigation
          securityScore={securityReport?.overall_score || 0}
          seoScore={seoReport?.overall_score || 0}
          url={url}
          onBack={async () => {
            // Cancel any ongoing scans before navigating
            await apiService.cancelScan();

            // Clear any stored scan data
            localStorage.removeItem("currentScanUrl");

            // Reset all state variables to ensure a clean slate for new scans
            setUrl("");
            setSecurityReport(null);
            setSeoReport(null);
            setLoading(false);
            setScanErrors([]);
            setScanProgress(0);

            // Navigate back to dashboard
            navigate("/dashboard", { replace: true });
          }}
          onDownload={(format: "pdf" | "html") => handleDownloadReport(format)}
          scanDuration={scanDuration}
          isPremiumOrAdmin={isPremiumOrAdmin}
          isExpanded={isExpanded}
          onExpandToggle={(expanded) => setIsExpanded(expanded)}
        />

        {/* Main content with margin to accommodate the fixed sidebar */}
        <div
          className="report-content transition-all duration-300 print:ml-0 overflow-y-auto"
          style={{ height: "100vh", overflowY: "auto", overflowX: "hidden" }}
        >
          <div className="container mx-auto px-4 sm:px-6 py-6 max-w-6xl">
            {scanErrors.length > 0 && (
              <div className="max-w-5xl mx-auto mb-6 bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-start space-x-2">
                <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <h3 className="font-medium text-amber-800">Attention</h3>
                  <p className="text-sm text-amber-700">
                    Certaines parties de l'analyse n'ont pas pu être complétées
                  </p>
                  <ul className="list-disc list-inside text-xs text-amber-700 mt-1">
                    {scanErrors.map((error, i) => (
                      <li key={i}>{error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>

          <div className="container mx-auto px-4">
            <div className="max-w-5xl mx-auto space-y-8">
              {/* Overview section with ID for navigation */}
              <div
                id="overview"
                className="grid grid-cols-1 md:grid-cols-2 gap-6"
              >
                <ScoreCard
                  type="security"
                  report={securityReport}
                  icon={<Shield className="w-5 h-5 text-neon-purple" />}
                />
                <ScoreCard
                  type="seo"
                  report={seoReport}
                  icon={<Search className="w-5 h-5 text-neon-magenta" />}
                />
              </div>

              {/* Priority Actions section already has an ID */}
              <PriorityActions actions={recommendedActions} />

              <AdvancedSection
                unlocked={isPremiumOrAdmin}
                onUnlock={() => navigate("/payment-plans")}
              >
                <div className="mb-8">
                  <h3 className="text-xl font-bold mb-6 text-center">
                    <span className="bg-gradient-to-r from-neon-purple to-neon-blue text-transparent bg-clip-text">
                      {t("report.technical_details")}
                    </span>
                  </h3>

                  <div className="space-y-10">
                    {/* Security Analysis Section */}
                    <div
                      id="security-details"
                      data-section="security"
                      className="neo-card p-5 bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl shadow-lg"
                      style={{
                        borderLeft: "4px solid",
                        borderImage:
                          "linear-gradient(to bottom, #3b82f6, #a855f7) 1",
                      }}
                    >
                      <div className="mb-4 flex items-center">
                        <div className="p-3 rounded-full bg-gradient-to-r from-blue-500/30 to-purple-500/30 mr-4 shadow-md">
                          <Shield className="w-6 h-6 text-neon-purple" />
                        </div>
                        <h3 className="text-xl font-bold bg-gradient-to-r from-neon-purple to-neon-blue text-transparent bg-clip-text">
                          {t("security.analysis")}
                        </h3>
                      </div>

                      <div className="w-full h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mb-4 shadow-sm"></div>

                      <TechnicalSection
                        title="hidden-title"
                        icon={<Shield className="w-5 h-5 text-neon-purple" />}
                        type="security"
                        items={[
                          SslSecuritySection({
                            sslDetails: securityReport?.ssl_details,
                            score: securityReport?.ssl_score,
                          }),
                          HttpHeadersSection({
                            headers: securityReport?.http_headers,
                            score: securityReport?.headers_score,
                          }),
                          VulnerabilitiesSection({
                            vulnerabilities: securityReport?.vulnerabilities,
                            score: securityReport?.vulnerabilities_score,
                          }),
                          JsSecuritySection({
                            libraries: securityReport?.js_libraries,
                            totalVulnerabilities:
                              securityReport?.js_total_vulnerabilities,
                            criticalVulnerabilities:
                              securityReport?.js_critical_vulnerabilities,
                            highVulnerabilities:
                              securityReport?.js_high_vulnerabilities,
                            mediumVulnerabilities:
                              securityReport?.js_medium_vulnerabilities,
                            lowVulnerabilities:
                              securityReport?.js_low_vulnerabilities,
                            score: securityReport?.js_security_score,
                          }),
                          ApiSecuritySection({
                            endpoints: securityReport?.api_endpoints,
                            authenticationIssues:
                              securityReport?.api_auth_issues,
                            apiDetected: securityReport?.api_detected,
                            score: securityReport?.api_security_score,
                          }),
                        ]}
                      />
                    </div>

                    {/* SEO Analysis Section */}
                    <div
                      id="seo-details"
                      data-section="seo"
                      className="neo-card p-5 bg-gradient-to-br from-fuchsia-50 to-cyan-50 rounded-xl shadow-lg"
                      style={{
                        borderLeft: "4px solid",
                        borderImage:
                          "linear-gradient(to bottom, #d946ef, #22d3ee) 1",
                      }}
                    >
                      <div className="mb-4 flex items-center">
                        <div className="p-3 rounded-full bg-gradient-to-r from-fuchsia-500/30 to-cyan-400/30 mr-4 shadow-md">
                          <Search className="w-6 h-6 text-fuchsia-500" />
                        </div>
                        <h3 className="text-xl font-bold bg-gradient-to-r from-fuchsia-500 to-cyan-400 text-transparent bg-clip-text">
                          {t("seo.analysis")}
                        </h3>
                      </div>

                      <div className="w-full h-1 bg-gradient-to-r from-fuchsia-500 to-cyan-400 rounded-full mb-4 shadow-sm"></div>

                      <TechnicalSection
                        title="hidden-title"
                        icon={<Search className="w-5 h-5 text-fuchsia-500" />}
                        type="seo"
                        items={[
                          {
                            id: "seo-1",
                            title: "Meta Tags",
                            icon: <Database className="w-4 h-4" />,
                            score: seoReport?.meta_score,
                            useUnifiedModel: true,
                            impactStatement: {
                              text: "Les balises meta aident les moteurs de recherche à comprendre le contenu de votre page et améliorent sa visibilité. Des balises meta bien optimisées peuvent augmenter votre taux de clics et votre classement dans les résultats de recherche.",
                              positivePoints: [
                                seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "title" &&
                                    tag.status === "success"
                                )
                                  ? "Titre optimisé pour le référencement"
                                  : "",
                                seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "description" &&
                                    tag.status === "success"
                                )
                                  ? "Description meta bien structurée"
                                  : "",
                                seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "viewport" &&
                                    tag.status === "success"
                                )
                                  ? "Configuration viewport correcte pour les appareils mobiles"
                                  : "",
                                // Add a generic positive point if score is high but no specific positive points
                                (seoReport?.meta_score || 0) >= 80 &&
                                ![
                                  seoReport?.meta_tags?.some(
                                    (tag) =>
                                      tag.name === "title" &&
                                      tag.status === "success"
                                  ),
                                  seoReport?.meta_tags?.some(
                                    (tag) =>
                                      tag.name === "description" &&
                                      tag.status === "success"
                                  ),
                                  seoReport?.meta_tags?.some(
                                    (tag) =>
                                      tag.name === "viewport" &&
                                      tag.status === "success"
                                  ),
                                ].some(Boolean)
                                  ? "Vos balises meta sont globalement bien configurées pour le référencement"
                                  : "",
                              ].filter(Boolean),
                              negativePoints: [
                                !seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "title" &&
                                    tag.status === "success"
                                )
                                  ? "Titre non optimisé ou manquant"
                                  : "",
                                !seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "description" &&
                                    tag.status === "success"
                                )
                                  ? "Description meta manquante ou trop courte"
                                  : "",
                                !seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "canonical" &&
                                    tag.status === "success"
                                )
                                  ? "Balise canonique manquante (risque de contenu dupliqué)"
                                  : "",
                              ].filter(Boolean),
                            },
                            findings: [
                              {
                                status: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "title" &&
                                    tag.status === "success"
                                )
                                  ? "success"
                                  : "error",
                                label: "Balise Titre",
                                impact: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "title" &&
                                    tag.status === "success"
                                )
                                  ? "Titre optimisé qui peut améliorer votre taux de clics de 20%"
                                  : "Un titre non optimisé réduit votre visibilité dans les résultats de recherche",
                              },
                              {
                                status: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "description" &&
                                    tag.status === "success"
                                )
                                  ? "success"
                                  : "error",
                                label: "Meta Description",
                                impact: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "description" &&
                                    tag.status === "success"
                                )
                                  ? "Description convaincante qui peut améliorer votre taux de clics de 15%"
                                  : "Une description manquante ou faible réduit l'attractivité de votre site dans les résultats",
                              },
                              {
                                status: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "viewport" &&
                                    tag.status === "success"
                                )
                                  ? "success"
                                  : "error",
                                label: "Viewport",
                                impact: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "viewport" &&
                                    tag.status === "success"
                                )
                                  ? "Configuration correcte pour l'affichage sur mobile (60% des recherches)"
                                  : "Mauvaise expérience sur mobile, ce qui pénalise votre référencement",
                              },
                              {
                                status: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "canonical" &&
                                    tag.status === "success"
                                )
                                  ? "success"
                                  : "warning",
                                label: "Canonical",
                                impact: seoReport?.meta_tags?.some(
                                  (tag) =>
                                    tag.name === "canonical" &&
                                    tag.status === "success"
                                )
                                  ? "Évite les problèmes de contenu dupliqué qui diluent votre autorité SEO"
                                  : "Risque de pénalité pour contenu dupliqué, diluant votre autorité SEO",
                              },
                            ],
                            technicalDetails: [
                              ...(seoReport?.meta_tags || [])
                                .slice(0, 6)
                                .map((tag) => {
                                  // Create specific impact messages based on tag name and status
                                  const getImpactMessage = (
                                    tagName: string,
                                    status: string
                                  ) => {
                                    const impactMessages: Record<
                                      string,
                                      Record<string, string>
                                    > = {
                                      title: {
                                        success:
                                          "Titre optimisé qui peut améliorer votre taux de clics de 20%",
                                        warning:
                                          "Titre présent mais pourrait être mieux optimisé",
                                        error:
                                          "Titre manquant ou non optimisé - réduit votre visibilité",
                                      },
                                      description: {
                                        success:
                                          "Description convaincante qui peut améliorer votre taux de clics de 15%",
                                        warning:
                                          "Description présente mais pourrait être plus attractive",
                                        error:
                                          "Description manquante - réduit l'attractivité de votre site",
                                      },
                                      viewport: {
                                        success:
                                          "Configuration correcte pour l'affichage sur mobile (60% des recherches)",
                                        warning:
                                          "Configuration viewport présente mais pourrait être optimisée",
                                        error:
                                          "Mauvaise expérience sur mobile - pénalise votre référencement",
                                      },
                                      robots: {
                                        success:
                                          "Contrôle correct de l'indexation par les moteurs de recherche",
                                        warning:
                                          "Balise robots présente mais pourrait être optimisée",
                                        error:
                                          "Balise robots manquante - contrôle limité de l'indexation",
                                      },
                                      charset: {
                                        success:
                                          "Encodage correct des caractères pour tous les navigateurs",
                                        warning:
                                          "Encodage présent mais pourrait être optimisé",
                                        error:
                                          "Problèmes potentiels d'affichage des caractères",
                                      },
                                    };

                                    return (
                                      impactMessages[tagName]?.[status] ||
                                      (status === "success"
                                        ? "Correctement optimisé pour le référencement"
                                        : "Nécessite une optimisation pour améliorer le référencement")
                                    );
                                  };

                                  return {
                                    parameter: tag.name,
                                    value: tag.content || "Non défini",
                                    impact: getImpactMessage(
                                      tag.name,
                                      tag.status
                                    ),
                                    tooltip:
                                      tag.description ||
                                      `Balise meta qui affecte le référencement de votre site`,
                                  };
                                }),
                            ],
                            actionReference: {
                              count: (seoReport?.meta_tags || []).filter(
                                (tag) => tag.status !== "success"
                              ).length,
                              sectionName: "Meta Tags",
                              actionWord: "Optimiser",
                              targetId: "meta-tags-actions",
                            },
                          },
                          {
                            id: "seo-2",
                            title: "Mobile Optimization",
                            icon: <Smartphone className="w-4 h-4" />,
                            score: seoReport?.mobile_score,
                            useUnifiedModel: true,
                            impactStatement: {
                              text: "L'optimisation mobile est essentielle car plus de 60% des recherches sont effectuées sur des appareils mobiles. Google utilise l'indexation mobile-first, ce qui signifie que la version mobile de votre site détermine votre classement.",
                              positivePoints: [
                                seoReport?.mobile_optimization?.responsiveDesign
                                  ? "Design responsive qui s'adapte à tous les appareils"
                                  : "",
                                seoReport?.mobile_optimization
                                  ?.viewportConfiguration
                                  ? "Configuration viewport correcte"
                                  : "",
                                seoReport?.mobile_optimization?.mobileUsability
                                  ? "Bonne expérience utilisateur sur mobile"
                                  : "",
                                // Add a generic positive point if score is high but no specific positive points
                                (seoReport?.mobile_score || 0) >= 80 &&
                                ![
                                  seoReport?.mobile_optimization
                                    ?.responsiveDesign,
                                  seoReport?.mobile_optimization
                                    ?.viewportConfiguration,
                                  seoReport?.mobile_optimization
                                    ?.mobileUsability,
                                ].some(Boolean)
                                  ? "Votre site est globalement bien optimisé pour les appareils mobiles"
                                  : "",
                              ].filter(Boolean),
                              negativePoints: [
                                !seoReport?.mobile_optimization
                                  ?.responsiveDesign
                                  ? "Design non responsive qui nuit à l'expérience mobile"
                                  : "",
                                !seoReport?.mobile_optimization?.touchTargets
                                  ? "Cibles tactiles trop petites ou trop proches"
                                  : "",
                                !seoReport?.mobile_optimization?.fontSizes
                                  ? "Tailles de police inadaptées pour la lecture sur mobile"
                                  : "",
                              ].filter(Boolean),
                            },
                            findings: [
                              {
                                status: seoReport?.mobile_optimization
                                  ?.responsiveDesign
                                  ? "success"
                                  : "error",
                                label: "Design Responsive",
                                impact: seoReport?.mobile_optimization
                                  ?.responsiveDesign
                                  ? "Votre site s'adapte correctement à tous les appareils"
                                  : "Votre site ne s'adapte pas correctement aux différentes tailles d'écran",
                              },
                              {
                                status: seoReport?.mobile_optimization
                                  ?.viewportConfiguration
                                  ? "success"
                                  : "error",
                                label: "Configuration Viewport",
                                impact: seoReport?.mobile_optimization
                                  ?.viewportConfiguration
                                  ? "Paramètres viewport correctement définis pour l'affichage mobile"
                                  : "Paramètres viewport incorrects ou manquants",
                              },
                              {
                                status: seoReport?.mobile_optimization
                                  ?.touchTargets
                                  ? "success"
                                  : "warning",
                                label: "Cibles Tactiles",
                                impact: seoReport?.mobile_optimization
                                  ?.touchTargets
                                  ? "Boutons et liens facilement cliquables sur mobile"
                                  : "Certains éléments sont difficiles à toucher sur mobile (trop petits ou trop proches)",
                              },
                              {
                                status: seoReport?.mobile_optimization
                                  ?.fontSizes
                                  ? "success"
                                  : "warning",
                                label: "Tailles de Police",
                                impact: seoReport?.mobile_optimization
                                  ?.fontSizes
                                  ? "Texte lisible sans zoom sur les appareils mobiles"
                                  : "Certains textes sont trop petits pour être lus confortablement sur mobile",
                              },
                            ],
                            technicalDetails: [
                              {
                                parameter: "Design Responsive",
                                value: seoReport?.mobile_optimization
                                  ?.responsiveDesign
                                  ? "Implémenté"
                                  : "Non implémenté",
                                impact:
                                  "Détermine si votre site s'adapte à toutes les tailles d'écran",
                                tooltip:
                                  "Un design responsive utilise des media queries CSS pour adapter la mise en page",
                              },
                              {
                                parameter: "Configuration Viewport",
                                value: seoReport?.mobile_optimization
                                  ?.viewportConfiguration
                                  ? "Configuré"
                                  : "Non défini",
                                impact:
                                  "Contrôle l'affichage initial et le zoom sur les appareils mobiles",
                                tooltip:
                                  "La balise meta viewport définit comment votre page s'affiche sur les appareils mobiles",
                              },
                              {
                                parameter: "Cibles Tactiles",
                                value: seoReport?.mobile_optimization
                                  ?.touchTargets
                                  ? "Optimisées"
                                  : "Non optimisées",
                                impact:
                                  "Affecte la facilité d'utilisation de votre site sur les appareils tactiles",
                                tooltip:
                                  "Les cibles tactiles doivent mesurer au moins 48x48 pixels avec un espacement suffisant",
                              },
                              {
                                parameter: "Tailles de Police",
                                value: seoReport?.mobile_optimization?.fontSizes
                                  ? "Lisibles"
                                  : "Trop petites",
                                impact:
                                  "Détermine si les utilisateurs peuvent lire votre contenu sans zoomer",
                                tooltip:
                                  "Google recommande une taille de police d'au moins 16px pour le texte principal",
                              },
                              {
                                parameter: "Usabilité Mobile",
                                value: seoReport?.mobile_optimization
                                  ?.mobileUsability
                                  ? "Bonne"
                                  : "À améliorer",
                                impact:
                                  "Affecte directement votre classement dans l'index mobile-first de Google",
                                tooltip:
                                  "L'usabilité mobile englobe tous les aspects de l'expérience sur appareils mobiles",
                              },
                            ],
                            actionReference: {
                              count: Object.values(
                                seoReport?.mobile_optimization || {}
                              ).filter((item) => item === false).length,
                              sectionName: "Mobile",
                              actionWord: "Optimiser",
                              targetId: "mobile-optimization-actions",
                            },
                          },
                          {
                            id: "seo-3",
                            title: "Performance",
                            icon: <Gauge className="w-4 h-4" />,
                            score: seoReport?.performance_score,
                            useUnifiedModel: true,
                            impactStatement: {
                              text: "La performance de votre site web est un facteur de classement crucial pour Google et a un impact direct sur l'expérience utilisateur. Un site lent peut augmenter le taux de rebond de 32% et réduire les conversions de 20%.",
                              positivePoints: [
                                parseInt(
                                  seoReport?.performance_metrics
                                    ?.firstContentfulPaint || "0"
                                ) < 2000
                                  ? "Premier affichage rapide (< 2s)"
                                  : "",
                                parseInt(
                                  seoReport?.performance_metrics
                                    ?.responseTime || "0"
                                ) < 500
                                  ? "Temps de réponse serveur rapide (< 500ms)"
                                  : "",
                                seoReport?.performance_metrics
                                  ?.compressionRatio !== "Unknown"
                                  ? "Compression des ressources activée"
                                  : "",
                                // Add a generic positive point if score is high but no specific positive points
                                (seoReport?.performance_score || 0) >= 80 &&
                                ![
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.firstContentfulPaint || "0"
                                  ) < 2000,
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.responseTime || "0"
                                  ) < 500,
                                  seoReport?.performance_metrics
                                    ?.compressionRatio !== "Unknown",
                                ].some(Boolean)
                                  ? "Votre site offre globalement de bonnes performances de chargement"
                                  : "",
                              ].filter(Boolean),
                              negativePoints: [
                                parseInt(
                                  seoReport?.performance_metrics
                                    ?.largestContentfulPaint || "0"
                                ) > 2500
                                  ? "Affichage du contenu principal lent (> 2.5s)"
                                  : "",
                                parseInt(
                                  seoReport?.performance_metrics?.pageSize ||
                                    "0"
                                ) > 1500
                                  ? "Page trop volumineuse"
                                  : "",
                                parseInt(
                                  seoReport?.performance_metrics
                                    ?.timeToInteractive || "0"
                                ) > 3800
                                  ? "Temps d'interactivité trop long (> 3.8s)"
                                  : "",
                              ].filter(Boolean),
                            },
                            findings: [
                              {
                                status:
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.firstContentfulPaint || "0"
                                  ) < 2000
                                    ? "success"
                                    : "error",
                                label: "Premier Affichage (FCP)",
                                impact:
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.firstContentfulPaint || "0"
                                  ) < 2000
                                    ? `Rapide (${seoReport?.performance_metrics?.firstContentfulPaint}) - Bonne première impression`
                                    : `Lent (${seoReport?.performance_metrics?.firstContentfulPaint}) - Risque d'abandon des visiteurs`,
                              },
                              {
                                status:
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.largestContentfulPaint || "0"
                                  ) < 2500
                                    ? "success"
                                    : "error",
                                label: "Affichage Principal (LCP)",
                                impact:
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.largestContentfulPaint || "0"
                                  ) < 2500
                                    ? `Rapide (${seoReport?.performance_metrics?.largestContentfulPaint}) - Contenu principal visible rapidement`
                                    : `Lent (${seoReport?.performance_metrics?.largestContentfulPaint}) - Contenu principal trop lent à s'afficher`,
                              },
                              {
                                status:
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.responseTime || "0"
                                  ) < 500
                                    ? "success"
                                    : "warning",
                                label: "Temps de Réponse Serveur (TTFB)",
                                impact:
                                  parseInt(
                                    seoReport?.performance_metrics
                                      ?.responseTime || "0"
                                  ) < 500
                                    ? `Rapide (${seoReport?.performance_metrics?.responseTime}) - Serveur réactif`
                                    : `Lent (${seoReport?.performance_metrics?.responseTime}) - Serveur peu réactif`,
                              },
                              {
                                status:
                                  parseInt(
                                    seoReport?.performance_metrics?.pageSize ||
                                      "0"
                                  ) < 1500
                                    ? "success"
                                    : "warning",
                                label: "Taille de la Page",
                                impact:
                                  parseInt(
                                    seoReport?.performance_metrics?.pageSize ||
                                      "0"
                                  ) < 1500
                                    ? `Optimale (${seoReport?.performance_metrics?.pageSize})`
                                    : `Excessive (${seoReport?.performance_metrics?.pageSize}) - Ralentit le chargement`,
                              },
                            ],
                            technicalDetails: [
                              {
                                parameter: "Premier Affichage (FCP)",
                                value:
                                  seoReport?.performance_metrics
                                    ?.firstContentfulPaint || "Inconnu",
                                impact:
                                  "Mesure le temps nécessaire pour afficher le premier contenu visible",
                                tooltip:
                                  "Un FCP rapide (< 2s) est essentiel pour une bonne expérience utilisateur",
                              },
                              {
                                parameter: "Affichage Principal (LCP)",
                                value:
                                  seoReport?.performance_metrics
                                    ?.largestContentfulPaint || "Inconnu",
                                impact:
                                  "Mesure le temps nécessaire pour afficher le contenu principal",
                                tooltip:
                                  "Un LCP rapide (< 2.5s) est un facteur de classement pour Google",
                              },
                              {
                                parameter: "Temps de Réponse Serveur",
                                value:
                                  seoReport?.performance_metrics
                                    ?.responseTime || "Inconnu",
                                impact:
                                  "Mesure la réactivité de votre serveur web",
                                tooltip:
                                  "Un TTFB rapide (< 500ms) est essentiel pour de bonnes performances",
                              },
                              {
                                parameter: "Taille de la Page",
                                value:
                                  seoReport?.performance_metrics?.pageSize ||
                                  "Inconnue",
                                impact:
                                  "Affecte le temps de chargement, surtout sur les connexions lentes",
                                tooltip:
                                  "Une page légère (< 1MB) se charge plus rapidement",
                              },
                              {
                                parameter: "Temps d'Interactivité",
                                value:
                                  seoReport?.performance_metrics
                                    ?.timeToInteractive || "Inconnu",
                                impact:
                                  "Mesure le temps nécessaire pour que la page devienne interactive",
                                tooltip:
                                  "Un TTI rapide (< 3.8s) améliore l'expérience utilisateur",
                              },
                              {
                                parameter: "Compression",
                                value:
                                  seoReport?.performance_metrics
                                    ?.compressionRatio !== "Unknown"
                                    ? "Activée"
                                    : "Non activée",
                                impact:
                                  "Réduit la taille des fichiers transmis",
                                tooltip:
                                  "La compression peut réduire la taille des fichiers de 70%",
                              },
                            ],
                            actionReference: {
                              count: [
                                parseInt(
                                  seoReport?.performance_metrics
                                    ?.firstContentfulPaint || "0"
                                ) > 2000
                                  ? 1
                                  : 0,
                                parseInt(
                                  seoReport?.performance_metrics
                                    ?.largestContentfulPaint || "0"
                                ) > 2500
                                  ? 1
                                  : 0,
                                parseInt(
                                  seoReport?.performance_metrics
                                    ?.responseTime || "0"
                                ) > 500
                                  ? 1
                                  : 0,
                                parseInt(
                                  seoReport?.performance_metrics?.pageSize ||
                                    "0"
                                ) > 1500
                                  ? 1
                                  : 0,
                                seoReport?.performance_metrics
                                  ?.compressionRatio === "Unknown"
                                  ? 1
                                  : 0,
                              ].reduce((a, b) => a + b, 0),
                              sectionName: "Performance",
                              actionWord: "Optimiser",
                              targetId: "performance-actions",
                            },
                          },
                          {
                            id: "seo-4",
                            title: "Content",
                            icon: <FileText className="w-4 h-4" />,
                            score: seoReport?.content_score,
                            useUnifiedModel: true,
                            impactStatement: {
                              text: seoReport?.content_analysis
                                ? "Le contenu est le pilier de votre stratégie SEO. Un contenu de qualité, bien structuré et optimisé pour les mots-clés pertinents améliore votre visibilité dans les moteurs de recherche et engage vos visiteurs."
                                : "L'analyse détaillée du contenu est une fonctionnalité premium. Passez à un compte premium pour obtenir une analyse complète de votre contenu, incluant le score de lisibilité, la densité des mots-clés et la structure des titres.",
                              positivePoints: seoReport?.content_analysis
                                ? [
                                    (seoReport.content_analysis.wordCount ||
                                      0) > 300
                                      ? "Contenu suffisamment développé (> 300 mots)"
                                      : "",
                                    seoReport.content_analysis.headingsStructure
                                      ?.headingsInOrder
                                      ? "Structure de titres bien organisée"
                                      : "",
                                    seoReport.content_analysis
                                      .readabilityScore !== undefined &&
                                    seoReport.content_analysis
                                      .readabilityScore > 60
                                      ? "Bonne lisibilité du contenu"
                                      : "",
                                    // Add a generic positive point if score is high but no specific positive points
                                    (seoReport?.content_score || 0) >= 80 &&
                                    ![
                                      (seoReport.content_analysis.wordCount ||
                                        0) > 300,
                                      seoReport.content_analysis
                                        .headingsStructure?.headingsInOrder,
                                      seoReport.content_analysis
                                        .readabilityScore !== undefined &&
                                        seoReport.content_analysis
                                          .readabilityScore > 60,
                                    ].some(Boolean)
                                      ? "Votre contenu est globalement bien optimisé pour le référencement"
                                      : "",
                                  ].filter(Boolean)
                                : [],
                              negativePoints: seoReport?.content_analysis
                                ? [
                                    (seoReport.content_analysis.wordCount ||
                                      0) < 300
                                      ? "Contenu trop court (< 300 mots)"
                                      : "",
                                    !seoReport.content_analysis
                                      .headingsStructure?.headingsInOrder
                                      ? "Structure de titres désorganisée"
                                      : "",
                                    seoReport.content_analysis
                                      .readabilityScore !== undefined &&
                                    seoReport.content_analysis
                                      .readabilityScore < 60
                                      ? "Lisibilité du contenu à améliorer"
                                      : "",
                                  ].filter(Boolean)
                                : [
                                    "Analyse de contenu non disponible - Fonctionnalité premium",
                                  ],
                            },
                            findings: seoReport?.content_analysis
                              ? [
                                  {
                                    status:
                                      (seoReport.content_analysis.wordCount ||
                                        0) > 300
                                        ? "success"
                                        : "error",
                                    label: "Longueur du Contenu",
                                    impact:
                                      (seoReport.content_analysis.wordCount ||
                                        0) > 300
                                        ? `${seoReport.content_analysis.wordCount} mots - Contenu suffisamment développé`
                                        : `${seoReport.content_analysis.wordCount} mots - Contenu trop court pour être bien référencé`,
                                  },
                                  {
                                    status: seoReport?.content_analysis
                                      ?.headingsStructure?.headingsInOrder
                                      ? "success"
                                      : "warning",
                                    label: "Structure des Titres",
                                    impact: seoReport?.content_analysis
                                      ?.headingsStructure?.headingsInOrder
                                      ? "Hiérarchie des titres correcte (H1 → H2 → H3)"
                                      : "Hiérarchie des titres incorrecte (niveaux manquants ou désordonnés)",
                                  },
                                  {
                                    status:
                                      seoReport?.content_analysis
                                        ?.readabilityScore !== undefined &&
                                      seoReport.content_analysis
                                        .readabilityScore > 60
                                        ? "success"
                                        : "warning",
                                    label: "Lisibilité",
                                    impact:
                                      seoReport?.content_analysis
                                        ?.readabilityScore !== undefined
                                        ? seoReport.content_analysis
                                            .readabilityScore > 60
                                          ? `Score de lisibilité: ${seoReport.content_analysis.readabilityScore}/100 - Facile à lire`
                                          : `Score de lisibilité: ${seoReport.content_analysis.readabilityScore}/100 - Difficile à lire`
                                        : "Score de lisibilité: 0/100 - Difficile à lire",
                                  },
                                  {
                                    status:
                                      seoReport?.content_analysis
                                        ?.contentQualityScore &&
                                      seoReport?.content_analysis
                                        ?.contentQualityScore > 60
                                        ? "success"
                                        : "warning",
                                    label: "Qualité du Contenu",
                                    impact:
                                      seoReport?.content_analysis
                                        ?.contentQualityScore &&
                                      seoReport?.content_analysis
                                        ?.contentQualityScore > 60
                                        ? "Contenu de bonne qualité - Valeur ajoutée pour les visiteurs"
                                        : "Qualité du contenu à améliorer - Manque de valeur ajoutée",
                                  },
                                ]
                              : [
                                  {
                                    status: "warning",
                                    label: "Analyse de contenu",
                                    impact:
                                      "Fonctionnalité premium requise pour l'analyse détaillée du contenu",
                                  },
                                ],
                            technicalDetails: (() => {
                              return seoReport?.content_analysis;
                            })()
                              ? [
                                  {
                                    parameter: "Nombre de mots",
                                    value: `${
                                      seoReport.content_analysis.wordCount || 0
                                    }`,
                                    impact:
                                      "Détermine la profondeur du contenu et son potentiel de classement",
                                    tooltip:
                                      "Google favorise généralement les contenus détaillés (> 300 mots)",
                                  },
                                  {
                                    parameter: "Titres H1",
                                    value: `${
                                      seoReport?.content_analysis
                                        ?.headingsStructure?.h1Count || 0
                                    }`,
                                    impact:
                                      "Définit le sujet principal de la page",
                                    tooltip:
                                      "Idéalement, une page ne devrait avoir qu'un seul titre H1",
                                  },
                                  {
                                    parameter: "Titres H2",
                                    value: `${
                                      seoReport?.content_analysis
                                        ?.headingsStructure?.h2Count || 0
                                    }`,
                                    impact:
                                      "Structure les sections principales du contenu",
                                    tooltip:
                                      "Les titres H2 devraient contenir des mots-clés secondaires",
                                  },
                                  {
                                    parameter: "Score de lisibilité",
                                    value:
                                      seoReport?.content_analysis
                                        ?.readabilityScore !== undefined
                                        ? `${seoReport.content_analysis.readabilityScore}/100`
                                        : "0/100",
                                    impact:
                                      "Mesure la facilité de lecture du contenu",
                                    tooltip:
                                      "Un score élevé (> 60) indique un contenu facile à comprendre",
                                  },
                                  {
                                    parameter: "Densité des mots-clés",
                                    value: Object.entries(
                                      seoReport?.content_analysis
                                        ?.keywordDensity || {}
                                    )
                                      .slice(0, 3)
                                      .map(
                                        ([keyword, density]) =>
                                          `${keyword}: ${(
                                            Number(density) * 100
                                          ).toFixed(1)}%`
                                      )
                                      .join(", "),
                                    impact:
                                      "Indique si les mots-clés sont utilisés naturellement",
                                    tooltip:
                                      "Une densité idéale se situe entre 1% et 3% pour les mots-clés principaux",
                                  },
                                ]
                              : [
                                  {
                                    parameter: "Analyse de contenu",
                                    value: "Non disponible",
                                    impact: "Fonctionnalité premium requise",
                                    tooltip:
                                      "Passez à un compte premium pour accéder à l'analyse détaillée du contenu",
                                  },
                                ],
                            actionReference: {
                              count: [
                                (seoReport?.content_analysis?.wordCount || 0) <
                                300
                                  ? 1
                                  : 0,
                                !seoReport?.content_analysis?.headingsStructure
                                  ?.headingsInOrder
                                  ? 1
                                  : 0,
                                (seoReport?.content_analysis
                                  ?.readabilityScore || 0) < 60
                                  ? 1
                                  : 0,
                                (seoReport?.content_analysis
                                  ?.contentQualityScore || 0) < 60
                                  ? 1
                                  : 0,
                              ].reduce((a, b) => a + b, 0),
                              sectionName: "Contenu",
                              actionWord: "Optimiser",
                              targetId: "content-actions",
                            },
                          },
                          {
                            id: "seo-5",
                            title: "Image Optimization",
                            icon: <ImageIcon className="w-4 h-4" />,
                            score: seoReport?.image_score,
                            useUnifiedModel: true,
                            impactStatement: {
                              text: "Les images représentent souvent la majorité du poids d'une page web. Leur optimisation est cruciale pour améliorer la vitesse de chargement, l'expérience utilisateur et le référencement de votre site.",
                              positivePoints: [
                                seoReport?.image_analysis?.imagesWithAlt ===
                                  seoReport?.image_analysis?.totalImages &&
                                seoReport?.image_analysis?.totalImages > 0
                                  ? "Toutes les images ont des attributs alt"
                                  : "",
                                seoReport?.image_analysis?.imagesCompressed ===
                                  seoReport?.image_analysis?.totalImages &&
                                seoReport?.image_analysis?.totalImages > 0
                                  ? "Toutes les images sont compressées"
                                  : "",
                                seoReport?.image_analysis
                                  ?.imagesWithLazyLoading > 0
                                  ? "Chargement paresseux activé pour certaines images"
                                  : "",
                                // Add a generic positive point if score is high but no specific positive points
                                (seoReport?.image_score || 0) >= 80 &&
                                ![
                                  seoReport?.image_analysis?.imagesWithAlt ===
                                    seoReport?.image_analysis?.totalImages &&
                                    seoReport?.image_analysis?.totalImages > 0,
                                  seoReport?.image_analysis
                                    ?.imagesCompressed ===
                                    seoReport?.image_analysis?.totalImages &&
                                    seoReport?.image_analysis?.totalImages > 0,
                                  seoReport?.image_analysis
                                    ?.imagesWithLazyLoading > 0,
                                ].some(Boolean)
                                  ? "Vos images sont globalement bien optimisées pour le référencement"
                                  : "",
                              ].filter(Boolean),
                              negativePoints: [
                                seoReport?.image_analysis?.imagesWithoutAlt > 0
                                  ? `${seoReport?.image_analysis?.imagesWithoutAlt} image(s) sans attribut alt`
                                  : "",
                                seoReport?.image_analysis?.imagesUncompressed >
                                0
                                  ? `${seoReport?.image_analysis?.imagesUncompressed} image(s) non compressées`
                                  : "",
                                seoReport?.image_analysis?.largeImages?.length >
                                0
                                  ? `${seoReport?.image_analysis?.largeImages.length} image(s) trop volumineuses`
                                  : "",
                              ].filter(Boolean),
                            },
                            findings: [
                              {
                                status:
                                  seoReport?.image_analysis
                                    ?.imagesWithoutAlt === 0
                                    ? "success"
                                    : "error",
                                label: "Attributs Alt",
                                impact:
                                  seoReport?.image_analysis
                                    ?.imagesWithoutAlt === 0
                                    ? "Toutes les images ont des attributs alt - Bon pour l'accessibilité et le SEO"
                                    : `${seoReport?.image_analysis?.imagesWithoutAlt} image(s) sans attribut alt - Mauvais pour l'accessibilité et le SEO`,
                              },
                              {
                                status:
                                  seoReport?.image_analysis
                                    ?.imagesUncompressed === 0
                                    ? "success"
                                    : "warning",
                                label: "Compression",
                                impact:
                                  seoReport?.image_analysis
                                    ?.imagesUncompressed === 0
                                    ? "Toutes les images sont compressées - Chargement rapide"
                                    : `${seoReport?.image_analysis?.imagesUncompressed} image(s) non compressées - Ralentissement du chargement`,
                              },
                              {
                                status:
                                  seoReport?.image_analysis?.largeImages
                                    ?.length === 0
                                    ? "success"
                                    : "warning",
                                label: "Taille des Images",
                                impact:
                                  seoReport?.image_analysis?.largeImages
                                    ?.length === 0
                                    ? "Toutes les images ont une taille appropriée"
                                    : `${seoReport?.image_analysis?.largeImages.length} image(s) trop volumineuses - Ralentissement du chargement`,
                              },
                              {
                                status:
                                  seoReport?.image_analysis
                                    ?.imagesWithLazyLoading > 0
                                    ? "success"
                                    : "warning",
                                label: "Chargement Paresseux",
                                impact:
                                  seoReport?.image_analysis
                                    ?.imagesWithLazyLoading > 0
                                    ? `${seoReport?.image_analysis?.imagesWithLazyLoading} image(s) avec chargement paresseux - Améliore les performances`
                                    : "Recommandation: Ajouter le chargement paresseux pour améliorer les performances",
                              },
                            ],
                            technicalDetails: [
                              {
                                parameter: "Nombre total d'images",
                                value: `${
                                  seoReport?.image_analysis?.totalImages || 0
                                }`,
                                impact:
                                  "Détermine la charge globale des images sur la page",
                                tooltip:
                                  "Un grand nombre d'images peut ralentir le chargement de la page",
                              },
                              {
                                parameter: "Images avec attribut alt",
                                value: `${
                                  seoReport?.image_analysis?.imagesWithAlt || 0
                                } / ${
                                  seoReport?.image_analysis?.totalImages || 0
                                }`,
                                impact:
                                  "Essentiel pour l'accessibilité et le référencement",
                                tooltip:
                                  "Les attributs alt décrivent l'image pour les lecteurs d'écran et les moteurs de recherche",
                              },
                              {
                                parameter: "Images compressées",
                                value: `${
                                  seoReport?.image_analysis?.imagesCompressed ||
                                  0
                                } / ${
                                  seoReport?.image_analysis?.totalImages || 0
                                }`,
                                impact:
                                  "Réduit le temps de chargement de la page",
                                tooltip:
                                  "La compression peut réduire la taille des images de 50-90% sans perte visible de qualité",
                              },
                              {
                                parameter: "Images avec chargement paresseux",
                                value: `${
                                  seoReport?.image_analysis
                                    ?.imagesWithLazyLoading || 0
                                } / ${
                                  seoReport?.image_analysis?.totalImages || 0
                                }`,
                                impact:
                                  "Améliore les performances perçues de la page",
                                tooltip:
                                  "Le chargement paresseux charge les images uniquement lorsqu'elles deviennent visibles",
                              },
                              {
                                parameter: "Images trop volumineuses",
                                value: `${
                                  seoReport?.image_analysis?.largeImages
                                    ?.length || 0
                                }`,
                                impact:
                                  "Ralentit considérablement le chargement de la page",
                                tooltip:
                                  "Les images doivent être redimensionnées à la taille d'affichage maximale requise",
                              },
                            ],
                            actionReference: {
                              count:
                                (seoReport?.image_analysis?.imagesWithoutAlt ||
                                  0) +
                                (seoReport?.image_analysis
                                  ?.imagesUncompressed || 0) +
                                (seoReport?.image_analysis?.largeImages
                                  ?.length || 0) +
                                (seoReport?.image_analysis
                                  ?.imagesWithLazyLoading === 0 &&
                                seoReport?.image_analysis?.totalImages > 0
                                  ? 1
                                  : 0),
                              sectionName: "Images",
                              actionWord: "Optimiser",
                              targetId: "image-optimization-actions",
                            },
                          },
                        ]}
                      />
                    </div>
                  </div>
                </div>
              </AdvancedSection>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
