import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { safeStorage } from "../utils/storage";
import Layout from "@/components/Layout";
import Card from "@/components/ui-custom/Card";
import Input from "@/components/ui-custom/Input";
import Button from "@/components/ui-custom/Button";
import { toast } from "sonner";
import axios from "axios";
import { PasswordCriteriaList } from "../components/PasswordCriteriaList";

const API_BASE_URL = "http://localhost:5012";

export default function ResetPassword() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordCriteria, setPasswordCriteria] = useState({
    length: false,
    uppercase: false,
    number: false,
    specialChar: false,
  });
  const [loading, setLoading] = useState(false);
  const [email, setEmail] = useState("");
  const navigate = useNavigate();

  const updatePasswordCriteria = (password: string) => {
    setPasswordCriteria({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
      specialChar: /[!@#$%^&*]/.test(password),
    });
  };

  useEffect(() => {
    const fetchEmail = async () => {
      // Get email from safeStorage
      const storedEmail = await safeStorage.getItem("resetEmail");
      if (!storedEmail) {
        toast.error(
          "Adresse email manquante. Veuillez recommencer le processus."
        );
        navigate("/forgot-password");
        return;
      }
      setEmail(storedEmail);
    };
    fetchEmail();
  }, [navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!password || !confirmPassword) {
      toast.error("Veuillez remplir tous les champs");
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Les mots de passe ne correspondent pas");
      return;
    }

    if (!Object.values(passwordCriteria).every(Boolean)) {
      toast.error("Le mot de passe ne répond pas aux critères de sécurité");
      return;
    }

    setLoading(true);

    try {
      // API call to reset password
      await axios.post(`${API_BASE_URL}/api/auth/reset-password`, {
        email: email,
        newPassword: password,
        confirmNewPassword: confirmPassword,
      });

      // Clear stored email after successful reset
      await safeStorage.removeItem("resetEmail");

      toast.success("Mot de passe réinitialisé avec succès");
      navigate("/success-reset");
    } catch (error: any) {
      toast.error(
        error.response?.data?.message ||
          "Erreur lors de la réinitialisation du mot de passe."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout showNav={true} navType="second">
      <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
        <Card className="w-full max-w-md animate-scale-in">
          <h1 className="text-2xl font-bold text-center text-gray-800 mb-6">
            Nouveau mot de passe
          </h1>

          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              type="password"
              placeholder="Nouveau mot de passe"
              icon="password"
              value={password}
              onChange={(e) => {
                setPassword(e.target.value);
                updatePasswordCriteria(e.target.value);
              }}
              required
            />
            <PasswordCriteriaList
              criteria={passwordCriteria}
              password={password}
            />

            <Input
              type="password"
              placeholder="Confirmer le mot de passe"
              icon="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />

            <Button type="submit" className="w-full mt-2" loading={loading}>
              Réinitialiser le mot de passe
            </Button>
          </form>

          <div className="mt-6 text-center">
            <Link
              to="/login"
              className="text-sm text-sitechecker-blue hover:underline"
            >
              Retour à la connexion
            </Link>
          </div>
        </Card>
      </div>
    </Layout>
  );
}
