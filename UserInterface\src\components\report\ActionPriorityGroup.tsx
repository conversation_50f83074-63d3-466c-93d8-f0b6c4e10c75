import { useState } from "react";
import { ReportAction } from "@/types/report";
import { useTranslation } from "react-i18next";
import {
  ChevronRight,
  Shield,
  Search,
  AlertTriangle,
  AlertCircle,
  Info,
  Zap,
  Clock,
  BarChart3,
} from "lucide-react";
import ActionItem from "./ActionItem";

interface ActionPriorityGroupProps {
  title: string;
  actions: ReportAction[];
  color: "security" | "seo";
  priority?: 1 | 2 | 3;
  defaultOpen?: boolean;
}

const neonColors = {
  security: {
    primary: "#1eaedb", // Neon blue - primary color for security
    secondary: "#1eaedb", // Neon blue - secondary color for security
    cardBg: "bg-[#a9cbff]", // Light blue background
    cardBorder: "border-[#1eaedb]/30", // Matching border with primary color
    glow: "shadow-[0_0_15px_rgba(30,174,219,0.5)]", // Glow effect matching primary
    iconGlow: "drop-shadow-[0_0_8px_rgba(30,174,219,0.7)]", // Icon glow
    titleGlow: "text-shadow-[0_0_8px_rgba(30,174,219,0.8)]", // Title glow
    icon: "text-[#1eaedb]", // Icon color matching primary
    title: "text-white", // White text for contrast
    headerBg: "bg-gradient-to-br from-neon-purple to-neon-blue", // Gradient header using Tailwind classes
    itemBg: "bg-gradient-to-br from-[#a9cbff]/40 to-[#e8efff]/60", // Gradient background matching ScoreCard
    actionTitle: "text-[#1eaedb]", // Action title color matching primary
    actionBorder: "border-[#1eaedb]/30", // Border color for actions
    neonEffect:
      "shadow-[0_0_10px_rgba(30,174,219,0.4),0_0_20px_rgba(30,174,219,0.3)]", // Enhanced dual glow effect
  },
  seo: {
    primary: "#d946ef", // Neon magenta - primary color for SEO
    secondary: "#1eaedb", // Neon blue - secondary color for SEO
    cardBg: "bg-[#d1c6f5]", // Light purple background
    cardBorder: "border-[#d946ef]/30", // Matching border with primary color
    glow: "shadow-[0_0_15px_rgba(217,70,239,0.5)]", // Glow effect matching primary
    iconGlow: "drop-shadow-[0_0_8px_rgba(217,70,239,0.7)]", // Icon glow
    titleGlow: "text-shadow-[0_0_8px_rgba(217,70,239,0.8)]", // Title glow
    icon: "text-[#d946ef]", // Icon color matching primary
    title: "text-white", // White text for contrast
    headerBg: "bg-gradient-to-br from-neon-magenta to-neon-blue", // Gradient header using Tailwind classes
    itemBg: "bg-gradient-to-br from-[#d1c6f5]/40 to-[#f5e8ff]/60", // Gradient background matching ScoreCard
    actionTitle: "text-[#d946ef]", // Action title color matching primary
    actionBorder: "border-[#d946ef]/30", // Border color for actions
    neonEffect:
      "shadow-[0_0_10px_rgba(217,70,239,0.4),0_0_20px_rgba(30,174,219,0.3)]", // Enhanced dual glow effect
  },
};

const ActionPriorityGroup = ({
  title,
  actions,
  color,
  priority,
  defaultOpen = false,
}: ActionPriorityGroupProps) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [showAll, setShowAll] = useState(false);
  const colors = neonColors[color];

  // Get appropriate icon based on difficulty
  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return <Zap className="w-2 h-2 mr-0.5 inline-block text-gray-500" />;
      case "medium":
        return <Clock className="w-2 h-2 mr-0.5 inline-block text-gray-500" />;
      case "hard":
        return (
          <BarChart3 className="w-2 h-2 mr-0.5 inline-block text-gray-500" />
        );
      default:
        return null;
    }
  };

  // Determine security section based on action title
  const getSecuritySection = (title: string) => {
    const titleLower = title.toLowerCase();

    if (
      titleLower.includes("ssl") ||
      titleLower.includes("tls") ||
      titleLower.includes("certificate")
    ) {
      return "SSL/TLS";
    } else if (
      titleLower.includes("header") ||
      titleLower.includes("hsts") ||
      titleLower.includes("csp")
    ) {
      return t("security.headers");
    } else if (
      titleLower.includes("vulnerability") ||
      titleLower.includes("injection") ||
      titleLower.includes("xss") ||
      titleLower.includes("csrf") ||
      titleLower.includes("sql") ||
      titleLower.includes("attack")
    ) {
      return t("security.vulnerabilities");
    } else if (
      titleLower.includes("javascript") ||
      titleLower.includes("js") ||
      titleLower.includes("library") ||
      titleLower.includes("npm")
    ) {
      return t("security.javascript");
    } else if (
      titleLower.includes("api") ||
      titleLower.includes("endpoint") ||
      titleLower.includes("authentication") ||
      titleLower.includes("rate limit")
    ) {
      return t("security.api");
    } else {
      return t("security.title");
    }
  };

  // Determine SEO section based on action title
  const getSeoSection = (title: string) => {
    const titleLower = title.toLowerCase();

    if (
      titleLower.includes("meta") ||
      titleLower.includes("title") ||
      titleLower.includes("description") ||
      titleLower.includes("og:")
    ) {
      return t("seo.meta");
    } else if (
      titleLower.includes("mobile") ||
      titleLower.includes("responsive") ||
      titleLower.includes("viewport")
    ) {
      return t("seo.mobile");
    } else if (
      titleLower.includes("image") ||
      titleLower.includes("alt") ||
      titleLower.includes("compress") ||
      titleLower.includes("webp")
    ) {
      return t("seo.images");
    } else if (
      titleLower.includes("performance") ||
      titleLower.includes("speed") ||
      titleLower.includes("load") ||
      titleLower.includes("cache")
    ) {
      return t("seo.performance");
    } else if (
      titleLower.includes("content") ||
      titleLower.includes("text") ||
      titleLower.includes("keyword") ||
      titleLower.includes("heading")
    ) {
      return t("seo.content");
    } else {
      return t("seo.title");
    }
  };

  return (
    <div
      className="rounded-xl overflow-hidden transition-all mb-4 shadow-sm bg-white border border-gray-200"
      style={{
        borderLeft:
          priority === 3
            ? "4px solid #ef4444" // red-500
            : priority === 2
            ? "4px solid #f59e0b" // amber-500
            : "4px solid #10b981", // emerald-500
        boxShadow:
          priority === 3
            ? "0 4px 14px rgba(239, 68, 68, 0.15)"
            : priority === 2
            ? "0 4px 14px rgba(245, 158, 11, 0.15)"
            : "0 4px 14px rgba(16, 185, 129, 0.15)",
      }}
    >
      <button
        className={`w-full flex items-center justify-between p-3 bg-[#FAFAFA] text-gray-800 transition-all duration-300 group hover:bg-gray-50 rounded-t-xl`}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-2">
            <div
              className={`flex-shrink-0 p-1 rounded-md ${
                priority === 3
                  ? "bg-red-100"
                  : priority === 2
                  ? "bg-amber-100"
                  : "bg-green-100"
              }`}
            >
              {priority === 3 ? (
                <AlertTriangle className="w-4 h-4 text-red-500" />
              ) : priority === 2 ? (
                <AlertCircle className="w-4 h-4 text-amber-500" />
              ) : (
                <Info className="w-4 h-4 text-green-500" />
              )}
            </div>
            <h3 className="text-base font-semibold text-gray-800 group-hover:scale-[1.01] transition-transform">
              {title}
              <span
                className={`ml-2 px-1.5 py-0.5 rounded-full text-xs font-medium ${
                  priority === 3
                    ? "bg-red-100 text-red-700"
                    : priority === 2
                    ? "bg-amber-100 text-amber-700"
                    : "bg-green-100 text-green-700"
                }`}
              >
                {actions.length}
              </span>
            </h3>
          </div>
          <div className="flex items-center gap-2">
            <span
              className={`text-xs font-medium px-2 py-1 rounded-md ${
                priority === 3
                  ? "bg-red-100 text-red-700"
                  : priority === 2
                  ? "bg-amber-100 text-amber-700"
                  : "bg-green-100 text-green-700"
              }`}
            >
              {priority === 3
                ? t("priority.high")
                : priority === 2
                ? t("priority.medium")
                : t("priority.low")}
            </span>
            <ChevronRight
              className={`h-4 w-4 text-gray-500 transition-all duration-200 group-hover:text-gray-700 ${
                isOpen ? "transform rotate-90" : ""
              }`}
            />
          </div>
        </div>
      </button>

      {isOpen && (
        <div className="p-3">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {(showAll ? actions : actions.slice(0, 3)).map((action, index) => {
              // Determine if this is a security action
              const isSecurityAction =
                action.type === "security" ||
                action.type === "SSL/TLS" ||
                action.type === "HTTP Headers" ||
                action.type === "Vulnerabilities" ||
                action.type === "JavaScript Security" ||
                action.type === "API Security";

              // Helper function to get the appropriate section name
              const getSectionName = (title: string, type: string) => {
                if (isSecurityAction) {
                  return type === "security" ? getSecuritySection(title) : type;
                } else {
                  return type === "seo"
                    ? getSeoSection(title)
                    : type === "meta_tags"
                    ? t("seo.meta")
                    : type === "mobile_optimization"
                    ? t("seo.mobile")
                    : type === "content"
                    ? t("seo.content")
                    : type === "url_structure"
                    ? t("seo.url")
                    : type === "images"
                    ? t("seo.images")
                    : type === "performance"
                    ? t("seo.performance")
                    : getSeoSection(title);
                }
              };

              return (
                <ActionItem
                  key={index}
                  action={action}
                  isSecurityAction={isSecurityAction}
                  getSectionName={getSectionName}
                  getDifficultyIcon={getDifficultyIcon}
                />
              );
            })}
          </div>

          {actions.length > 3 && (
            <div className="mt-2 flex justify-center">
              <button
                onClick={() => setShowAll(!showAll)}
                className="px-2 py-1 text-xs font-medium rounded transition-colors flex items-center gap-1 bg-gray-100 text-gray-700 hover:bg-gray-200"
              >
                {showAll ? (
                  <>
                    <span>{t("report.show_less")}</span>
                    <ChevronRight className="w-3 h-3 rotate-270" />
                  </>
                ) : (
                  <>
                    <span>+{actions.length - 3} de plus</span>
                    <ChevronRight className="w-3 h-3 rotate-90" />
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ActionPriorityGroup;
