using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using SiteCheckerApp.Models;
using SiteCheckerApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace SiteCheckerApp.Controllers
{
    [EnableCors("AllowFrontend")]
    [ApiController]
    [Route("api/[controller]")]
    public class ScanHistoryController : ControllerBase
    {
        private readonly IScanHistoryService _scanHistoryService;
        private readonly ILogger<ScanHistoryController> _logger;

        public ScanHistoryController(
            IScanHistoryService scanHistoryService,
            ILogger<ScanHistoryController> logger)
        {
            _scanHistoryService = scanHistoryService;
            _logger = logger;
        }

        [HttpPost]
        [Authorize]
        public async Task<IActionResult> RecordScan([FromBody] RecordScanRequest request)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out Guid userGuid))
                {
                    return Unauthorized(new { message = "User ID not found in token." });
                }

                var scanHistory = new ScanHistory
                {
                    Id = Guid.NewGuid(),
                    UserId = userGuid,
                    Url = request.Url,
                    ScanType = request.ScanType,
                    ScanDepth = request.ScanDepth,
                    Score = request.Score,
                    ScanDate = DateTime.UtcNow,
                    IsSuccessful = request.IsSuccessful,
                    ErrorMessage = request.ErrorMessage
                };

                await _scanHistoryService.AddScanHistoryAsync(scanHistory);
                return Ok(new { message = "Scan recorded successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error recording scan");
                return StatusCode(500, new { message = "An error occurred while recording the scan." });
            }
        }

        [HttpGet("recent")]
        [Authorize]
        public async Task<IActionResult> GetRecentScans([FromQuery] int limit = 5, [FromQuery] bool includeDetails = false)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out Guid userGuid))
                {
                    return Unauthorized(new { message = "Identifiant utilisateur non trouvé dans le jeton." });
                }

                var scanHistory = await _scanHistoryService.GetUserScanHistoryAsync(userGuid);

                // Group by URL and take the most recent scan of each type
                var groupedScans = scanHistory
                    .GroupBy(s => s.Url)
                    .Select(g => g.OrderByDescending(s => s.ScanDate).First())
                    .OrderByDescending(s => s.ScanDate)
                    .Take(limit)
                    .Select(s => new RecentScanResponse
                    {
                        Url = s.Url,
                        Timestamp = s.ScanDate.ToString("o"),
                        ScanDepth = s.ScanDepth,
                        ScanType = includeDetails ? s.ScanType : null,
                        Score = includeDetails ? s.Score : null,
                        IsSuccessful = s.IsSuccessful
                    })
                    .ToList();

                return Ok(groupedScans);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'historique des analyses récentes");
                return StatusCode(500, new { message = "Une erreur s'est produite lors de la récupération de l'historique des analyses récentes." });
            }
        }

        [HttpGet("premium/history")]
        [Authorize]
        public async Task<IActionResult> GetPremiumScanHistory([FromQuery] int limit = 50)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userRole = User.FindFirst(ClaimTypes.Role)?.Value;

                if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out Guid userGuid))
                {
                    return Unauthorized(new { message = "Identifiant utilisateur non trouvé dans le jeton." });
                }

                // Check if user is premium or admin
                if (userRole != "2" && userRole != "3")
                {
                    return Forbid("Cette fonctionnalité est réservée aux utilisateurs premium.");
                }

                var scanHistory = await _scanHistoryService.GetUserScanHistoryAsync(userGuid);

                // For premium users, return all scan history with full details
                var detailedScans = scanHistory
                    .OrderByDescending(s => s.ScanDate)
                    .Take(limit)
                    .Select(s => new RecentScanResponse
                    {
                        Url = s.Url,
                        Timestamp = s.ScanDate.ToString("o"),
                        ScanDepth = s.ScanDepth,
                        ScanType = s.ScanType,
                        Score = s.Score,
                        IsSuccessful = s.IsSuccessful
                    })
                    .ToList();

                return Ok(detailedScans);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'historique des analyses premium");
                return StatusCode(500, new { message = "Une erreur s'est produite lors de la récupération de l'historique des analyses premium." });
            }
        }
    }

    public class RecordScanRequest
    {
        public required string Url { get; set; }
        public required string ScanType { get; set; }
        public required string ScanDepth { get; set; }
        public int? Score { get; set; }
        public bool IsSuccessful { get; set; } = true;
        public string? ErrorMessage { get; set; }
    }

    public class RecentScanResponse
    {
        public required string Url { get; set; }
        public required string Timestamp { get; set; }
        public required string ScanDepth { get; set; }
        public string? ScanType { get; set; }
        public int? Score { get; set; }
        public bool IsSuccessful { get; set; } = true;
    }
}
