import React from "react";
import { AlertTriangle, Check, Globe, Server, HelpCircle } from "lucide-react";

// Section Introduction Component
const SectionIntro: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => {
  return (
    <div className="mb-4 p-3 neo-card bg-gradient-to-r from-gray-50 to-indigo-50 border-b border-indigo-100">
      <h3 className="text-indigo-700 font-medium mb-2 flex items-center">
        <Server className="h-4 w-4 mr-2" />
        {title}
      </h3>
      <p className="text-sm text-gray-700">{children}</p>
    </div>
  );
};

// Security Impact Component
const SecurityImpact: React.FC<{
  severity: "high" | "medium" | "low";
  title: string;
  children: React.ReactNode;
}> = ({ severity, title, children }) => {
  const bgColor =
    severity === "high"
      ? "bg-red-50 border-red-200"
      : severity === "medium"
      ? "bg-yellow-50 border-yellow-200"
      : "bg-blue-50 border-blue-200";

  const textColor =
    severity === "high"
      ? "text-red-700"
      : severity === "medium"
      ? "text-yellow-700"
      : "text-blue-700";

  const icon =
    severity === "high" ? (
      <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
    ) : severity === "medium" ? (
      <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />
    ) : (
      <HelpCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
    );

  return (
    <div className={`p-3 rounded-lg border ${bgColor} mb-4`}>
      <h4 className={`text-sm font-medium ${textColor} mb-2 flex items-center`}>
        {icon}
        <span className="ml-2">{title}</span>
      </h4>
      <p className="text-sm text-gray-700">{children}</p>
    </div>
  );
};

interface ApiEndpoint {
  url: string;
  method: string;
  authentication: boolean;
  rateLimit: boolean;
  inputValidation: boolean;
  securityHeaders: boolean;
  issues: {
    title: string;
    description: string;
    severity: "critical" | "high" | "medium" | "low";
    recommendation: string;
  }[];
}

interface ApiSecurityDetailsProps {
  endpoints?: ApiEndpoint[];
  authenticationIssues?: {
    title: string;
    description: string;
    severity: "critical" | "high" | "medium" | "low";
    recommendation: string;
  }[];
  overallScore?: number;
  apiDetected?: boolean;
}

const ApiSecurityDetails: React.FC<ApiSecurityDetailsProps> = ({
  endpoints = [],
  authenticationIssues = [],
  overallScore = 0,
  apiDetected = false,
}) => {
  // Helper function to get severity class
  const getSeverityClass = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case "critical":
        return "bg-red-100 text-red-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-green-100 text-green-800";
    }
  };

  // We can calculate issue counts if needed for UI components

  if (!apiDetected) {
    return (
      <div className="space-y-4">
        <SectionIntro title="Sécurité des API">
          La sécurité des API est cruciale pour protéger les données et
          fonctionnalités exposées par votre site web. Une API mal sécurisée
          peut permettre à des attaquants d'accéder à des données sensibles ou
          de manipuler votre application.
        </SectionIntro>
        <div className="p-4 neo-card bg-blue-50 flex items-center gap-3">
          <Globe className="h-6 w-6 text-blue-500" />
          <div>
            <h4 className="font-medium text-blue-800">Aucune API détectée</h4>
            <p className="text-sm text-blue-600 mt-1">
              Aucune API n'a été détectée sur votre site web ou l'analyse n'a
              pas pu être effectuée.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Part 1: Description of the section */}
      <SectionIntro title="Sécurité des API">
        Les API (Interfaces de Programmation d'Applications) sont comme des
        portes d'entrée vers les données et fonctionnalités de votre site.
        Chaque API mal sécurisée est une porte potentiellement ouverte pour les
        attaquants. Cette section analyse la sécurité des API détectées sur
        votre site.
      </SectionIntro>

      {/* Part 2: Impact of missing elements */}
      <SecurityImpact
        severity={
          overallScore < 50 ? "high" : overallScore < 80 ? "medium" : "low"
        }
        title="Impact sur la sécurité de votre site"
      >
        {overallScore < 50
          ? "Les failles de sécurité dans vos API représentent un risque élevé pour votre site. Des attaquants pourraient accéder à des données sensibles, manipuler votre application ou compromettre les comptes de vos utilisateurs. Une action immédiate est nécessaire."
          : overallScore < 80
          ? "Votre configuration API présente des vulnérabilités qui pourraient être exploitées. Bien que le risque ne soit pas critique, ces faiblesses pourraient permettre à des attaquants de compromettre certaines fonctionnalités de votre site."
          : "Votre configuration API est généralement sécurisée, mais quelques améliorations mineures pourraient encore renforcer la protection de vos données et fonctionnalités."}
      </SecurityImpact>

      {/* API Door Locks Diagram */}
      <div className="p-4 neo-card bg-gradient-to-r from-gray-50 to-indigo-50">
        <h4 className="font-medium text-gray-800 mb-3">
          État des Verrous de Sécurité API
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
            <div
              className={`text-xl ${
                authenticationIssues.length > 0
                  ? "text-red-500"
                  : "text-green-500"
              }`}
            >
              {authenticationIssues.length > 0 ? "🔓" : "🔐"}
            </div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                Authentification
              </h5>
              <p className="text-xs text-gray-600">
                {authenticationIssues.length > 0
                  ? "Problèmes d'authentification détectés"
                  : "Authentification correctement configurée"}
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
            <div
              className={`text-xl ${
                endpoints.some((e) => !e.rateLimit)
                  ? "text-red-500"
                  : "text-green-500"
              }`}
            >
              {endpoints.some((e) => !e.rateLimit) ? "🔓" : "🔐"}
            </div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                Limitation de Débit
              </h5>
              <p className="text-xs text-gray-600">
                {endpoints.some((e) => !e.rateLimit)
                  ? "Limitation de débit manquante sur certains endpoints"
                  : "Limitation de débit correctement configurée"}
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
            <div
              className={`text-xl ${
                endpoints.some((e) => !e.inputValidation)
                  ? "text-red-500"
                  : "text-green-500"
              }`}
            >
              {endpoints.some((e) => !e.inputValidation) ? "🔓" : "🔐"}
            </div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                Validation des Entrées
              </h5>
              <p className="text-xs text-gray-600">
                {endpoints.some((e) => !e.inputValidation)
                  ? "Validation des entrées insuffisante"
                  : "Validation des entrées correctement configurée"}
              </p>
            </div>
          </div>

          <div className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm">
            <div
              className={`text-xl ${
                endpoints.some((e) => !e.securityHeaders)
                  ? "text-red-500"
                  : "text-green-500"
              }`}
            >
              {endpoints.some((e) => !e.securityHeaders) ? "🔓" : "🔐"}
            </div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                En-têtes de Sécurité
              </h5>
              <p className="text-xs text-gray-600">
                {endpoints.some((e) => !e.securityHeaders)
                  ? "En-têtes de sécurité manquants"
                  : "En-têtes de sécurité correctement configurés"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* API Security Score */}
      <div className="p-4 neo-card bg-white">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-medium text-gray-800">Score de Sécurité API</h4>
          <div className="flex items-center">
            <div
              className={`w-16 h-16 rounded-full flex items-center justify-center text-white font-bold ${
                overallScore >= 80
                  ? "bg-green-500"
                  : overallScore >= 50
                  ? "bg-yellow-500"
                  : "bg-red-500"
              }`}
            >
              {overallScore}
            </div>
            <span className="ml-2 text-sm text-gray-600">/100</span>
          </div>
        </div>

        <div className="h-2 bg-gray-200 rounded-full overflow-hidden mb-4">
          <div
            className={`h-full ${
              overallScore >= 80
                ? "bg-green-500"
                : overallScore >= 50
                ? "bg-yellow-500"
                : "bg-red-500"
            }`}
            style={{ width: `${overallScore}%` }}
          ></div>
        </div>

        <p className="text-sm text-gray-600 mb-2">
          {overallScore >= 80
            ? "Bon niveau de sécurité API. Quelques améliorations mineures recommandées."
            : overallScore >= 50
            ? "Niveau de sécurité API moyen. Des améliorations importantes sont nécessaires."
            : "Niveau de sécurité API faible. Des mesures urgentes sont requises."}
        </p>
      </div>

      {/* Authentication Issues */}
      {authenticationIssues.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-800">
            Problèmes d'Authentification
          </h4>
          <div className="space-y-3">
            {authenticationIssues.map((issue, index) => (
              <div
                key={`auth-issue-${index}`}
                className={`p-4 neo-card border-l-4 ${
                  issue.severity === "critical"
                    ? "border-red-500 bg-red-50"
                    : issue.severity === "high"
                    ? "border-orange-500 bg-orange-50"
                    : issue.severity === "medium"
                    ? "border-yellow-500 bg-yellow-50"
                    : "border-blue-500 bg-blue-50"
                }`}
              >
                <div className="flex items-start gap-3">
                  <div
                    className={`text-2xl ${
                      issue.severity === "critical"
                        ? "text-red-500"
                        : issue.severity === "high"
                        ? "text-orange-500"
                        : issue.severity === "medium"
                        ? "text-yellow-500"
                        : "text-blue-500"
                    }`}
                  >
                    🔑
                  </div>
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-800 flex items-center">
                      {issue.title}
                      <span
                        className={`ml-2 text-xs px-2 py-0.5 rounded ${getSeverityClass(
                          issue.severity
                        )}`}
                      >
                        {issue.severity.toUpperCase()}
                      </span>
                    </h5>

                    <p className="text-sm text-gray-700 mt-2">
                      {issue.description}
                    </p>

                    <div className="mt-3 bg-white p-3 rounded-lg border border-gray-200">
                      <h6 className="text-xs font-medium text-gray-700">
                        Recommandation:
                      </h6>
                      <p className="text-sm text-gray-700 mt-1">
                        {issue.recommendation}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* API Endpoints */}
      {endpoints.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-800">
            Points d'Accès API ({endpoints.length})
          </h4>
          <div className="space-y-4">
            {endpoints.map((endpoint, index) => {
              const hasIssues = endpoint.issues.length > 0;
              const highestSeverity = hasIssues
                ? endpoint.issues.reduce((highest, issue) => {
                    const severityOrder = {
                      critical: 0,
                      high: 1,
                      medium: 2,
                      low: 3,
                    };
                    return severityOrder[issue.severity] <
                      severityOrder[highest]
                      ? issue.severity
                      : highest;
                  }, "low" as "critical" | "high" | "medium" | "low")
                : "low";

              return (
                <div
                  key={`endpoint-${index}`}
                  className={`p-4 neo-card ${
                    hasIssues
                      ? highestSeverity === "critical"
                        ? "border-l-4 border-red-500 bg-red-50"
                        : highestSeverity === "high"
                        ? "border-l-4 border-orange-500 bg-orange-50"
                        : highestSeverity === "medium"
                        ? "border-l-4 border-yellow-500 bg-yellow-50"
                        : "border-l-4 border-blue-500 bg-blue-50"
                      : "bg-white"
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h5 className="font-medium text-gray-800 flex items-center">
                        <span className="px-2 py-1 bg-gray-200 text-gray-800 rounded text-xs font-mono mr-2">
                          {endpoint.method}
                        </span>
                        <span className="font-mono text-sm">
                          {endpoint.url}
                        </span>
                      </h5>
                    </div>
                    {hasIssues && (
                      <span
                        className={`text-xs px-2 py-0.5 rounded ${getSeverityClass(
                          highestSeverity
                        )}`}
                      >
                        {endpoint.issues.length} problème
                        {endpoint.issues.length > 1 ? "s" : ""}
                      </span>
                    )}
                  </div>

                  <div className="mt-3 grid grid-cols-2 sm:grid-cols-4 gap-2">
                    <div
                      className={`p-2 rounded flex items-center gap-2 ${
                        endpoint.authentication ? "bg-green-50" : "bg-red-50"
                      }`}
                    >
                      <div
                        className={
                          endpoint.authentication
                            ? "text-green-500"
                            : "text-red-500"
                        }
                      >
                        {endpoint.authentication ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <AlertTriangle className="h-4 w-4" />
                        )}
                      </div>
                      <span className="text-xs">Authentification</span>
                    </div>

                    <div
                      className={`p-2 rounded flex items-center gap-2 ${
                        endpoint.rateLimit ? "bg-green-50" : "bg-red-50"
                      }`}
                    >
                      <div
                        className={
                          endpoint.rateLimit ? "text-green-500" : "text-red-500"
                        }
                      >
                        {endpoint.rateLimit ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <AlertTriangle className="h-4 w-4" />
                        )}
                      </div>
                      <span className="text-xs">Limitation</span>
                    </div>

                    <div
                      className={`p-2 rounded flex items-center gap-2 ${
                        endpoint.inputValidation ? "bg-green-50" : "bg-red-50"
                      }`}
                    >
                      <div
                        className={
                          endpoint.inputValidation
                            ? "text-green-500"
                            : "text-red-500"
                        }
                      >
                        {endpoint.inputValidation ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <AlertTriangle className="h-4 w-4" />
                        )}
                      </div>
                      <span className="text-xs">Validation</span>
                    </div>

                    <div
                      className={`p-2 rounded flex items-center gap-2 ${
                        endpoint.securityHeaders ? "bg-green-50" : "bg-red-50"
                      }`}
                    >
                      <div
                        className={
                          endpoint.securityHeaders
                            ? "text-green-500"
                            : "text-red-500"
                        }
                      >
                        {endpoint.securityHeaders ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <AlertTriangle className="h-4 w-4" />
                        )}
                      </div>
                      <span className="text-xs">En-têtes</span>
                    </div>
                  </div>

                  {hasIssues && (
                    <div className="mt-3 space-y-2">
                      <h6 className="text-xs font-medium text-gray-700">
                        Problèmes détectés:
                      </h6>
                      {endpoint.issues.map((issue, issueIndex) => (
                        <div
                          key={`endpoint-${index}-issue-${issueIndex}`}
                          className={`p-2 rounded-lg ${
                            issue.severity === "critical"
                              ? "bg-red-50"
                              : issue.severity === "high"
                              ? "bg-orange-50"
                              : issue.severity === "medium"
                              ? "bg-yellow-50"
                              : "bg-blue-50"
                          }`}
                        >
                          <div className="flex items-start gap-2">
                            <div
                              className={`flex-shrink-0 ${
                                issue.severity === "critical"
                                  ? "text-red-500"
                                  : issue.severity === "high"
                                  ? "text-orange-500"
                                  : issue.severity === "medium"
                                  ? "text-yellow-500"
                                  : "text-blue-500"
                              }`}
                            >
                              <AlertTriangle className="h-4 w-4" />
                            </div>
                            <div>
                              <p className="text-xs font-medium">
                                {issue.title}
                              </p>
                              <p className="text-xs text-gray-600 mt-1">
                                {issue.description}
                              </p>
                              <p className="text-xs text-gray-800 mt-1">
                                <strong>Solution:</strong>{" "}
                                {issue.recommendation}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* CURL Test Snippet */}
                  <div className="mt-3">
                    <h6 className="text-xs font-medium text-gray-700">
                      Tester cet endpoint:
                    </h6>
                    <div className="mt-1 bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                      <pre>
                        curl -X {endpoint.method}{" "}
                        {endpoint.authentication
                          ? '-H "Authorization: Bearer YOUR_TOKEN" '
                          : ""}
                        {endpoint.url}
                      </pre>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Part 3: Best practices & solutions */}
      <div className="p-4 neo-card bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-100">
        <h4 className="font-medium text-indigo-800 mb-3 flex items-center">
          <Server className="h-4 w-4 mr-2" />
          Meilleures Pratiques et Solutions
        </h4>
        <div className="space-y-4">
          <p className="text-sm text-gray-700">
            La sécurisation des API repose sur plusieurs piliers fondamentaux
            qui, ensemble, forment une défense robuste contre les attaques.
            Voici les mesures essentielles à mettre en place pour protéger vos
            API:
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {authenticationIssues.length > 0 && (
              <div className="p-3 bg-white rounded-lg shadow-sm border-l-4 border-indigo-400">
                <div className="flex items-start gap-2">
                  <div className="text-indigo-500 text-xl flex-shrink-0">
                    🔑
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-gray-800">
                      Authentification Robuste
                    </h5>
                    <p className="text-xs text-gray-700 mt-1">
                      Implémentez OAuth 2.0 ou JWT avec des tokens à courte
                      durée de vie et une rotation régulière. Utilisez des
                      mécanismes d'authentification à plusieurs facteurs pour
                      les API sensibles.
                    </p>
                    <div className="mt-2 flex items-center">
                      <span className="text-xs font-medium text-indigo-600">
                        Priorité:
                      </span>
                      <span className="ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">
                        Élevée
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {endpoints.some((e) => !e.rateLimit) && (
              <div className="p-3 bg-white rounded-lg shadow-sm border-l-4 border-indigo-400">
                <div className="flex items-start gap-2">
                  <div className="text-indigo-500 text-xl flex-shrink-0">
                    ⏱️
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-gray-800">
                      Limitation de Débit
                    </h5>
                    <p className="text-xs text-gray-700 mt-1">
                      Implémentez des limites de requêtes par utilisateur et par
                      IP pour prévenir les attaques par force brute, le scraping
                      abusif et les dénis de service.
                    </p>
                    <div className="mt-2 flex items-center">
                      <span className="text-xs font-medium text-indigo-600">
                        Priorité:
                      </span>
                      <span className="ml-2 px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                        Moyenne
                      </span>
                    </div>
                    <div className="mt-2 bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                      <pre>
                        # Express Rate Limit (Node.js)
                        <br />
                        const rateLimit = require("express-rate-limit");
                        <br />
                        <br />
                        const apiLimiter = rateLimit({"{"}
                        <br />
                        {"  "}windowMs: 15 * 60 * 1000, // 15 minutes
                        <br />
                        {"  "}max: 100 // limite par IP
                        <br />
                        {"}"});
                        <br />
                        <br />
                        app.use("/api/", apiLimiter);
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {endpoints.some((e) => !e.inputValidation) && (
              <div className="p-3 bg-white rounded-lg shadow-sm border-l-4 border-indigo-400">
                <div className="flex items-start gap-2">
                  <div className="text-indigo-500 text-xl flex-shrink-0">
                    ✅
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-gray-800">
                      Validation des Entrées
                    </h5>
                    <p className="text-xs text-gray-700 mt-1">
                      Validez rigoureusement toutes les entrées utilisateur côté
                      serveur pour prévenir les injections SQL, NoSQL, les XSS
                      et autres attaques par injection. Utilisez des
                      bibliothèques de validation comme Joi, Yup ou
                      class-validator.
                    </p>
                    <div className="mt-2 flex items-center">
                      <span className="text-xs font-medium text-indigo-600">
                        Priorité:
                      </span>
                      <span className="ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">
                        Élevée
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {endpoints.some((e) => !e.securityHeaders) && (
              <div className="p-3 bg-white rounded-lg shadow-sm border-l-4 border-indigo-400">
                <div className="flex items-start gap-2">
                  <div className="text-indigo-500 text-xl flex-shrink-0">
                    📋
                  </div>
                  <div>
                    <h5 className="text-sm font-medium text-gray-800">
                      En-têtes de Sécurité
                    </h5>
                    <p className="text-xs text-gray-700 mt-1">
                      Configurez les en-têtes de sécurité comme
                      Content-Security-Policy, X-Content-Type-Options,
                      Strict-Transport-Security et X-XSS-Protection pour
                      renforcer la sécurité de vos API.
                    </p>
                    <div className="mt-2 flex items-center">
                      <span className="text-xs font-medium text-indigo-600">
                        Priorité:
                      </span>
                      <span className="ml-2 px-2 py-0.5 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                        Moyenne
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="p-3 bg-white rounded-lg shadow-sm border-l-4 border-indigo-400">
              <div className="flex items-start gap-2">
                <div className="text-indigo-500 text-xl flex-shrink-0">🔍</div>
                <div>
                  <h5 className="text-sm font-medium text-gray-800">
                    Tests de Sécurité Réguliers
                  </h5>
                  <p className="text-xs text-gray-700 mt-1">
                    Effectuez des tests de pénétration et des analyses de
                    vulnérabilités régulièrement pour identifier et corriger les
                    failles avant qu'elles ne soient exploitées. Utilisez des
                    outils comme OWASP ZAP ou Burp Suite.
                  </p>
                  <div className="mt-2 flex items-center">
                    <span className="text-xs font-medium text-indigo-600">
                      Priorité:
                    </span>
                    <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                      Recommandée
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-3 bg-white rounded-lg shadow-sm border-l-4 border-indigo-400">
              <div className="flex items-start gap-2">
                <div className="text-indigo-500 text-xl flex-shrink-0">🛡️</div>
                <div>
                  <h5 className="text-sm font-medium text-gray-800">
                    Documentation et Monitoring
                  </h5>
                  <p className="text-xs text-gray-700 mt-1">
                    Documentez vos API avec OpenAPI/Swagger et mettez en place
                    un monitoring continu pour détecter les comportements
                    anormaux et les tentatives d'intrusion.
                  </p>
                  <div className="mt-2 flex items-center">
                    <span className="text-xs font-medium text-indigo-600">
                      Priorité:
                    </span>
                    <span className="ml-2 px-2 py-0.5 bg-blue-100 text-blue-800 text-xs rounded-full">
                      Recommandée
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiSecurityDetails;
