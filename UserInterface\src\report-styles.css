@tailwind base;
@tailwind components;
@tailwind utilities;

/* Core scoped styles */
.report-template {
  --background: 221 100% 97%; /* Light blue-white */
  --foreground: 222 47% 11%;  /* Deep blue-black */
  background: linear-gradient(135deg, #f1f4ff 0%, #f9f5ff 100%);
  min-height: 100vh;
}

/* Essential components */
@layer components {
  /* Glass card */
  .report-card {
    @apply rounded-xl border border-white/20 backdrop-blur-lg p-6;
    background: rgba(255, 255, 255, 0.85);
    box-shadow: 0 8px 32px -8px rgba(37, 99, 235, 0.1);
  }

  /* Text gradient (reuses your config colors) */
  .report-heading {
    @apply text-transparent bg-clip-text;
    background-image: linear-gradient(to right, #007BFF, #9333EA);
  }

  /* Animated hover effect */
  .report-hover {
    @apply transition-all hover:translate-y-[-2px] hover:shadow-lg;
  }
}