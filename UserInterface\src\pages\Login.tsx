import { useState, useRef, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

declare global {
  interface Window {
    grecaptcha: any;
  }
}
import Logo from "../components/ui-custom/Logo";
import Layout from "../components/Layout";
import Card from "../components/ui-custom/Card";
import Input from "../components/ui-custom/Input";
import Button from "../components/ui-custom/Button";
import { LogIn } from "lucide-react";
import { toast } from "sonner";
import { safeStorage } from "../utils/storage";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

export default function Login() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isCaptchaRequired, setIsCaptchaRequired] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isV2Fallback, setIsV2Fallback] = useState(false);
  const [recaptchaV2Token, setRecaptchaV2Token] = useState("");
  const recaptchaV2Ref = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  // We don't need location anymore since we always redirect to dashboard
  const { login } = useAuth();
  const { executeRecaptcha } = useGoogleReCaptcha();

  // Always redirect to dashboard after successful login
  let from = "/dashboard";

  // Ignore any other redirect paths that might be set
  // This ensures users always go to the dashboard after login

  // Load reCAPTCHA v2 script dynamically when fallback is needed
  useEffect(() => {
    if (isV2Fallback && recaptchaV2Ref.current && !window.grecaptcha) {
      const script = document.createElement("script");
      script.src = "https://www.google.com/recaptcha/api.js";
      script.async = true;
      script.defer = true;
      document.body.appendChild(script);
      script.onload = () => {
        if (window.grecaptcha && recaptchaV2Ref.current) {
          window.grecaptcha.render(recaptchaV2Ref.current, {
            sitekey:
              import.meta.env.VITE_RECAPTCHA_SITE_KEY_V2 ||
              "6LdtTCYrAAAAAN6mv1dND-5_0vCBJgeX_hkIGGGc",
            callback: (token: string) => {
              setRecaptchaV2Token(token);
            },
            "expired-callback": () => {
              setRecaptchaV2Token("");
            },
          });
        }
      };
    }
  }, [isV2Fallback]);

  // State to track toast messages
  const [toastMessage, setToastMessage] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);

  // Effect to show toast messages
  useEffect(() => {
    if (toastMessage) {
      if (toastMessage.type === "success") {
        toast.success(toastMessage.message);
      } else {
        toast.error(toastMessage.message);
      }
      setToastMessage(null);
    }
  }, [toastMessage]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) {
      setToastMessage({
        type: "error",
        message: "Veuillez remplir tous les champs",
      });
      return;
    }

    setLoading(true);
    try {
      let captchaToken = "";

      if (isV2Fallback) {
        if (!recaptchaV2Token) {
          setToastMessage({
            type: "error",
            message: "Veuillez compléter le CAPTCHA.",
          });
          setLoading(false);
          return;
        }
        captchaToken = recaptchaV2Token;
      } else {
        if (executeRecaptcha) {
          captchaToken = await executeRecaptcha("login");
        }
      }

      // Use the login function from AuthContext, pass captchaToken and isV2Fallback flag
      const loginResponse = await login(
        email,
        password,
        captchaToken,
        isV2Fallback
      );

      console.log("Login response:", loginResponse);

      // Check if this is an admin login that requires 2FA
      const isAdminLogin = loginResponse?.requiresTwoFactor === true;
      if (isAdminLogin) {
        // Clear any previous admin verification state
        await safeStorage.removeItem("isAdminVerified");

        // Set admin login state
        await safeStorage.setItem("isAdminLogin", "true");
        await safeStorage.setItem("adminEmail", email);
        await safeStorage.setItem("adminPassword", password);

        // Navigate to forgot password page with admin flag
        navigate("/forgot-password?isAdmin=true");
        return;
      }

      // On successful login, reset CAPTCHA requirement and fallback
      setIsCaptchaRequired(false);
      setIsV2Fallback(false);
      setRecaptchaV2Token("");

      // Set success message and navigate
      setToastMessage({ type: "success", message: "Connexion réussie !" });
      navigate(from, { replace: true });
    } catch (err: any) {
      // Reset CAPTCHA requirement on each error, will be set again if needed
      setIsCaptchaRequired(false);

      if (err.response?.data?.error === "CAPTCHA_REQUIRED") {
        if (!isV2Fallback) {
          setIsV2Fallback(true);
        }
        setIsCaptchaRequired(true);
        setToastMessage({
          type: "error",
          message: err.response.data.message || "CAPTCHA required",
        });
      } else {
        setToastMessage({
          type: "error",
          message:
            err.response?.data?.message || "Email ou mot de passe incorrect",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout showNav={false} showFooter={!loading}>
      <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
        <div className="w-full py-4 px-6 flex justify-between items-center bg-transparent backdrop-blur-sm fixed top-0 z-50 hover:bg-white/10 transition duration-300">
          <Logo to="/" />
          <div className="flex gap-8">
            <a href="#" className="text-white hover:text-indigo-100">
              Services
            </a>
            <a href="#" className="text-white hover:text-indigo-100">
              À propos
            </a>
            <a href="#" className="text-white hover:text-indigo-100">
              Avis
            </a>
          </div>
        </div>
        <Card className="w-full max-w-md animate-scale-in">
          <h1 className="text-2xl font-bold text-center text-gray-800 mb-6">
            Connexion
          </h1>

          <form onSubmit={handleSubmit} className="space-y-4">
            <Input
              type="email"
              placeholder="Adresse email"
              icon="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />

            <Input
              type="password"
              placeholder="Mot de passe"
              icon="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />

            {isCaptchaRequired && !isV2Fallback && (
              <div className="text-red-600 text-sm font-semibold">
                CAPTCHA is required due to multiple failed login attempts.
              </div>
            )}

            {isV2Fallback && <div ref={recaptchaV2Ref} className="my-4"></div>}

            <div className="flex justify-end">
              <Link
                to="/forgot-password"
                className="text-sm text-sitechecker-blue hover:underline"
              >
                Mot de passe oublié ?
              </Link>
            </div>

            <Button type="submit" className="w-full" loading={loading}>
              <LogIn className="w-4 h-4 mr-2" />
              Se connecter
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600">
              Pas de compte ?{" "}
              <Link
                to="/register"
                className="text-sitechecker-blue hover:underline"
              >
                Inscrivez-vous
              </Link>
            </p>
          </div>
        </Card>
      </div>
    </Layout>
  );
}
