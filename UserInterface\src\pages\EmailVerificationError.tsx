import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { AlertTriangle } from "lucide-react";
import Layout from "@/components/Layout";
import Card from "@/components/ui-custom/Card";
import Button from "@/components/ui-custom/Button";
import { toast } from "sonner";
import axios from "axios";

// Base API URL
const API_BASE_URL = "http://localhost:5012"; // Adjust this to match your API server URL

export default function EmailVerificationError() {
  const [email, setEmail] = useState("");
  const [resendLoading, setResendLoading] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const location = useLocation();

  // Constants for resend cooldown
  const RESEND_TIMER_KEY = "emailVerificationResendTimer";
  const RESEND_COOLDOWN = 60; // 60 seconds cooldown

  useEffect(() => {
    // Get email from URL query params
    const params = new URLSearchParams(location.search);
    const emailParam = params.get("email");
    if (emailParam) {
      setEmail(emailParam);
    }

    // Check if there's an active cooldown timer
    const storedEndTimeStr = localStorage.getItem(RESEND_TIMER_KEY);
    if (storedEndTimeStr) {
      const endTime = parseInt(storedEndTimeStr, 10);
      const now = Date.now();
      const remaining = Math.floor((endTime - now) / 1000);

      if (remaining > 0) {
        setTimeLeft(remaining);
        startCountdown(remaining);
      } else {
        // Clear expired timer
        localStorage.removeItem(RESEND_TIMER_KEY);
      }
    }
  }, [location.search]);

  // Start countdown timer
  const startCountdown = (seconds) => {
    if (seconds <= 0) return;

    setTimeLeft(seconds);

    const intervalId = setInterval(() => {
      setTimeLeft((prevTime) => {
        const newTime = prevTime - 1;
        if (newTime <= 0) {
          clearInterval(intervalId);
          localStorage.removeItem(RESEND_TIMER_KEY);
          return 0;
        }
        return newTime;
      });
    }, 1000);

    // Cleanup interval on component unmount
    return () => clearInterval(intervalId);
  };

  // Handle resend verification email
  const handleResendVerificationEmail = async () => {
    if (timeLeft > 0) return;

    if (!email) {
      toast.error("Adresse e-mail non disponible. Veuillez vous réinscrire.");
      return;
    }

    setResendLoading(true);

    try {
      await axios.post(`${API_BASE_URL}/api/auth/resend-verification-email`, {
        email: email,
      });

      // Set cooldown timer
      const endTime = Date.now() + RESEND_COOLDOWN * 1000;
      localStorage.setItem(RESEND_TIMER_KEY, endTime.toString());

      // Start countdown
      startCountdown(RESEND_COOLDOWN);

      toast.success("Un nouvel e-mail de vérification a été envoyé.");

      // Redirect to check-email page with email parameter after a short delay
      setTimeout(() => {
        window.location.href = `/check-email?email=${encodeURIComponent(
          email
        )}`;
      }, 1500);
    } catch (error) {
      console.error("Error resending verification email:", error);
      const errorMessage =
        error.response?.data?.message ||
        "Erreur lors de l'envoi de l'e-mail de vérification. Veuillez réessayer plus tard.";
      toast.error(errorMessage);
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <Layout showNav={true} navType="second">
      <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
        <Card className="w-full max-w-md backdrop-blur-sm rounded-xl shadow-lg p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-10 h-10 text-red-500" />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Erreur de Vérification
          </h1>

          <p className="text-gray-600 mb-2">
            Nous n'avons pas pu vérifier votre adresse e-mail. Le lien de
            vérification est peut-être expiré ou invalide.
          </p>

          <p className="text-gray-500 text-sm mb-6">
            Veuillez demander un nouveau lien de vérification pour continuer.
          </p>

          <div className="space-y-3">
            <Button
              onClick={handleResendVerificationEmail}
              className="w-full"
              disabled={timeLeft > 0 || resendLoading}
            >
              {timeLeft > 0
                ? `Réessayer dans ${timeLeft}s`
                : resendLoading
                ? "Envoi en cours..."
                : "Réessayer"}
            </Button>

            <Link to="/login">
              <Button variant="outline" className="w-full">
                Retour à la connexion
              </Button>
            </Link>
          </div>
        </Card>
      </div>
    </Layout>
  );
}
