import CryptoJS from "crypto-js";

const SECRET_KEY =
  import.meta.env.VITE_STORAGE_ENCRYPTION_KEY || "default-secure-key";

export const encryptData = (data: string): string => {
  return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
};

export const decryptData = (ciphertext: string): string => {
  const bytes = CryptoJS.AES.decrypt(ciphertext, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

export const isEncryptionSupported = (): boolean => {
  try {
    encryptData("test");
    return true;
  } catch {
    return false;
  }
};
