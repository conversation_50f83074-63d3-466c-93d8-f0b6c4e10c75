﻿using System.ComponentModel.DataAnnotations;

namespace SiteCheckerApp.DTOs;

public class RegisterRequest
{
    [Required]
    public string Username { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    public string Email { get; set; } = string.Empty;

    [Required]
    [StringLength(100, MinimumLength = 6)]
    public string Password { get; set; } = string.Empty;

    [Required]
    public string Plan { get; set; } = "Free";

    [Required]
    public string BillingCycle { get; set; } = "Monthly";

    [Required]
    public string CaptchaToken { get; set; } = string.Empty;

    public string? IsV2Fallback { get; set; } = "register";
}
