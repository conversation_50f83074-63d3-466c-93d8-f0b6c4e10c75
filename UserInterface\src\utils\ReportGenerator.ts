import {
  SecurityReport,
  SeoReport,
  AdvancedSslDetail,
  BrowserScanDetail,
  ReportAction,
  StatusType,
  SslDetail,
} from "@/types/report";

class ReportGenerator {
  // Toast method removed as progress is now shown directly in the UI

  // Enhanced security report generation
  static generateSecurityReport(apiData: any): SecurityReport {
    try {
      if (!apiData) {
        throw new Error("No security data received");
      }

      // Extract individual scores from categories
      const sslCategory = (apiData.categories || []).find(
        (cat: any) =>
          cat.name.toLowerCase() === "ssl/tls" ||
          cat.name.toLowerCase() === "ssl"
      );
      const headersCategory = (apiData.categories || []).find(
        (cat: any) =>
          cat.name.toLowerCase() === "http headers" ||
          cat.name.toLowerCase() === "headers"
      );
      const vulnerabilitiesCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "vulnerabilities"
      );
      const jsSecurityCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "javascript security"
      );
      const apiSecurityCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "api security"
      );

      const report: SecurityReport = {
        overall_score: apiData.overall_score || 0,
        ssl_score: sslCategory ? sslCategory.score : 0,
        headers_score: headersCategory ? headersCategory.score : 0,
        vulnerabilities_score: vulnerabilitiesCategory
          ? vulnerabilitiesCategory.score
          : 0,
        js_security_score: jsSecurityCategory ? jsSecurityCategory.score : 0,
        api_security_score: apiSecurityCategory ? apiSecurityCategory.score : 0,
        categories: apiData.categories || [],
        ssl_details: this.mapBasicSslDetails(apiData.ssl_details),
        http_headers: (apiData.http_headers || []).map((h: any) => ({
          name: h.name || "Unknown",
          value: h.value || "",
          description: h.description,
          securityImpact: h.security_impact,
          recommended: h.recommended || false,
          importance: h.importance || "low",
          status: h.value ? "success" : ("error" as StatusType),
        })),
        vulnerabilities: (apiData.vulnerabilities || []).map((v: any) => ({
          id: v.id || "unknown",
          name: v.name || "Unknown Vulnerability",
          cveId: v.cve_id,
          severity: v.severity || "medium",
          description: v.description || "No description available",
          affectedComponent: v.affected_component,
          fixAvailable: v.fix_available || false,
          fixVersion: v.fix_version,
        })),
        recommended_actions: this.mapRecommendedActions(
          apiData.recommended_actions
        ),
        scan_date: apiData.scan_date || new Date().toISOString(),
        success: true,

        // Map JavaScript Security details
        js_libraries: apiData.js_libraries
          ? apiData.js_libraries.map((lib: any) => ({
              name: lib.name || "Unknown Library",
              version: lib.version || "Unknown",
              vulnerabilities: lib.vulnerabilities || 0,
              severity: lib.severity || "none",
              outdated: lib.outdated || false,
              latestVersion: lib.latest_version,
              description: lib.description,
              vulnerabilityDetails: lib.vulnerability_details,
            }))
          : undefined,
        js_total_vulnerabilities: apiData.js_total_vulnerabilities || 0,
        js_critical_vulnerabilities: apiData.js_critical_vulnerabilities || 0,
        js_high_vulnerabilities: apiData.js_high_vulnerabilities || 0,
        js_medium_vulnerabilities: apiData.js_medium_vulnerabilities || 0,
        js_low_vulnerabilities: apiData.js_low_vulnerabilities || 0,

        // Map API Security details
        api_endpoints: apiData.api_endpoints
          ? apiData.api_endpoints.map((endpoint: any) => ({
              url: endpoint.url || "",
              method: endpoint.method || "GET",
              authentication: endpoint.authentication || false,
              rateLimit: endpoint.rate_limit || false,
              inputValidation: endpoint.input_validation || false,
              securityHeaders: endpoint.security_headers || false,
              issues: endpoint.issues
                ? endpoint.issues.map((issue: any) => ({
                    title: issue.title || "Unknown Issue",
                    description: issue.description || "",
                    severity: issue.severity || "medium",
                    recommendation: issue.recommendation || "",
                  }))
                : [],
            }))
          : undefined,
        api_auth_issues: apiData.api_auth_issues
          ? apiData.api_auth_issues.map((issue: any) => ({
              title: issue.title || "Unknown Issue",
              description: issue.description || "",
              severity: issue.severity || "medium",
              recommendation: issue.recommendation || "",
            }))
          : undefined,
        api_detected:
          apiData.api_detected !== undefined ? apiData.api_detected : false,
      };

      return report;
    } catch (error) {
      console.error("Error generating security report:", error);
      throw error;
    }
  }

  // Enhanced SEO report generation
  static generateSeoReport(
    apiData: any,
    isPremiumOrAdmin: boolean = false
  ): SeoReport {
    try {
      if (!apiData) {
        throw new Error("No SEO data received");
      }

      // Extract individual scores from categories
      const metaCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "meta tags"
      );
      const mobileCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "mobile optimization"
      );
      const performanceCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "performance"
      );
      const contentCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "content analysis"
      );
      const imageCategory = (apiData.categories || []).find(
        (cat: any) => cat.name.toLowerCase() === "image optimization"
      );

      // Filter categories based on user role
      const filteredCategories = isPremiumOrAdmin
        ? apiData.categories || []
        : (apiData.categories || []).filter(
            (cat: any) =>
              cat.name.toLowerCase() === "meta tags" ||
              cat.name.toLowerCase() === "mobile optimization" ||
              cat.name.toLowerCase() === "performance"
          );

      const report: SeoReport = {
        overall_score: apiData.overall_score || 0,
        meta_score: metaCategory ? metaCategory.score : 0,
        mobile_score: mobileCategory ? mobileCategory.score : 0,
        performance_score: performanceCategory ? performanceCategory.score : 0,
        content_score: contentCategory ? contentCategory.score : 0,
        image_score: imageCategory ? imageCategory.score : 0,
        categories: filteredCategories,
        meta_tags: (apiData.meta_tags || []).map((tag: any) => {
          // Convert backend status to frontend status
          let status: StatusType = "error";
          if (tag.status === "good") {
            status = "success";
          } else if (tag.status === "warning") {
            status = "warning";
          } else if (tag.status === "error") {
            status = "error";
          }

          return {
            name: tag.name || "Unknown",
            content: tag.content || "",
            status: status,
            recommendation: tag.recommendation || "",
            importance: "medium" as "high" | "medium" | "low", // Default importance
            description: `Balise meta ${tag.name} qui affecte le référencement de votre site`,
          };
        }),
        performance_metrics: apiData.performance_metrics
          ? {
              responseTime: apiData.performance_metrics.response_time,
              compressionRatio: apiData.performance_metrics.compression_ratio,
              cacheStatus: apiData.performance_metrics.cache_status,
              pageSize: apiData.performance_metrics.page_size,
              firstContentfulPaint:
                apiData.performance_metrics.first_contentful_paint,
              largestContentfulPaint:
                apiData.performance_metrics.largest_contentful_paint,
              timeToInteractive:
                apiData.performance_metrics.time_to_interactive,
              cumulativeLayoutShift:
                apiData.performance_metrics.cumulative_layout_shift,
              speedIndex: apiData.performance_metrics.speed_index,
              resourceWaterfallAnalysis: isPremiumOrAdmin
                ? apiData.performance_metrics.resource_waterfall_analysis
                : undefined,
            }
          : undefined,
        mobile_optimization: apiData.mobile_optimization
          ? {
              responsiveDesign: apiData.mobile_optimization.responsive_design,
              viewportConfiguration:
                apiData.mobile_optimization.viewport_configuration,
              touchTargets: apiData.mobile_optimization.touch_targets,
              fontSizes: apiData.mobile_optimization.font_sizes,
              mobileUsability: apiData.mobile_optimization.mobile_usability,
            }
          : undefined,
        content_analysis:
          isPremiumOrAdmin && apiData.content_analysis
            ? {
                wordCount: apiData.content_analysis.wordCount || 0,
                readabilityScore:
                  apiData.content_analysis.readabilityScore !== undefined
                    ? apiData.content_analysis.readabilityScore
                    : undefined,
                keywordDensity: apiData.content_analysis.keywordDensity || {},
                contentQualityScore:
                  apiData.content_analysis.contentQualityScore,
                headingsStructure: apiData.content_analysis.headingsStructure
                  ? {
                      h1Count:
                        apiData.content_analysis.headingsStructure.h1Count || 0,
                      h2Count:
                        apiData.content_analysis.headingsStructure.h2Count || 0,
                      h3Count:
                        apiData.content_analysis.headingsStructure.h3Count || 0,
                      h4Count:
                        apiData.content_analysis.headingsStructure.h4Count || 0,
                      h5Count:
                        apiData.content_analysis.headingsStructure.h5Count || 0,
                      h6Count:
                        apiData.content_analysis.headingsStructure.h6Count || 0,
                      headingsInOrder:
                        apiData.content_analysis.headingsStructure
                          .headingsInOrder || false,
                    }
                  : undefined,
                semanticAnalysis: apiData.content_analysis.semantic_analysis
                  ? {
                      keyPhrases:
                        apiData.content_analysis.semantic_analysis
                          .key_phrases || [],
                      sentiment:
                        apiData.content_analysis.semantic_analysis.sentiment ||
                        "neutral",
                    }
                  : undefined,
                structuredData: apiData.content_analysis.structured_data,
              }
            : undefined,
        image_analysis: apiData.image_analysis
          ? {
              totalImages: apiData.image_analysis.total_images || 0,
              imagesWithAlt: apiData.image_analysis.images_with_alt || 0,
              imagesWithoutAlt: apiData.image_analysis.images_without_alt || 0,
              imagesCompressed: apiData.image_analysis.images_compressed || 0,
              imagesUncompressed:
                apiData.image_analysis.images_uncompressed || 0,
              imagesWithLazyLoading:
                apiData.image_analysis.images_with_lazy_loading || 0,
              largeImages: apiData.image_analysis.large_images || [],
              missingAltImages: apiData.image_analysis.missing_alt_images || [],
            }
          : null,
        recommended_actions: this.mapRecommendedActions(
          apiData.recommended_actions
        ),
        scan_date: apiData.scan_date || new Date().toISOString(),
        success: true,
        url_structure: isPremiumOrAdmin
          ? apiData.url_structure || {
              canonicalUrl: "",
              redirects: [],
              sitemap: {
                exists: false,
                lastModified: "",
                urlCount: 0,
              },
            }
          : undefined,
      };

      return report;
    } catch (error) {
      console.error("Error generating SEO report:", error);
      throw error;
    }
  }

  static mapAdvancedSslDetails(sslData: any): AdvancedSslDetail {
    try {
      if (!sslData) {
        throw new Error("No SSL data received");
      }

      return {
        protocolVersions: sslData.protocol_versions || [],
        keyExchange: sslData.key_exchange || "Unknown",
        certificateChain: (sslData.certificate_chain || []).map(
          (cert: any) => ({
            subject: cert.subject || "Unknown",
            issuer: cert.issuer || "Unknown",
            validFrom: cert.valid_from || "Unknown",
            validTo: cert.valid_to || "Unknown",
            fingerprint: cert.fingerprint || "Unknown",
          })
        ),
        vulnerabilities: sslData.vulnerabilities || [],
      };
    } catch (error) {
      console.error("Error mapping SSL details:", error);
      throw error;
    }
  }

  static mapBasicSslDetails(sslData: any): SslDetail {
    try {
      if (!sslData) {
        throw new Error("No SSL data received");
      }

      // Déterminer le type de certificat en fonction du sujet
      let certificateType = "Standard";
      if (sslData.subject) {
        if (
          sslData.subject.toLowerCase().includes("extended validation") ||
          sslData.subject.toLowerCase().includes("ev")
        ) {
          certificateType = "EV (Extended Validation)";
        } else if (
          sslData.subject.toLowerCase().includes("organization validation") ||
          sslData.subject.toLowerCase().includes("organization") ||
          sslData.subject.toLowerCase().includes("ov")
        ) {
          certificateType = "OV (Organization Validation)";
        } else if (
          sslData.subject.toLowerCase().includes("domain validation") ||
          sslData.subject.toLowerCase().includes("dv")
        ) {
          certificateType = "DV (Domain Validation)";
        }
      }

      // Formater la date d'expiration
      let expirationDate = "Unknown";
      if (sslData.not_valid_after) {
        try {
          const date = new Date(sslData.not_valid_after);
          expirationDate = date.toLocaleDateString("fr-FR", {
            day: "numeric",
            month: "long",
            year: "numeric",
          });
        } catch (e) {
          expirationDate = sslData.not_valid_after;
        }
      }

      // Déterminer la force du chiffrement
      let cipherStrength = "Fort (AES-256 ou équivalent)";
      if (sslData.cipher_strength) {
        cipherStrength = sslData.cipher_strength;
      }

      // Déterminer si TLS 1.3 est supporté
      let supportsTls13 = false;
      if (sslData.protocols && Array.isArray(sslData.protocols)) {
        supportsTls13 = sslData.protocols.some(
          (p: string) => p.includes("TLSv1.3") || p.includes("TLS 1.3")
        );
      }

      // Si le score SSL est élevé et qu'aucun protocole n'est détecté, supposer TLS 1.3
      if (!supportsTls13 && sslData.ssl_score && sslData.ssl_score >= 90) {
        supportsTls13 = true;
      }

      // Si la force du chiffrement indique un chiffrement moderne, supposer TLS 1.3
      if (
        !supportsTls13 &&
        sslData.cipher_strength &&
        sslData.cipher_strength.includes("AES-256")
      ) {
        supportsTls13 = true;
      }

      return {
        certificateType: sslData.certificate_type || certificateType,
        expirationDate: sslData.expiration_date || expirationDate,
        issuer: sslData.issuer || "Unknown",
        cipherStrength: sslData.cipher_strength || cipherStrength,
        forwardSecrecy: sslData.forward_secrecy || true, // Supposer vrai pour TLS 1.3
        hstsEnabled:
          sslData.hsts_enabled ||
          (sslData.http_headers &&
          sslData.http_headers["Strict-Transport-Security"]
            ? true
            : false),
        ocspStapling:
          sslData.ocsp_stapling ||
          (sslData.certificate && sslData.certificate.ocsp_stapling) ||
          false,
        vulnerabilities: sslData.vulnerabilities || [],
        supportsTls13:
          sslData.supports_tls13 !== false
            ? sslData.supports_tls13 || supportsTls13
            : supportsTls13,
        severity: sslData.severity || "low",
        // Ajouter les nouveaux champs
        not_valid_after: sslData.not_valid_after || null,
        not_valid_before: sslData.not_valid_before || null,
        subject: sslData.subject || null,
      };
    } catch (error) {
      console.error("Error mapping SSL details:", error);
      throw error;
    }
  }

  static mapBrowserScan(browserData: any): BrowserScanDetail {
    try {
      if (!browserData) {
        throw new Error("No browser scan data received");
      }

      return {
        screenshotUrl: browserData.screenshot_url || "",
        consoleErrors: browserData.console_errors || [],
        blockedResources: browserData.blocked_resources || [],
        jsExecutionTime: browserData.js_execution_time || "Unknown",
        domLoadTime: browserData.dom_load_time || "Unknown",
      };
    } catch (error) {
      console.error("Error mapping browser scan:", error);
      throw error;
    }
  }

  private static mapRecommendedActions(actions: any[]): ReportAction[] {
    try {
      return (actions || []).map((action) => {
        // Determine if this is a security or SEO action based on the type
        // SEO action types include: meta_tags, mobile_optimization, content, url_structure, images, performance
        const seoActionTypes = [
          "meta_tags",
          "mobile_optimization",
          "content",
          "url_structure",
          "images",
          "performance",
        ];
        const isSeoAction =
          seoActionTypes.includes(action.type) || action.type === "seo";

        return {
          title: action.title || "Unknown Action",
          description: action.description || "No description available",
          priority: action.priority || 3,
          // Preserve the original action type for more specific categorization
          type: isSeoAction ? action.type : "security",
          implementationDifficulty:
            action.implementation_difficulty || "medium",
          estimatedImpact: action.estimated_impact || "medium",
        };
      });
    } catch (error) {
      console.error("Error mapping recommended actions:", error);
      return [];
    }
  }
}

// Helper exports
export const processSecurityReport = (data: any): SecurityReport =>
  ReportGenerator.generateSecurityReport(data);

export const processSeoReport = (
  data: any,
  isPremiumOrAdmin: boolean = false
): SeoReport => ReportGenerator.generateSeoReport(data, isPremiumOrAdmin);

export { ReportGenerator };
