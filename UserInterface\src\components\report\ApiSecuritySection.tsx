import React from "react";
import { Server } from "lucide-react";
import { TechnicalSectionItem } from "./interfaces";

interface ApiEndpoint {
  url: string;
  method: string;
  authentication: boolean;
  rateLimit: boolean;
  inputValidation: boolean;
  securityHeaders: boolean;
  issues: {
    title: string;
    description: string;
    severity: "critical" | "high" | "medium" | "low";
    recommendation: string;
  }[];
}

interface ApiSecurityIssue {
  title: string;
  description: string;
  severity: "critical" | "high" | "medium" | "low";
  recommendation: string;
}

interface ApiSecuritySectionProps {
  endpoints?: ApiEndpoint[];
  authenticationIssues?: ApiSecurityIssue[];
  overallScore?: number;
  apiDetected?: boolean;
  score?: number;
}

const ApiSecuritySection = ({
  endpoints = [],
  authenticationIssues = [],
  overallScore = 0,
  apiDetected = false,
  score = 0,
}: ApiSecuritySectionProps): TechnicalSectionItem => {
  // Handle case where no API is detected
  if (!apiDetected) {
    return {
      id: "security-5",
      title: "API Security",
      icon: <Server className="w-4 h-4" />,
      score: 100, // Perfect score since no API means no vulnerabilities
      useUnifiedModel: true,
      impactStatement: {
        text: "Les API (interfaces de programmation) sont comme des portes d'entrée vers les données et fonctionnalités de votre site. Bien sécurisées, elles permettent des échanges de données fiables. Mal protégées, elles peuvent être exploitées par des attaquants pour accéder à vos données sensibles.",
        positivePoints: [
          "Aucune API publique détectée sur votre site",
          "Risque réduit car aucune API n'est exposée aux attaques",
          "Votre site utilise une approche plus sécurisée sans API publiques",
        ],
        negativePoints: [],
      },
      findings: [
        {
          status: "success",
          label: "Aucune API détectée",
          impact:
            "Votre site n'expose pas d'API publiques, ce qui réduit considérablement les risques d'attaques ciblant ces points d'entrée",
        },
        {
          status: "success",
          label: "Surface d'attaque réduite",
          impact:
            "L'absence d'API publiques réduit les possibilités d'exploitation par des attaquants",
        },
      ],
      technicalDetails: [
        {
          parameter: "API détectée",
          value: "Non",
          impact:
            "Votre site n'expose pas d'API publiques aux attaquants potentiels",
          tooltip:
            "Les API sont des interfaces qui permettent à différentes applications de communiquer entre elles. Elles peuvent être ciblées par des attaquants si elles ne sont pas correctement sécurisées.",
          recommended:
            "Continuez à surveiller votre site pour vous assurer qu'aucune API n'est exposée accidentellement lors de futures mises à jour",
        },
        {
          parameter: "Risque d'attaque",
          value: "Faible",
          impact:
            "Sans API exposée, le risque d'attaques ciblant ces interfaces est considérablement réduit",
          tooltip:
            "Les attaques sur les API peuvent inclure l'injection de code, le contournement d'authentification ou l'extraction de données sensibles",
        },
      ],
      actionReference: {
        count: 0,
        sectionName: "API",
        actionWord: "Vérifier",
        targetId: "api-security-actions",
      },
    };
  }

  // Count issues by severity
  const criticalIssues = [
    ...authenticationIssues,
    ...endpoints.flatMap((e) => e.issues),
  ].filter((i) => i.severity === "critical").length;
  const highIssues = [
    ...authenticationIssues,
    ...endpoints.flatMap((e) => e.issues),
  ].filter((i) => i.severity === "high").length;
  const mediumIssues = [
    ...authenticationIssues,
    ...endpoints.flatMap((e) => e.issues),
  ].filter((i) => i.severity === "medium").length;
  const lowIssues = [
    ...authenticationIssues,
    ...endpoints.flatMap((e) => e.issues),
  ].filter((i) => i.severity === "low").length;
  // Calculate total issues for reference
  const totalIssues = criticalIssues + highIssues + mediumIssues + lowIssues;

  // Generate positive and negative points with more user-friendly language
  const positivePoints = [
    authenticationIssues.length === 0
      ? "Votre système d'authentification API est correctement configuré, empêchant les accès non autorisés"
      : "",
    endpoints.filter((e) => e.authentication).length === endpoints.length &&
    endpoints.length > 0
      ? "Toutes vos API exigent une authentification, ce qui protège vos données contre les accès non autorisés"
      : "",
    endpoints.filter((e) => e.securityHeaders).length === endpoints.length &&
    endpoints.length > 0
      ? "Vos API utilisent des en-têtes de sécurité qui renforcent leur protection contre diverses attaques"
      : "",
    endpoints.filter((e) => e.rateLimit).length === endpoints.length &&
    endpoints.length > 0
      ? "Vos API sont protégées contre les attaques par force brute grâce à des limites de débit"
      : "",
  ].filter(Boolean);

  const negativePoints = [
    authenticationIssues.length > 0
      ? `${authenticationIssues.length} problèmes d'authentification API détectés qui pourraient permettre des accès non autorisés`
      : "",
    endpoints.filter((e) => !e.authentication).length > 0
      ? `${
          endpoints.filter((e) => !e.authentication).length
        } API sans authentification, ce qui expose vos données à des accès non autorisés`
      : "",
    endpoints.filter((e) => !e.rateLimit).length > 0
      ? `${
          endpoints.filter((e) => !e.rateLimit).length
        } API sans limitation de débit, les rendant vulnérables aux attaques par force brute`
      : "",
    endpoints.filter((e) => !e.inputValidation).length > 0
      ? `${
          endpoints.filter((e) => !e.inputValidation).length
        } API sans validation des entrées, ce qui les expose aux attaques par injection`
      : "",
    endpoints.filter((e) => !e.securityHeaders).length > 0
      ? `${
          endpoints.filter((e) => !e.securityHeaders).length
        } API sans en-têtes de sécurité, réduisant leur protection contre diverses attaques`
      : "",
  ].filter(Boolean);

  // Generate findings
  const findings = [];

  // Add findings for authentication issues
  for (const issue of authenticationIssues.slice(0, 2)) {
    findings.push({
      status:
        issue.severity === "critical" || issue.severity === "high"
          ? "error"
          : "warning",
      label: issue.title,
      impact: issue.description,
    });
  }

  // Add findings for endpoint issues
  const endpointsWithIssues = endpoints
    .filter((endpoint) => endpoint.issues.length > 0)
    .sort((a, b) => {
      // Sort by highest severity issue
      const getHighestSeverity = (endpoint: ApiEndpoint) => {
        const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
        return Math.min(
          ...endpoint.issues.map((issue) => severityOrder[issue.severity])
        );
      };
      return getHighestSeverity(a) - getHighestSeverity(b);
    })
    .slice(0, 2);

  for (const endpoint of endpointsWithIssues) {
    const highestSeverityIssue = endpoint.issues.sort((a, b) => {
      const severityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
      return severityOrder[a.severity] - severityOrder[b.severity];
    })[0];

    findings.push({
      status:
        highestSeverityIssue.severity === "critical" ||
        highestSeverityIssue.severity === "high"
          ? "error"
          : "warning",
      label: `${endpoint.method} ${endpoint.url}`,
      impact: highestSeverityIssue.description,
    });
  }

  // Add findings for missing security features
  if (findings.length < 4) {
    if (
      endpoints.filter((e) => !e.rateLimit).length > 0 &&
      findings.length < 4
    ) {
      findings.push({
        status: "warning",
        label: "Limitation de débit manquante",
        impact:
          "Certains points de terminaison n'ont pas de limitation de débit, ce qui les expose aux attaques par force brute",
      });
    }

    if (
      endpoints.filter((e) => !e.inputValidation).length > 0 &&
      findings.length < 4
    ) {
      findings.push({
        status: "warning",
        label: "Validation des entrées insuffisante",
        impact:
          "Certains points de terminaison n'ont pas de validation des entrées, ce qui les expose aux injections",
      });
    }
  }

  // Generate technical details with more user-friendly explanations
  const technicalDetails = [
    {
      parameter: "API détectée",
      value: "Oui",
      impact:
        "Votre site expose des API qui nécessitent une sécurisation appropriée pour protéger vos données",
      tooltip:
        "Les API sont des interfaces qui permettent à différentes applications de communiquer entre elles. Elles peuvent être ciblées par des attaquants si elles ne sont pas correctement sécurisées.",
      recommended:
        "Assurez-vous que toutes vos API sont correctement authentifiées et validées",
    },
    {
      parameter: "Points d'accès API",
      value: endpoints.length.toString(),
      impact:
        "Chaque point d'accès API est une porte potentielle pour les attaquants s'il n'est pas correctement sécurisé",
      tooltip:
        "Un point d'accès API (endpoint) est une URL spécifique qui permet d'accéder à une fonctionnalité ou à des données. Plus vous avez de points d'accès, plus votre surface d'attaque est grande.",
      recommended:
        "Limitez le nombre de points d'accès API au strict nécessaire et sécurisez chacun d'eux",
    },
    {
      parameter: "Problèmes d'authentification",
      value: authenticationIssues.length.toString(),
      impact:
        "Des faiblesses dans l'authentification peuvent permettre à des attaquants d'accéder à vos données sensibles",
      tooltip:
        "L'authentification vérifie l'identité des utilisateurs ou des systèmes qui tentent d'accéder à vos API. Des problèmes dans ce mécanisme peuvent permettre des accès non autorisés.",
      recommended:
        authenticationIssues.length > 0
          ? "Corrigez les problèmes d'authentification identifiés dans la section des recommandations"
          : "Continuez à surveiller votre système d'authentification",
    },
    {
      parameter: "API non sécurisées",
      value: endpoints
        .filter((e) => !e.authentication || !e.securityHeaders)
        .length.toString(),
      impact:
        "Ces API présentent des vulnérabilités qui pourraient être exploitées par des attaquants",
      tooltip:
        "Une API non sécurisée manque de mécanismes essentiels comme l'authentification, la validation des entrées ou les en-têtes de sécurité appropriés.",
      recommended:
        endpoints.filter((e) => !e.authentication || !e.securityHeaders)
          .length > 0
          ? "Ajoutez l'authentification et les en-têtes de sécurité appropriés à toutes vos API"
          : "Maintenez vos bonnes pratiques de sécurité API",
    },
    {
      parameter: "Validation des entrées",
      value:
        endpoints.filter((e) => e.inputValidation).length +
        "/" +
        endpoints.length,
      impact:
        "La validation des entrées empêche les attaques par injection comme SQL injection ou XSS",
      tooltip:
        "La validation des entrées vérifie que les données reçues par votre API sont conformes à ce qui est attendu, empêchant l'injection de code malveillant.",
      recommended:
        "Implémentez une validation stricte des entrées sur toutes vos API",
    },
  ];

  // Count issues for action reference
  const issueCount =
    authenticationIssues.length +
    endpoints.filter((e) => !e.authentication).length +
    endpoints.filter((e) => !e.rateLimit).length +
    endpoints.filter((e) => !e.inputValidation).length +
    endpoints.filter((e) => !e.securityHeaders).length;

  // Ensure we have positive points if score is high
  let finalPositivePoints = positivePoints;
  if (score >= 80 && positivePoints.length === 0) {
    finalPositivePoints = [
      "La sécurité de vos API est globalement bonne avec peu de vulnérabilités détectées",
      "La plupart de vos API sont correctement protégées contre les attaques courantes",
      "Votre configuration API suit généralement les bonnes pratiques de sécurité",
    ];
  } else if (score >= 60 && positivePoints.length === 0) {
    finalPositivePoints = [
      "Certains aspects de la sécurité de vos API sont bien configurés",
      "Vos API présentent une protection de base contre certaines attaques",
    ];
  }

  // Return the unified model object
  return {
    id: "security-5",
    title: "API Security",
    icon: <Server className="w-4 h-4" />,
    score: score,
    useUnifiedModel: true,
    impactStatement: {
      text:
        "Les API (interfaces de programmation) sont comme des portes d'entrée vers les données et fonctionnalités de votre site. Bien sécurisées, elles permettent des échanges de données fiables. Mal protégées, elles peuvent être exploitées par des attaquants pour accéder à vos données sensibles." +
        (totalIssues > 0
          ? " Nous avons détecté " +
            totalIssues +
            " problèmes qui nécessitent votre attention."
          : ""),
      positivePoints: finalPositivePoints,
      negativePoints: negativePoints,
    },
    findings: findings,
    technicalDetails: technicalDetails,
    actionReference: {
      count: issueCount,
      sectionName: "API",
      actionWord: "Sécuriser",
      targetId: "api-security-actions",
    },
  };
};

export default ApiSecuritySection;
