using SiteCheckerApp.DTOs;
using SiteCheckerApp.Models;
using SiteCheckerApp.Repositories;
using System.Security.Cryptography;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using SiteCheckerApp.Data;

namespace SiteCheckerApp.Services
{
    public class UserService : IUserService
    {
        private readonly IUserRepository _userRepository;
        private readonly IJwtService _jwtService;
        private readonly IEmailService _emailService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UserService> _logger;
        private readonly AppDbContext _context;
        private readonly IRefreshTokenService _refreshTokenService;

        public UserService(
            IUserRepository userRepository,
            IJwtService jwtService,
            IEmailService emailService,
            IConfiguration configuration,
            ILogger<UserService> logger,
            AppDbContext context,
            IRefreshTokenService refreshTokenService)
        {
            _userRepository = userRepository;
            _jwtService = jwtService;
            _emailService = emailService;
            _configuration = configuration;
            _logger = logger;
            _context = context;
            _refreshTokenService = refreshTokenService;
        }

        public async Task<bool> VerifyPasswordAsync(User user, string password)
        {
            if (user == null || string.IsNullOrEmpty(user.PasswordHash))
            {
                return false;
            }
            return await Task.Run(() => BCrypt.Net.BCrypt.Verify(password, user.PasswordHash));
        }

        public async Task UpdateUserAsync(User user)
        {
            if (user == null)
            {
                throw new ArgumentNullException(nameof(user));
            }
            await _userRepository.UpdateAsync(user);
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            return await _userRepository.GetByEmailAsync(email);
        }

        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            return await _userRepository.GetByUsernameAsync(username);
        }

        public async Task<RegisterResponse> RegisterAsync(RegisterRequest request)
        {
            if (!IsPasswordStrong(request.Password))
            {
                return new RegisterResponse
                {
                    Success = false,
                    Message = "Password does not meet strength requirements."
                };
            }

            var existingUserByEmail = await _userRepository.GetByEmailAsync(request.Email);
            if (existingUserByEmail != null)
            {
                return new RegisterResponse
                {
                    Success = false,
                    Message = "Email is already registered"
                };
            }

            var existingUserByUsername = await _userRepository.GetByUsernameAsync(request.Username);
            if (existingUserByUsername != null)
            {
                return new RegisterResponse
                {
                    Success = false,
                    Message = "Username is already taken"
                };
            }

            var newUser = new User
            {
                Username = request.Username,
                Email = request.Email,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                VerificationToken = GenerateVerificationCode(),
                VerificationTokenExpiry = DateTime.UtcNow.AddHours(24),
                IsVerified = false,
                BillingCycle = request.BillingCycle,
                IdRole = request.Plan.ToLower() == "premium" ? 2 : 1,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _userRepository.CreateAsync(newUser);

            // Get the backend URL directly from environment variables
            string? backendBaseUrl = Environment.GetEnvironmentVariable("BACKEND_BASE_URL");

            // If environment variable is not set, try configuration or use default
            if (string.IsNullOrEmpty(backendBaseUrl))
            {
                backendBaseUrl = _configuration["AppSettings:BackendBaseUrl"];

                // If still null or contains placeholder, use default
                if (string.IsNullOrEmpty(backendBaseUrl) || backendBaseUrl?.Contains("${") == true)
                {
                    backendBaseUrl = "http://localhost:5012";
                }
            }

            _logger.LogInformation($"Using backend URL for verification link: {backendBaseUrl}");
            string verificationLink = $"{backendBaseUrl}/api/auth/verify-email?email={newUser.Email}&token={newUser.VerificationToken}";

            await _emailService.SendVerificationEmailAsync(newUser.Email, verificationLink);

            return new RegisterResponse
            {
                Success = true,
                Message = "Registration successful. Check your email for verification.",
                UserId = newUser.Id,
                RequiresVerification = true,
                Plan = request.Plan,
                BillingCycle = request.BillingCycle
            };
        }

        public async Task<VerificationResult> VerifyUserAsync(string email, string verificationToken)
        {
            var user = await _userRepository.GetByEmailAsync(email);
            if (user == null ||
                user.VerificationToken != verificationToken ||
                user.VerificationTokenExpiry < DateTime.UtcNow)
            {
                return new VerificationResult
                {
                    Success = false,
                    Message = "Invalid or expired verification token."
                };
            }

            user.IsVerified = true;
            user.VerificationToken = null;
            user.VerificationTokenExpiry = null;
            await _userRepository.UpdateAsync(user);

            return new VerificationResult
            {
                Success = true,
                Message = "Email verified successfully.",
                IsAdmin = user.IdRole == 3 // Role ID 3 represents Admin
            };
        }

        public async Task<VerificationResult> ResendVerificationEmailAsync(string email)
        {
            var user = await _userRepository.GetByEmailAsync(email);

            // If user doesn't exist or is already verified, return success but don't send email
            // This prevents user enumeration attacks
            if (user == null)
            {
                return new VerificationResult
                {
                    Success = true,
                    Message = "If the email is registered and not verified, a new verification email has been sent."
                };
            }

            if (user.IsVerified)
            {
                return new VerificationResult
                {
                    Success = false,
                    Message = "This email is already verified."
                };
            }

            // Check if the last verification email was sent less than 1 minute ago
            if (user.VerificationTokenExpiry != null &&
                user.VerificationTokenExpiry > DateTime.UtcNow.AddMinutes(-1))
            {
                return new VerificationResult
                {
                    Success = false,
                    Message = "Please wait at least 1 minute before requesting another verification email."
                };
            }

            // Generate a new verification token
            user.VerificationToken = Guid.NewGuid().ToString();
            user.VerificationTokenExpiry = DateTime.UtcNow.AddHours(24);
            await _userRepository.UpdateAsync(user);

            // Get backend URL from environment variables or use default
            string? backendBaseUrl = Environment.GetEnvironmentVariable("BACKEND_BASE_URL");
            if (string.IsNullOrEmpty(backendBaseUrl))
            {
                backendBaseUrl = _configuration["AppSettings:BackendBaseUrl"];
                if (string.IsNullOrEmpty(backendBaseUrl) || backendBaseUrl?.Contains("${") == true)
                {
                    backendBaseUrl = "http://localhost:5012";
                }
            }

            _logger.LogInformation($"Using backend URL for verification link: {backendBaseUrl}");
            string verificationLink = $"{backendBaseUrl}/api/auth/verify-email?email={user.Email}&token={user.VerificationToken}";

            await _emailService.SendVerificationEmailAsync(user.Email, verificationLink);

            return new VerificationResult
            {
                Success = true,
                Message = "A new verification email has been sent."
            };
        }

        public async Task<LoginResponse> LoginAsync(LoginRequest request)
        {
            var user = await _userRepository.GetByEmailAsync(request.Email);
            if (user == null)
                throw new UnauthorizedAccessException("Invalid credentials");

            // Lockout check (new)
            if (user.IsLockedOut)
            {
                _logger.LogWarning($"Account locked for {request.Email} until {user.LockoutEnd}");
                throw new UnauthorizedAccessException("Account locked. Try again later.");
            }

            if (!user.IsVerified || !BCrypt.Net.BCrypt.Verify(request.Password, user.PasswordHash))
            {
                user.FailedLoginAttempts++;

                // Auto-lock after 5 failures (new)
                if (user.FailedLoginAttempts >= 5)
                {
                    user.LockoutEnd = DateTime.UtcNow.AddMinutes(15);
                    await _userRepository.UpdateAsync(user);
                    _logger.LogWarning($"Auto-lock triggered for {request.Email}");
                    throw new UnauthorizedAccessException("Too many attempts. Account locked for 15 minutes.");
                }

                await _userRepository.UpdateAsync(user);
                throw new UnauthorizedAccessException("Invalid credentials");
            }

            // Reset counters on success
            user.FailedLoginAttempts = 0;
            user.LockoutEnd = null;
            await _userRepository.UpdateAsync(user);

            _logger.LogInformation("LoginAsync: Checking user role and 2FA status. IdRole={IdRole}, IsTwoFactorEnabled={IsTwoFactorEnabled}", user.IdRole, user.IsTwoFactorEnabled);

            // Check if user is admin and 2FA is enabled
            if (user.IdRole == 3 && user.IsTwoFactorEnabled)
            {
                // Admin's first login attempt - generate and send OTP
                string otp = await GenerateAndSendOtpAsync(user);

                _logger.LogInformation("LoginAsync: Generated OTP {Otp} for admin user {Email}", otp, user.Email);

                return new LoginResponse
                {
                    RequiresTwoFactor = true,
                    Message = "2FA required. OTP sent to your email.",
                    UserId = user.Id,
                    Email = user.Email,
                    Username = user.Username,
                    Role = (UserRole)user.IdRole
                };
            }

            _logger.LogInformation("LoginAsync: User does not require 2FA. IdRole={IdRole}", user.IdRole);

            // Regular user login - generate tokens
            var (accessToken, refreshToken) = await _jwtService.GenerateTokensAsync(user);

            // Save refresh token to user in database and get the saved token
            var savedRefreshToken = await _refreshTokenService.GenerateRefreshTokenAsync(user.Id);

            // Reload user from database to get updated RefreshToken and RefreshTokenExpiry
            user = await _userRepository.GetByIdAsync(user.Id);

            // Log tokens for debugging
            _logger.LogInformation($"LoginAsync: Generated tokens for user {user?.Id} - AccessToken: {accessToken}, RefreshToken: {savedRefreshToken}");

            if (user == null)
            {
                throw new InvalidOperationException("User not found after refresh token generation");
            }

            return new LoginResponse
            {
                Token = accessToken,
                RefreshToken = savedRefreshToken,
                RequiresTwoFactor = false,
                Role = (UserRole)user.IdRole,
                Message = "Login successful.",
                UserId = user.Id,
                Email = user.Email,
                Username = user.Username
            };
        }

        public async Task RequestPasswordResetAsync(string email)
        {
            var user = await _userRepository.GetByEmailAsync(email);
            if (user == null)
            {
                _logger.LogWarning("Password reset requested for non-existent email: {Email}", email);
                return;
            }

            string resetOtp = GenerateVerificationCode();
            user.PasswordResetToken = resetOtp;
            user.PasswordResetTokenExpiry = DateTime.UtcNow.AddMinutes(10);

            await _userRepository.UpdateAsync(user);
            await _emailService.SendPasswordResetEmail(user.Email, resetOtp);
        }

        public async Task<OtpVerificationResult> VerifyOtpAsync(string email, string otp)
        {
            var user = await _userRepository.GetByEmailAsync(email);
            if (user == null)
            {
                _logger.LogWarning("VerifyOtpAsync: User not found for email {Email}", email);
                return new OtpVerificationResult { IsValid = false };
            }

            string trimmedOtp = otp.Trim();
            string storedOtpAdmin = user.Otp?.Trim() ?? string.Empty;
            string storedOtpReset = user.PasswordResetToken?.Trim() ?? string.Empty;

            _logger.LogInformation("VerifyOtpAsync: Verifying OTP for user {Email}. Provided OTP: {Otp}, Stored Admin OTP: {StoredOtpAdmin}, Stored Reset OTP: {StoredOtpReset}, OTP Expiry: {OtpExpiry}, Password Reset Expiry: {PasswordResetExpiry}",
                email, trimmedOtp, storedOtpAdmin, storedOtpReset, user.OtpExpiry, user.PasswordResetTokenExpiry);

            // For admin login OTP verification
            if (user.IdRole == 3)
            {
                bool isExpired = user.OtpExpiry < DateTime.UtcNow;
                bool isValid = storedOtpAdmin.Equals(trimmedOtp, System.StringComparison.OrdinalIgnoreCase) && !isExpired;

                _logger.LogInformation("VerifyOtpAsync: Admin OTP validation result: IsValid={IsValid}, IsExpired={IsExpired}", isValid, isExpired);

                if (!isValid)
                {
                    _logger.LogWarning("VerifyOtpAsync: Admin OTP verification failed. Provided OTP: {Otp}, Stored OTP: {StoredOtpAdmin}, Expiry: {OtpExpiry}, Current Time: {Now}",
                        trimmedOtp, storedOtpAdmin, user.OtpExpiry, DateTime.UtcNow);
                }

                return new OtpVerificationResult
                {
                    IsValid = isValid,
                    IsExpired = isExpired
                };
            }
            // For password reset OTP verification
            else
            {
                bool isExpired = user.PasswordResetTokenExpiry < DateTime.UtcNow;
                bool isValid = storedOtpReset.Equals(trimmedOtp, System.StringComparison.OrdinalIgnoreCase) && !isExpired;

                _logger.LogInformation("VerifyOtpAsync: Password reset OTP validation result: IsValid={IsValid}, IsExpired={IsExpired}", isValid, isExpired);

                if (!isValid)
                {
                    _logger.LogWarning("VerifyOtpAsync: Password reset OTP verification failed. Provided OTP: {Otp}, Stored OTP: {StoredOtpReset}, Expiry: {PasswordResetExpiry}, Current Time: {Now}",
                        trimmedOtp, storedOtpReset, user.PasswordResetTokenExpiry, DateTime.UtcNow);
                }

                return new OtpVerificationResult
                {
                    IsValid = isValid,
                    IsExpired = isExpired
                };
            }

        }

        public async Task<bool> ResetPasswordAsync(string email, string newPassword)
        {
            var user = await _userRepository.GetByEmailAsync(email);
            if (user == null || user.PasswordResetToken == null || user.PasswordResetTokenExpiry < DateTime.UtcNow)
            {
                return false;
            }

            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
            user.PasswordResetToken = null;
            user.PasswordResetTokenExpiry = null;

            await _userRepository.UpdateAsync(user);
            return true;
        }
        public async Task<RegisterResponse> RegisterAdminAsync(AdminRegisterRequest request, string secretKey)
        {
            if (!IsPasswordStrong(request.Password))
            {
                return new RegisterResponse
                {
                    Success = false,
                    Message = "Password does not meet strength requirements."
                };
            }

            // Validate admin registration secret key
            string configuredSecretKey = _configuration["AdminSettings:SecretKey"] ?? string.Empty;
            if (string.IsNullOrEmpty(configuredSecretKey) || request.SecretKey != configuredSecretKey)
            {
                return new RegisterResponse
                {
                    Success = false,
                    Message = "Unauthorized admin registration attempt."
                };
            }

            var existingUser = await _userRepository.GetByEmailAsync(request.Email);
            if (existingUser != null)
            {
                return new RegisterResponse
                {
                    Success = false,
                    Message = "Email is already registered"
                };
            }

            var newAdmin = new User
            {
                Username = request.Username,
                Email = request.Email,
                PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.Password),
                VerificationToken = GenerateVerificationCode(),
                VerificationTokenExpiry = DateTime.UtcNow.AddHours(24),
                IsVerified = false,
                BillingCycle = "monthly", // Default billing cycle for admin
                IdRole = 3, // Admin role
                IsTwoFactorEnabled = true, // Enable 2FA for admins by default
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _userRepository.CreateAsync(newAdmin);

            // Get the backend URL directly from environment variables
            string? backendBaseUrl = Environment.GetEnvironmentVariable("BACKEND_BASE_URL");

            // If environment variable is not set, try configuration or use default
            if (string.IsNullOrEmpty(backendBaseUrl))
            {
                backendBaseUrl = _configuration["AppSettings:BackendBaseUrl"];

                // If still null or contains placeholder, use default
                if (string.IsNullOrEmpty(backendBaseUrl) || backendBaseUrl?.Contains("${") == true)
                {
                    backendBaseUrl = "http://localhost:5012";
                }
            }

            _logger.LogInformation($"Using backend URL for admin verification link: {backendBaseUrl}");
            string verificationLink = $"{backendBaseUrl}/api/auth/verify-email?email={newAdmin.Email}&token={newAdmin.VerificationToken}";

            await _emailService.SendVerificationEmailAsync(newAdmin.Email, verificationLink);

            return new RegisterResponse
            {
                Success = true,
                Message = "Admin registration successful. Check your email for verification.",
                UserId = newAdmin.Id,
                RequiresVerification = true,
                Plan = "admin",
                BillingCycle = "monthly"
            };
        }

        private async Task<string> GenerateAndSendOtpAsync(User user)
        {
            string otp = GenerateVerificationCode();
            user.Otp = otp;
            user.OtpExpiry = DateTime.UtcNow.AddMinutes(10);
            await _userRepository.UpdateAsync(user);

            // Use the admin OTP email template for admin users
            if (user.IdRole == 3)
            {
                await _emailService.SendAdminOtpEmail(user.Email, otp);
            }
            else
            {
                await _emailService.SendOtpEmail(user.Email, otp);
            }

            return otp;
        }

        private string GenerateVerificationCode()
        {
            using var rng = RandomNumberGenerator.Create();
            byte[] randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            return (Math.Abs(BitConverter.ToInt32(randomBytes, 0)) % 900000 + 100000).ToString();
        }

        private string HashToken(string token)
        {
            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var bytes = System.Text.Encoding.UTF8.GetBytes(token);
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToBase64String(hash);
        }

        public async Task<Guid?> GetUserIdFromRefreshTokenAsync(string refreshToken)
        {
            var hashedToken = HashToken(refreshToken);

            var user = await _context.Users
                .AsNoTracking()
                .Where(u => u.RefreshToken == hashedToken && u.RefreshTokenExpiry > DateTime.UtcNow)
                .Select(u => new { u.Id })
                .FirstOrDefaultAsync();

            if (user == null)
            {
                _logger.LogWarning($"GetUserIdFromRefreshTokenAsync: No user found with valid refresh token: {refreshToken}");
                return null;
            }

            _logger.LogInformation($"GetUserIdFromRefreshTokenAsync: Valid refresh token for user {user.Id}");
            return user.Id;
        }

        public async Task<User?> GetUserByIdAsync(Guid userId)
        {
            return await _userRepository.GetByIdAsync(userId);
        }
        public async Task UpdateRefreshTokenAsync(Guid userId, string refreshToken, DateTime refreshTokenExpiry)
        {
            var user = await _userRepository.GetByIdAsync(userId);
            if (user != null)
            {
                user.RefreshToken = refreshToken;
                user.RefreshTokenExpiry = refreshTokenExpiry;
                await _userRepository.UpdateAsync(user);
            }

        }

        private bool IsPasswordStrong(string password)
        {
            var hasUpperChar = new System.Text.RegularExpressions.Regex(@"[A-Z]+");
            var hasLowerChar = new System.Text.RegularExpressions.Regex(@"[a-z]+");
            var hasMiniMaxChars = new System.Text.RegularExpressions.Regex(@".{8,15}");
            var hasNumber = new System.Text.RegularExpressions.Regex(@"[0-9]+");
            var hasSymbols = new System.Text.RegularExpressions.Regex(@"[!@#$%^&*]+");

            return hasMiniMaxChars.IsMatch(password) &&
                   hasUpperChar.IsMatch(password) &&
                   hasLowerChar.IsMatch(password) &&
                   hasNumber.IsMatch(password) &&
                   hasSymbols.IsMatch(password);
        }

        // Admin functionality methods
        public async Task<List<User>> GetAllUsersAsync()
        {
            return await _context.Users
                .AsNoTracking()
                .OrderByDescending(u => u.CreatedAt)
                .ToListAsync();
        }

        public async Task<User?> GetUserByIdAsync(string id)
        {
            if (!Guid.TryParse(id, out Guid userId))
            {
                return null;
            }
            return await _userRepository.GetByIdAsync(userId);
        }

        public async Task<bool> UpdateUserRoleAsync(string id, int newRole)
        {
            if (!Guid.TryParse(id, out Guid userId))
            {
                return false;
            }

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            // Only allow changing between free (1) and premium (2) roles
            if (newRole != 1 && newRole != 2)
            {
                return false;
            }

            user.IdRole = newRole;
            user.UpdatedAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);
            return true;
        }

        public async Task<bool> UpdateUserStatusAsync(string id, bool isActive)
        {
            if (!Guid.TryParse(id, out Guid userId))
            {
                return false;
            }

            var user = await _userRepository.GetByIdAsync(userId);
            if (user == null)
            {
                return false;
            }

            // Set or clear lockout based on isActive flag
            if (isActive)
            {
                user.LockoutEnd = null;
                user.FailedLoginAttempts = 0;
            }
            else
            {
                user.LockoutEnd = DateTime.UtcNow.AddYears(100); // Effectively permanent until manually reactivated
            }

            user.UpdatedAt = DateTime.UtcNow;
            await _userRepository.UpdateAsync(user);
            return true;
        }

        public async Task<PlatformStatsResponse> GetPlatformStatsAsync()
        {
            // Get user counts
            var userCounts = await _context.Users
                .GroupBy(u => u.IdRole)
                .Select(g => new { Role = g.Key, Count = g.Count() })
                .ToListAsync();

            int totalUsers = userCounts.Sum(x => x.Count);
            int freeUsers = userCounts.FirstOrDefault(x => x.Role == 1)?.Count ?? 0;
            int premiumUsers = userCounts.FirstOrDefault(x => x.Role == 2)?.Count ?? 0;
            int adminUsers = userCounts.FirstOrDefault(x => x.Role == 3)?.Count ?? 0;

            // Get new registrations in the last month
            var lastMonth = DateTime.UtcNow.AddMonths(-1);
            int newRegistrations = await _context.Users
                .Where(u => u.CreatedAt >= lastMonth)
                .CountAsync();

            // Get real scan data from the ScanHistories table
            int totalScans = await _context.ScanHistories.CountAsync();
            int securityScans = await _context.ScanHistories
                .Where(s => s.ScanType.ToLower() == "security")
                .CountAsync();
            int seoScans = await _context.ScanHistories
                .Where(s => s.ScanType.ToLower() == "seo")
                .CountAsync();

            // Calculate average scans per user
            double averageScansPerUser = totalUsers > 0 ? (double)totalScans / totalUsers : 0;

            return new PlatformStatsResponse
            {
                TotalUsers = totalUsers,
                FreeUsers = freeUsers,
                PremiumUsers = premiumUsers,
                AdminUsers = adminUsers,
                TotalScans = totalScans,
                SecurityScans = securityScans,
                SeoScans = seoScans,
                RegistrationsLastMonth = newRegistrations,
                AverageScansPerUser = averageScansPerUser
            };
        }
    }
}
