import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON>ag } from "@/types/report";
import {
  Check,
  X,
  AlertTriangle,
  Info,
  HelpCircle,
  Lock,
  Globe,
  ChevronRight,
  Shield,
  Database,
  FileText,
  Smartphone,
  ArrowRight,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// InfoTooltip Component - Reusable tooltip for technical terms (more compact)
const InfoTooltip: React.FC<{ term: string; explanation: string }> = ({
  term,
  explanation,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-flex items-center cursor-help">
          {term}
          <HelpCircle className="h-3 w-3 ml-0.5 text-gray-400" />
        </span>
      </TooltipTrigger>
      <TooltipContent className="max-w-xs bg-gray-800 text-white border-gray-700 p-2">
        <p className="text-xs">{explanation}</p>
      </TooltipContent>
    </Tooltip>
  );
};

// Section Introduction Component
const SectionIntro: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="mb-3 p-2 neo-card bg-gray-50/50 border-l-4 border-blue-500/50">
      <div className="text-xs text-gray-600">{children}</div>
    </div>
  );
};

// Security Impact Component
const SecurityImpact: React.FC<{
  severity: "high" | "medium" | "low";
  children: React.ReactNode;
}> = ({ severity, children }) => {
  const bgColor =
    severity === "high"
      ? "bg-red-500/10 border-red-500/30"
      : severity === "medium"
      ? "bg-yellow-500/10 border-yellow-500/30"
      : "bg-blue-500/10 border-blue-500/30";

  const icon =
    severity === "high" ? (
      <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
    ) : severity === "medium" ? (
      <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />
    ) : (
      <Info className="h-4 w-4 text-blue-500 flex-shrink-0" />
    );

  return (
    <div className={`p-2 rounded-lg flex items-start gap-2 ${bgColor}`}>
      {icon}
      <p className="text-xs text-gray-700">{children}</p>
    </div>
  );
};

// SSL Details Component
export const SslDetails: React.FC<{ details?: SslDetail }> = ({ details }) => {
  if (!details) {
    return <p className="text-gray-600">Aucune donnée SSL disponible.</p>;
  }

  // Calculate days until expiration
  const daysUntilExpiration = (() => {
    try {
      const expiryDate = new Date(details.expirationDate);
      const today = new Date();
      const diffTime = expiryDate.getTime() - today.getTime();
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    } catch (e) {
      return null;
    }
  })();

  // Determine expiration warning level
  const getExpirationWarning = () => {
    if (daysUntilExpiration === null) return null;
    if (daysUntilExpiration <= 7) return "critical";
    if (daysUntilExpiration <= 30) return "warning";
    return "good";
  };

  const expirationWarning = getExpirationWarning();

  return (
    <div className="space-y-4">
      <SectionIntro>
        <div className="flex items-center gap-2 mb-2">
          <span className="text-blue-600 font-semibold flex items-center">
            <Lock className="w-5 h-5 mr-1.5" />
            Passeport Numérique de Votre Site
          </span>
        </div>
        Le certificat SSL/TLS est comme le passeport de votre site web. Il
        vérifie son identité auprès des navigateurs et chiffre toutes les
        données échangées avec vos visiteurs, protégeant ainsi les informations
        sensibles.
      </SectionIntro>

      {/* Passport Verification Flow - Fixed layout */}
      <div className="grid grid-cols-3 gap-1 p-2 neo-card bg-gradient-to-r from-gray-50 to-blue-50">
        <div className="flex flex-col items-center text-center">
          <div className="p-1 rounded-full bg-blue-100 mb-0.5">
            <Globe className="w-3 h-3 text-blue-600" />
          </div>
          <div className="text-xs font-medium">Domaine</div>
          <div className="text-xs text-gray-600 truncate w-full">
            {details.certificateType}
          </div>
        </div>

        <div className="flex flex-col items-center text-center">
          <div className="p-1 rounded-full bg-blue-100 mb-0.5">
            <Lock className="w-3 h-3 text-blue-600" />
          </div>
          <div className="text-xs font-medium">Chiffrement</div>
          <div className="text-xs text-gray-600">
            {details.cipherStrength} bits
          </div>
        </div>

        <div className="flex flex-col items-center text-center">
          <div className="p-1 rounded-full bg-green-100 mb-0.5">
            <Check className="w-3 h-3 text-green-600" />
          </div>
          <div className="text-xs font-medium">Confiance</div>
          <div className="text-xs text-gray-600 truncate w-full">
            Émis par {details.issuer.split(",")[0]}
          </div>
        </div>
      </div>

      {/* Expiration Warning System - Horizontal layout */}
      {expirationWarning && (
        <div
          className={`p-1 neo-card ${
            expirationWarning === "critical"
              ? "bg-red-50 border-l-4 border-red-500"
              : expirationWarning === "warning"
              ? "bg-yellow-50 border-l-4 border-yellow-500"
              : "bg-green-50 border-l-4 border-green-500"
          }`}
        >
          <div className="flex items-center gap-1">
            <div
              className={`p-1 rounded-full flex-shrink-0 ${
                expirationWarning === "critical"
                  ? "bg-red-100"
                  : expirationWarning === "warning"
                  ? "bg-yellow-100"
                  : "bg-green-100"
              }`}
            >
              {expirationWarning === "critical" ? (
                <AlertTriangle className="w-3 h-3 text-red-500" />
              ) : expirationWarning === "warning" ? (
                <AlertTriangle className="w-3 h-3 text-yellow-500" />
              ) : (
                <Check className="w-3 h-3 text-green-500" />
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <h4
                  className={`font-medium text-xs truncate ${
                    expirationWarning === "critical"
                      ? "text-red-700"
                      : expirationWarning === "warning"
                      ? "text-yellow-700"
                      : "text-green-700"
                  }`}
                >
                  {expirationWarning === "critical" ? "URGENT: " : ""}
                  Certificat SSL expire{" "}
                  {daysUntilExpiration <= 0
                    ? "aujourd'hui"
                    : `dans ${daysUntilExpiration} jours`}
                </h4>

                <span
                  className={`ml-auto text-xs ${
                    expirationWarning === "critical"
                      ? "text-red-600"
                      : expirationWarning === "warning"
                      ? "text-yellow-600"
                      : "text-green-600"
                  }`}
                >
                  {expirationWarning === "critical"
                    ? "Risque sécurité"
                    : expirationWarning === "warning"
                    ? "Renouveler bientôt"
                    : "Valide"}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Health Check - Grid layout for horizontal space */}
      <div className="p-1 neo-card bg-white">
        <h4 className="font-medium text-gray-800 mb-1 text-xs">
          Bilan de Santé SSL
        </h4>
        <div className="grid grid-cols-2 gap-1">
          <div
            className={`flex items-center p-1 rounded ${
              details.supportsTls13 ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-1 flex-shrink-0">
              {details.supportsTls13 ? (
                <span className="text-green-600 text-xs">✓</span>
              ) : (
                <span className="text-red-600 text-xs">✗</span>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex justify-between">
                <span className="text-xs font-medium">TLS 1.3</span>
                <span className="text-xs text-gray-500 ml-1">
                  {details.supportsTls13 ? "Activé" : "Non activé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 truncate">
                {details.supportsTls13
                  ? "Chiffrement moderne"
                  : "Version obsolète"}
              </p>
            </div>
          </div>

          <div
            className={`flex items-center p-1 rounded ${
              details.hstsEnabled ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-1 flex-shrink-0">
              {details.hstsEnabled ? (
                <span className="text-green-600 text-xs">✓</span>
              ) : (
                <span className="text-red-600 text-xs">✗</span>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex justify-between">
                <span className="text-xs font-medium">HSTS</span>
                <span className="text-xs text-gray-500 ml-1">
                  {details.hstsEnabled ? "Activé" : "Non activé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 truncate">
                {details.hstsEnabled
                  ? "Force HTTPS"
                  : "Vulnérable aux attaques"}
              </p>
            </div>
          </div>

          <div
            className={`flex items-center p-1 rounded ${
              details.ocspStapling ? "bg-green-50" : "bg-yellow-50"
            }`}
          >
            <div className="mr-1 flex-shrink-0">
              {details.ocspStapling ? (
                <span className="text-green-600 text-xs">✓</span>
              ) : (
                <span className="text-yellow-600 text-xs">!</span>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex justify-between">
                <span className="text-xs font-medium">OCSP Stapling</span>
                <span className="text-xs text-gray-500 ml-1">
                  {details.ocspStapling ? "Activé" : "Non activé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 truncate">
                {details.ocspStapling
                  ? "Vérification rapide"
                  : "Chargement lent"}
              </p>
            </div>
          </div>

          <div
            className={`flex items-center p-1 rounded ${
              details.forwardSecrecy ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-1 flex-shrink-0">
              {details.forwardSecrecy ? (
                <span className="text-green-600 text-xs">✓</span>
              ) : (
                <span className="text-red-600 text-xs">✗</span>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex justify-between">
                <span className="text-xs font-medium">Forward Secrecy</span>
                <span className="text-xs text-gray-500 ml-1">
                  {details.forwardSecrecy ? "Activé" : "Non activé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 truncate">
                {details.forwardSecrecy
                  ? "Protection des données"
                  : "Risque de déchiffrement"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* One-Click Fix Guide - Horizontal layout */}
      {(!details.forwardSecrecy ||
        !details.hstsEnabled ||
        !details.ocspStapling ||
        !details.supportsTls13) && (
        <div className="p-1 neo-card bg-blue-50">
          <h4 className="font-medium text-blue-800 mb-1 text-xs">
            Guide de Correction Rapide
          </h4>

          <div className="grid grid-cols-2 gap-1">
            {!details.hstsEnabled && (
              <div className="mb-1">
                <h5 className="text-xs font-medium text-blue-700">
                  Activer HSTS
                </h5>
                <div className="bg-gray-800 text-green-400 p-1 rounded text-xs font-mono overflow-x-auto">
                  <pre className="text-xs">
                    # .htaccess:
                    <br />
                    Header set Strict-Transport-Security "max-age=31536000"
                  </pre>
                </div>
              </div>
            )}

            {!details.supportsTls13 && (
              <div className="mb-1">
                <h5 className="text-xs font-medium text-blue-700">
                  Activer TLS 1.3
                </h5>
                <div className="bg-gray-800 text-green-400 p-1 rounded text-xs font-mono overflow-x-auto">
                  <pre className="text-xs">
                    # Apache:
                    <br />
                    SSLProtocol -all +TLSv1.2 +TLSv1.3
                  </pre>
                </div>
              </div>
            )}
          </div>

          <p className="text-xs text-blue-600 mt-1">
            <strong>Note:</strong> Contactez votre hébergeur pour activer ces
            fonctionnalités.
          </p>
        </div>
      )}

      {/* Business Impact Section - Horizontal grid layout */}
      <div className="p-1 neo-card bg-gradient-to-r from-gray-50 to-gray-100">
        <h4 className="font-medium text-gray-800 mb-1 text-xs">
          Impact sur votre Activité
        </h4>
        <div className="grid grid-cols-3 gap-1">
          <div className="flex items-start gap-1 p-1">
            <div className="text-blue-500 text-sm flex-shrink-0">🛡️</div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">Protection</h5>
              <p className="text-xs text-gray-600 truncate">
                Sécurise les données sensibles
              </p>
            </div>
          </div>

          <div className="flex items-start gap-1 p-1">
            <div className="text-blue-500 text-sm flex-shrink-0">⭐</div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">Confiance</h5>
              <p className="text-xs text-gray-600 truncate">
                Augmente les conversions
              </p>
            </div>
          </div>

          <div className="flex items-start gap-1 p-1">
            <div className="text-blue-500 text-sm flex-shrink-0">📈</div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">SEO</h5>
              <p className="text-xs text-gray-600 truncate">
                Améliore le positionnement
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// HTTP Headers Component
export const HeaderTable: React.FC<{ headers?: HttpHeader[] }> = ({
  headers,
}) => {
  if (!headers || headers.length === 0) {
    return (
      <p className="text-gray-600">Aucune donnée d'en-têtes HTTP disponible.</p>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <Check className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
      case "bad":
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  // We don't need these variables anymore as we're using a different approach
  // for displaying headers in the new UI

  // Get overall security headers score
  const securityHeadersScore =
    headers.find((h) => h.name === "Security Headers Score")?.value || "0/100";
  const scoreValue = parseInt(securityHeadersScore.split("/")[0]) || 0;

  // Common security headers with explanations and examples
  const headerExplanations: Record<
    string,
    { explanation: string; example: string; protection: string }
  > = {
    "Content-Security-Policy": {
      explanation:
        "Définit quelles sources de contenu sont autorisées à s'exécuter sur votre site",
      example: "default-src 'self'; script-src 'self' trusted-scripts.com",
      protection: "Bloque les scripts malveillants injectés (XSS)",
    },
    "X-Content-Type-Options": {
      explanation:
        "Empêche les navigateurs de deviner le type MIME des ressources",
      example: "nosniff",
      protection: "Bloque les attaques MIME-sniffing",
    },
    "X-Frame-Options": {
      explanation: "Contrôle si votre site peut être affiché dans un iframe",
      example: "DENY ou SAMEORIGIN",
      protection: "Empêche le clickjacking (piratage par clic)",
    },
    "Strict-Transport-Security": {
      explanation: "Force les connexions HTTPS pour votre domaine",
      example: "max-age=31536000; includeSubDomains",
      protection: "Bloque les attaques de déclassement HTTPS",
    },
    "Referrer-Policy": {
      explanation:
        "Contrôle les informations de référence envoyées lors de la navigation",
      example: "no-referrer-when-downgrade",
      protection: "Limite les fuites d'information",
    },
    "Permissions-Policy": {
      explanation:
        "Contrôle quelles fonctionnalités du navigateur peuvent être utilisées",
      example: "camera=(), microphone=()",
      protection: "Limite l'accès aux fonctionnalités sensibles",
    },
  };

  // Helper function to get explanation for a header
  const getHeaderExplanation = (name: string) => {
    return (
      headerExplanations[name]?.explanation ||
      "En-tête HTTP qui affecte le comportement du navigateur ou du serveur."
    );
  };

  // Get missing critical headers
  const missingCriticalHeaders = [
    "Content-Security-Policy",
    "Strict-Transport-Security",
    "X-Content-Type-Options",
    "X-Frame-Options",
  ].filter(
    (name) =>
      !headers.some(
        (h) =>
          h.name === name &&
          !["bad", "warning", "error"].includes(h.status as string)
      )
  );

  return (
    <div className="space-y-4">
      <SectionIntro>
        <div className="flex items-center gap-2 mb-2">
          <span className="text-blue-600 font-semibold flex items-center">
            <Shield className="w-5 h-5 mr-1.5" />
            Agents de Sécurité de Votre Site
          </span>
        </div>
        Les en-têtes HTTP sont comme des agents de sécurité qui contrôlent ce
        qui entre et sort de votre site web. Ils donnent des instructions aux
        navigateurs pour bloquer diverses attaques et protéger vos visiteurs.
      </SectionIntro>

      {/* Security Guard Analogy - More compact */}
      <div className="p-2 neo-card bg-gradient-to-r from-gray-50 to-blue-50">
        <h4 className="font-medium text-gray-800 mb-2 text-sm">
          Rôles des Agents de Sécurité
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div className="flex items-start gap-2 p-2 bg-white rounded-lg shadow-sm">
            <div className="p-1 rounded bg-red-100">
              <X className="w-3 h-3 text-red-600" />
            </div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">
                Content-Security-Policy
              </h5>
              <p className="text-xs text-gray-600">
                Vérifie les identités (Bloque les scripts malveillants)
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2 p-2 bg-white rounded-lg shadow-sm">
            <div className="p-1 rounded bg-blue-100">
              <Lock className="w-3 h-3 text-blue-600" />
            </div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">
                X-Frame-Options
              </h5>
              <p className="text-xs text-gray-600">
                Interdit les copies (Empêche le clickjacking)
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2 p-2 bg-white rounded-lg shadow-sm">
            <div className="p-1 rounded bg-yellow-100">
              <AlertTriangle className="w-3 h-3 text-yellow-600" />
            </div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">HSTS</h5>
              <p className="text-xs text-gray-600">
                VIP uniquement (Force HTTPS)
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2 p-2 bg-white rounded-lg shadow-sm">
            <div className="p-1 rounded bg-blue-100">
              <Info className="w-3 h-3 text-blue-600" />
            </div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">
                Referrer-Policy
              </h5>
              <p className="text-xs text-gray-600">
                Contrôle les informations partagées
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Security Score Card - More compact */}
      <div className="p-2 neo-card bg-white">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium text-gray-800 text-sm">
            Score de Sécurité des En-têtes
          </h4>
          <div className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-xs ${
                scoreValue >= 80
                  ? "bg-green-500"
                  : scoreValue >= 50
                  ? "bg-yellow-500"
                  : "bg-red-500"
              }`}
            >
              {scoreValue}
            </div>
            <span className="ml-1 text-xs text-gray-600">/100</span>
          </div>
        </div>

        <div className="h-1.5 bg-gray-200 rounded-full overflow-hidden mb-2">
          <div
            className={`h-full ${
              scoreValue >= 80
                ? "bg-green-500"
                : scoreValue >= 50
                ? "bg-yellow-500"
                : "bg-red-500"
            }`}
            style={{ width: `${scoreValue}%` }}
          ></div>
        </div>

        <p className="text-xs text-gray-600 mb-1">
          {scoreValue >= 80
            ? "Excellent! Votre site a une bonne configuration des en-têtes de sécurité."
            : scoreValue >= 50
            ? "Moyen. Plusieurs en-têtes de sécurité importants sont manquants."
            : "Faible. La plupart des en-têtes de sécurité essentiels sont manquants."}
        </p>

        {scoreValue < 80 && (
          <p className="text-xs text-gray-500">
            L'ajout des en-têtes manquants peut améliorer considérablement votre
            score et la sécurité de votre site.
          </p>
        )}
      </div>

      {/* Priority Matrix - More compact */}
      <div className="p-2 neo-card bg-white">
        <h4 className="font-medium text-gray-800 mb-2 text-sm">
          Matrice de Priorité
        </h4>
        <div className="overflow-x-auto">
          <table className="w-full text-xs">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 py-1 text-left font-medium text-gray-500 uppercase tracking-wider">
                  En-tête
                </th>
                <th className="px-2 py-1 text-left font-medium text-gray-500 uppercase tracking-wider">
                  Protection
                </th>
                <th className="px-2 py-1 text-left font-medium text-gray-500 uppercase tracking-wider">
                  Statut
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {[
                "Content-Security-Policy",
                "Strict-Transport-Security",
                "X-Content-Type-Options",
                "X-Frame-Options",
                "Referrer-Policy",
                "Permissions-Policy",
              ].map((headerName) => {
                const header = headers.find((h) => h.name === headerName);
                // Check if the header is present and has a positive status
                const isPresent =
                  header &&
                  !["bad", "warning", "error"].includes(
                    header.status as string
                  );

                return (
                  <tr key={headerName} className="hover:bg-gray-50">
                    <td className="px-2 py-1">
                      <div className="flex items-center">
                        <InfoTooltip
                          term={headerName}
                          explanation={
                            headerExplanations[headerName]?.explanation ||
                            getHeaderExplanation(headerName)
                          }
                        />
                      </div>
                    </td>
                    <td className="px-2 py-1">
                      <span
                        className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                          headerName === "Content-Security-Policy" ||
                          headerName === "Strict-Transport-Security"
                            ? "bg-red-100 text-red-800"
                            : headerName === "X-Content-Type-Options" ||
                              headerName === "X-Frame-Options"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-blue-100 text-blue-800"
                        }`}
                      >
                        {headerName === "Content-Security-Policy" ||
                        headerName === "Strict-Transport-Security"
                          ? "Critique"
                          : headerName === "X-Content-Type-Options" ||
                            headerName === "X-Frame-Options"
                          ? "Important"
                          : "Recommandé"}
                      </span>
                    </td>
                    <td className="px-2 py-1">
                      <span
                        className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${
                          isPresent
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {isPresent ? "✓ Présent" : "❌ Manquant"}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* One-Click Fixes - More compact */}
      {missingCriticalHeaders.length > 0 && (
        <div className="p-2 neo-card bg-blue-50">
          <h4 className="font-medium text-blue-800 mb-2 text-sm">
            Solutions Rapides
          </h4>

          {missingCriticalHeaders.includes("Content-Security-Policy") && (
            <div className="mb-2">
              <h5 className="text-xs font-medium text-blue-700 mb-1">
                Content-Security-Policy
              </h5>
              <div className="bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                <pre className="text-xs">
                  # .htaccess (Apache):
                  <br />
                  Header set Content-Security-Policy "default-src 'self';
                  script-src 'self' 'unsafe-inline' *.googleapis.com"
                </pre>
              </div>
            </div>
          )}

          {missingCriticalHeaders.includes("Strict-Transport-Security") && (
            <div className="mb-2">
              <h5 className="text-xs font-medium text-blue-700 mb-1">
                Strict-Transport-Security (HSTS)
              </h5>
              <div className="bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                <pre className="text-xs">
                  # .htaccess (Apache):
                  <br />
                  Header set Strict-Transport-Security "max-age=31536000;
                  includeSubDomains"
                </pre>
              </div>
            </div>
          )}

          {missingCriticalHeaders.includes("X-Frame-Options") && (
            <div className="mb-2">
              <h5 className="text-xs font-medium text-blue-700 mb-1">
                X-Frame-Options
              </h5>
              <div className="bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                <pre className="text-xs">
                  # .htaccess (Apache):
                  <br />
                  Header set X-Frame-Options "SAMEORIGIN"
                </pre>
              </div>
            </div>
          )}

          {missingCriticalHeaders.includes("X-Content-Type-Options") && (
            <div className="mb-2">
              <h5 className="text-xs font-medium text-blue-700 mb-1">
                X-Content-Type-Options
              </h5>
              <div className="bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                <pre className="text-xs">
                  # .htaccess (Apache):
                  <br />
                  Header set X-Content-Type-Options "nosniff"
                </pre>
              </div>
            </div>
          )}

          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-2 rounded">
            <p className="text-xs text-yellow-700">
              <strong>Note:</strong> Ces modifications nécessitent un accès au
              serveur web. Si vous utilisez un hébergement partagé, contactez
              votre hébergeur.
            </p>
          </div>
        </div>
      )}

      {/* Business Impact - More compact */}
      <div className="p-2 neo-card bg-gradient-to-r from-gray-50 to-gray-100">
        <h4 className="font-medium text-gray-800 mb-2 text-sm">
          Impact sur votre Activité
        </h4>
        <div className="space-y-2">
          <div className="flex items-start gap-2">
            <div className="text-blue-500 text-base">🔒</div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">
                Protection contre les Attaques
              </h5>
              <p className="text-xs text-gray-600">
                Les en-têtes de sécurité protègent contre les attaques XSS, le
                clickjacking et d'autres vulnérabilités.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2">
            <div className="text-blue-500 text-base">💼</div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">
                Conformité Réglementaire
              </h5>
              <p className="text-xs text-gray-600">
                Aide à respecter les réglementations comme le RGPD et à éviter
                les amendes potentielles.
              </p>
            </div>
          </div>

          <div className="flex items-start gap-2">
            <div className="text-blue-500 text-base">⭐</div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">
                Confiance des Utilisateurs
              </h5>
              <p className="text-xs text-gray-600">
                Un site sécurisé inspire confiance, augmentant conversions et
                fidélité.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Complete Headers Table (Collapsible) - More compact */}
      <details className="neo-card p-2">
        <summary className="font-medium text-gray-800 cursor-pointer text-sm">
          Voir tous les en-têtes HTTP détectés
        </summary>
        <div className="mt-2 overflow-x-auto">
          <table className="w-full text-xs">
            <thead className="text-left bg-gray-50">
              <tr>
                <th className="px-2 py-1 text-gray-600">En-tête</th>
                <th className="px-2 py-1 text-gray-600">Valeur</th>
                <th className="px-2 py-1 text-gray-600">Statut</th>
                <th className="px-2 py-1 text-gray-600">Importance</th>
              </tr>
            </thead>
            <tbody>
              {headers.map((header, index) => (
                <tr
                  key={index}
                  className="border-t border-gray-200 hover:bg-gray-100 transition"
                >
                  <td className="px-2 py-1 text-gray-800">
                    <InfoTooltip
                      term={header.name}
                      explanation={
                        header.description || getHeaderExplanation(header.name)
                      }
                    />
                  </td>
                  <td className="px-2 py-1 text-gray-700">
                    <div className="max-w-xs truncate">
                      {header.value || "Non défini"}
                    </div>
                  </td>
                  <td className="px-2 py-1">
                    <div className="flex items-center">
                      {getStatusIcon(header.status || "info")}
                    </div>
                  </td>
                  <td className="px-2 py-1">
                    <span
                      className={`px-1 py-0.5 rounded text-xs ${
                        header.importance === "critical" ||
                        header.importance === "high"
                          ? "bg-red-500/20 text-red-700"
                          : header.importance === "medium"
                          ? "bg-yellow-500/20 text-yellow-700"
                          : "bg-blue-500/20 text-blue-700"
                      }`}
                    >
                      {header.importance || "low"}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </details>
    </div>
  );
};

// Meta Tags Component
export const MetaTagsTable: React.FC<{ tags?: MetaTag[] }> = ({ tags }) => {
  if (!tags || tags.length === 0) {
    return (
      <p className="text-gray-600">Aucune donnée de balises meta disponible.</p>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <Check className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-blue-500" />;
    }
  };

  // Common meta tag explanations and impact
  const metaTagInfo: Record<
    string,
    { explanation: string; impact: string; example: string }
  > = {
    title: {
      explanation:
        "Le titre de la page qui apparaît dans les résultats de recherche et l'onglet du navigateur.",
      impact:
        "Un titre optimisé peut augmenter votre taux de clics de 20% dans les résultats de recherche.",
      example: "Titre Principal | Nom de l'Entreprise | Mot-clé Principal",
    },
    description: {
      explanation:
        "Résumé du contenu de la page qui apparaît dans les résultats de recherche.",
      impact:
        "Une description convaincante peut améliorer votre taux de clics de 15% dans les résultats de recherche.",
      example:
        "Description concise (150-160 caractères) incluant un appel à l'action et vos mots-clés principaux.",
    },
    keywords: {
      explanation:
        "Mots-clés pertinents pour le contenu de la page (moins important pour le référencement moderne).",
      impact:
        "Peu d'impact direct sur le classement, mais aide à la cohérence du contenu.",
      example: "mot-clé1, mot-clé2, mot-clé3 (5-10 mots-clés pertinents)",
    },
    viewport: {
      explanation: "Contrôle l'affichage de la page sur les appareils mobiles.",
      impact:
        "Essentiel pour le référencement mobile, qui représente plus de 60% des recherches.",
      example: "width=device-width, initial-scale=1",
    },
    robots: {
      explanation: "Indique aux moteurs de recherche comment indexer la page.",
      impact:
        "Contrôle quelles pages sont indexées et suivies par les moteurs de recherche.",
      example: "index, follow (pour les pages importantes)",
    },
    canonical: {
      explanation:
        "Indique l'URL préférée pour éviter les problèmes de contenu dupliqué.",
      impact:
        "Évite la dilution du référencement entre plusieurs URLs similaires.",
      example: "https://www.votresite.com/page-canonique/",
    },
    "og:title": {
      explanation: "Titre utilisé lors du partage sur les réseaux sociaux.",
      impact:
        "Augmente l'engagement de 40% lors des partages sur les réseaux sociaux.",
      example: "Titre accrocheur pour les réseaux sociaux (60-70 caractères)",
    },
    "og:description": {
      explanation:
        "Description utilisée lors du partage sur les réseaux sociaux.",
      impact:
        "Améliore le taux de clics de 30% sur les publications partagées.",
      example:
        "Description attrayante qui donne envie de cliquer (2-3 phrases)",
    },
    "og:image": {
      explanation: "Image affichée lors du partage sur les réseaux sociaux.",
      impact:
        "Les publications avec images génèrent 2,3 fois plus d'engagement.",
      example: "Image de 1200x630 pixels, claire et pertinente",
    },
    "twitter:card": {
      explanation: "Contrôle l'apparence des liens partagés sur Twitter.",
      impact:
        "Les cartes Twitter augmentent l'engagement de 43% par rapport aux liens simples.",
      example: "summary_large_image (pour une meilleure visibilité)",
    },
  };

  // Helper function to get explanation for a meta tag
  const getMetaTagExplanation = (name: string) => {
    return (
      metaTagInfo[name]?.explanation ||
      "Balise meta qui fournit des informations aux navigateurs ou moteurs de recherche."
    );
  };

  // Count missing and problematic tags
  const missingTags = tags.filter((tag) => tag.status === "error");
  const warningTags = tags.filter((tag) => tag.status === "warning");
  const goodTags = tags.filter((tag) => tag.status === "success");

  // Group tags by category
  const basicTags = tags.filter((tag) =>
    [
      "title",
      "description",
      "keywords",
      "viewport",
      "robots",
      "canonical",
    ].includes(tag.name)
  );
  const socialTags = tags.filter(
    (tag) => tag.name.startsWith("og:") || tag.name.startsWith("twitter:")
  );

  return (
    <div className="space-y-6">
      <SectionIntro>
        <div className="flex items-center gap-2 mb-2">
          <span className="text-purple-600 font-semibold flex items-center">
            <Database className="w-5 h-5 mr-1.5" />
            Carte d'Identité de Votre Page
          </span>
        </div>
        Les balises meta sont comme la carte d'identité de votre page web. Elles
        indiquent aux moteurs de recherche et aux réseaux sociaux qui vous êtes,
        ce que vous proposez, et comment vous présenter aux utilisateurs. Des
        balises meta bien optimisées sont essentielles pour améliorer votre
        visibilité en ligne.
      </SectionIntro>

      {/* Meta Tags Impact Dashboard - Ultra compact */}
      <div className="p-1.5 neo-card bg-gradient-to-r from-gray-50 to-purple-50">
        <h4 className="font-medium text-gray-800 mb-1 text-xs">
          État de vos Balises Meta
        </h4>
        <div className="grid grid-cols-3 gap-1">
          <div className="flex items-center p-1.5 bg-white rounded-lg shadow-sm">
            <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center text-green-500 mr-1.5 flex-shrink-0">
              <Check className="h-3 w-3" />
            </div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">Optimisées</h5>
              <p className="text-base font-semibold text-green-600">
                {goodTags.length}
              </p>
            </div>
          </div>

          <div className="flex items-center p-1.5 bg-white rounded-lg shadow-sm">
            <div className="w-6 h-6 rounded-full bg-yellow-100 flex items-center justify-center text-yellow-500 mr-1.5 flex-shrink-0">
              <AlertTriangle className="h-3 w-3" />
            </div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">À Améliorer</h5>
              <p className="text-base font-semibold text-yellow-600">
                {warningTags.length}
              </p>
            </div>
          </div>

          <div className="flex items-center p-1.5 bg-white rounded-lg shadow-sm">
            <div className="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center text-red-500 mr-1.5 flex-shrink-0">
              <X className="h-3 w-3" />
            </div>
            <div>
              <h5 className="text-xs font-medium text-gray-700">Manquantes</h5>
              <p className="text-base font-semibold text-red-600">
                {missingTags.length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Missing Tags = Lost Money */}
      {missingTags.length > 0 && (
        <div className="p-4 neo-card bg-red-50 border-l-4 border-red-500">
          <h4 className="font-medium text-red-800 mb-3 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-red-600" />
            Balises Manquantes = Opportunités Perdues
          </h4>
          <div className="space-y-3">
            {missingTags.map((tag, index) => (
              <div
                key={`missing-${index}`}
                className="flex items-start gap-3 p-3 bg-white rounded-lg shadow-sm"
              >
                <div className="p-1 rounded bg-red-100">
                  <X className="w-4 h-4 text-red-600" />
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700">
                    {tag.name} manquante
                  </h5>
                  <p className="text-xs text-red-600 mt-1">
                    <strong>Impact:</strong>{" "}
                    {metaTagInfo[tag.name]?.impact ||
                      "Réduit votre visibilité en ligne"}
                  </p>
                  {tag.recommendation && (
                    <p className="text-xs text-gray-600 mt-1">
                      <strong>Recommandation:</strong> {tag.recommendation}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Basic SEO Tags Section */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-800">
          Balises SEO Essentielles
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {basicTags.map((tag, index) => {
            const statusColor =
              tag.status === "success"
                ? "border-green-500 bg-green-50"
                : tag.status === "warning"
                ? "border-yellow-500 bg-yellow-50"
                : "border-red-500 bg-red-50";

            return (
              <div
                key={`basic-${index}`}
                className={`p-4 neo-card border-l-4 ${statusColor}`}
              >
                <div className="flex justify-between items-start">
                  <h5 className="font-medium text-gray-800">
                    <InfoTooltip
                      term={tag.name}
                      explanation={
                        tag.description || getMetaTagExplanation(tag.name)
                      }
                    />
                  </h5>
                  <div className="flex items-center">
                    {getStatusIcon(tag.status)}
                  </div>
                </div>

                <div className="mt-2 p-2 bg-white rounded border border-gray-200">
                  <p className="text-sm text-gray-700 break-words">
                    {tag.content || "Non défini"}
                  </p>
                </div>

                {tag.status !== "success" && tag.recommendation && (
                  <div className="mt-2">
                    <p className="text-xs text-gray-700">
                      <strong>Recommandation:</strong> {tag.recommendation}
                    </p>
                  </div>
                )}

                <div className="mt-2 flex justify-between items-center text-xs text-gray-500">
                  <span>
                    Impact:{" "}
                    {tag.importance === "high"
                      ? "Élevé"
                      : tag.importance === "medium"
                      ? "Moyen"
                      : "Faible"}
                  </span>
                  {metaTagInfo[tag.name]?.example && (
                    <span className="italic">
                      Ex: {metaTagInfo[tag.name].example}
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Social Media Tags Section */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-800">
          Balises pour Réseaux Sociaux
        </h4>
        <div className="p-4 neo-card bg-gradient-to-r from-gray-50 to-blue-50 mb-4">
          <div className="flex items-start gap-3">
            <div className="text-blue-500 text-xl">📱</div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                Importance des Balises Sociales
              </h5>
              <p className="text-xs text-gray-600 mt-1">
                Les balises Open Graph (og:) et Twitter Card permettent de
                contrôler l'apparence de votre contenu lorsqu'il est partagé sur
                les réseaux sociaux, augmentant significativement l'engagement
                et les clics.
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {socialTags.map((tag, index) => {
            const statusColor =
              tag.status === "success"
                ? "border-green-500 bg-green-50"
                : tag.status === "warning"
                ? "border-yellow-500 bg-yellow-50"
                : "border-red-500 bg-red-50";

            return (
              <div
                key={`social-${index}`}
                className={`p-4 neo-card border-l-4 ${statusColor}`}
              >
                <div className="flex justify-between items-start">
                  <h5 className="font-medium text-gray-800">
                    <InfoTooltip
                      term={tag.name}
                      explanation={
                        tag.description || getMetaTagExplanation(tag.name)
                      }
                    />
                  </h5>
                  <div className="flex items-center">
                    {getStatusIcon(tag.status)}
                  </div>
                </div>

                <div className="mt-2 p-2 bg-white rounded border border-gray-200">
                  <p className="text-sm text-gray-700 break-words">
                    {tag.content || "Non défini"}
                  </p>
                </div>

                {tag.status !== "success" && tag.recommendation && (
                  <div className="mt-2">
                    <p className="text-xs text-gray-700">
                      <strong>Recommandation:</strong> {tag.recommendation}
                    </p>
                  </div>
                )}

                <div className="mt-2 flex justify-between items-center text-xs text-gray-500">
                  <span>
                    Impact:{" "}
                    {tag.importance === "high"
                      ? "Élevé"
                      : tag.importance === "medium"
                      ? "Moyen"
                      : "Faible"}
                  </span>
                  {metaTagInfo[tag.name]?.example && (
                    <span className="italic">
                      Ex: {metaTagInfo[tag.name].example}
                    </span>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Social Media Preview */}
      {socialTags.length > 0 && (
        <div className="p-4 neo-card bg-white">
          <h4 className="font-medium text-gray-800 mb-3">
            Aperçu sur les Réseaux Sociaux
          </h4>
          <div className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-100 p-2 border-b border-gray-200">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs font-bold">
                  FB
                </div>
                <div className="ml-2">
                  <p className="text-sm font-medium">Facebook</p>
                  <p className="text-xs text-gray-500">il y a 2 heures</p>
                </div>
              </div>
            </div>

            <div className="p-3">
              <p className="text-sm mb-2">
                {socialTags.find((tag) => tag.name === "og:description")
                  ?.content ||
                  tags.find((tag) => tag.name === "description")?.content ||
                  "Description de votre page..."}
              </p>

              <div className="border border-gray-200 rounded overflow-hidden">
                <div className="h-40 bg-gray-200 flex items-center justify-center">
                  {socialTags.find((tag) => tag.name === "og:image")
                    ?.content ? (
                    <p className="text-sm text-gray-500">
                      Image:{" "}
                      {
                        socialTags.find((tag) => tag.name === "og:image")
                          ?.content
                      }
                    </p>
                  ) : (
                    <p className="text-sm text-gray-500">
                      Aucune image définie
                    </p>
                  )}
                </div>
                <div className="p-2 bg-gray-50">
                  <p className="text-sm font-medium">
                    {socialTags.find((tag) => tag.name === "og:title")
                      ?.content ||
                      tags.find((tag) => tag.name === "title")?.content ||
                      "Titre de votre page"}
                  </p>
                  <p className="text-xs text-gray-500">votresite.com</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* All Meta Tags Table (Collapsible) */}
      <details className="neo-card p-4">
        <summary className="font-medium text-gray-800 cursor-pointer">
          Voir toutes les balises meta ({tags.length})
        </summary>
        <div className="mt-4 overflow-x-auto">
          <table className="w-full">
            <thead className="text-left bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-gray-600">Nom</th>
                <th className="px-4 py-2 text-gray-600">Contenu</th>
                <th className="px-4 py-2 text-gray-600">Statut</th>
                <th className="px-4 py-2 text-gray-600">Importance</th>
              </tr>
            </thead>
            <tbody>
              {tags.map((tag, index) => (
                <tr
                  key={index}
                  className="border-t border-gray-200 hover:bg-gray-100 transition"
                >
                  <td className="px-4 py-3 text-gray-800">
                    <InfoTooltip
                      term={tag.name}
                      explanation={
                        tag.description || getMetaTagExplanation(tag.name)
                      }
                    />
                  </td>
                  <td className="px-4 py-3 text-gray-700">
                    <div className="max-w-xs truncate">
                      {tag.content || "Non défini"}
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      {getStatusIcon(tag.status)}
                      <span className="ml-2 text-xs text-gray-600">
                        {tag.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span
                      className={`px-2 py-1 rounded text-xs ${
                        tag.importance === "high"
                          ? "bg-red-500/20 text-red-700"
                          : tag.importance === "medium"
                          ? "bg-yellow-500/20 text-yellow-700"
                          : "bg-blue-500/20 text-blue-700"
                      }`}
                    >
                      {tag.importance || "low"}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </details>

      {/* Recommendations */}
      <div className="p-4 neo-card bg-gradient-to-r from-gray-50 to-purple-50">
        <h4 className="font-medium text-gray-800 mb-3">Actions Recommandées</h4>
        <ul className="space-y-3">
          {missingTags.length > 0 && (
            <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
              <div className="text-purple-500 text-xl">1️⃣</div>
              <div>
                <h5 className="text-sm font-medium text-gray-700">
                  Ajouter les balises manquantes
                </h5>
                <p className="text-xs text-gray-600 mt-1">
                  Priorité: Élevée - Ajoutez les {missingTags.length} balises
                  manquantes pour améliorer significativement votre visibilité.
                </p>
              </div>
            </li>
          )}

          {warningTags.length > 0 && (
            <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
              <div className="text-purple-500 text-xl">2️⃣</div>
              <div>
                <h5 className="text-sm font-medium text-gray-700">
                  Optimiser les balises existantes
                </h5>
                <p className="text-xs text-gray-600 mt-1">
                  Priorité: Moyenne - Améliorez les {warningTags.length} balises
                  qui nécessitent une optimisation.
                </p>
              </div>
            </li>
          )}

          <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
            <div className="text-purple-500 text-xl">3️⃣</div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                Vérifier régulièrement
              </h5>
              <p className="text-xs text-gray-600 mt-1">
                Priorité: Faible - Vérifiez vos balises meta tous les 3 mois
                pour vous assurer qu'elles restent optimisées.
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  );
};

// Mobile Optimization Details Component
export const MobileDetails: React.FC<{
  details?: {
    responsiveDesign: boolean;
    viewportConfiguration: boolean;
    touchTargets: boolean;
    fontSizes: boolean;
    mobileUsability: boolean;
  };
}> = ({ details }) => {
  if (!details) {
    return (
      <p className="text-gray-600">
        Aucune donnée d'optimisation mobile disponible.
      </p>
    );
  }

  // Calculate mobile score
  const totalChecks = 5;
  const passedChecks = [
    details.responsiveDesign,
    details.viewportConfiguration,
    details.touchTargets,
    details.fontSizes,
    details.mobileUsability,
  ].filter(Boolean).length;

  const mobileScore = Math.round((passedChecks / totalChecks) * 100);

  // Determine mobile experience rating
  const getMobileRating = () => {
    if (mobileScore >= 80)
      return { label: "Excellent", color: "text-green-600" };
    if (mobileScore >= 60) return { label: "Bon", color: "text-blue-600" };
    if (mobileScore >= 40) return { label: "Moyen", color: "text-yellow-600" };
    return { label: "Médiocre", color: "text-red-600" };
  };

  const mobileRating = getMobileRating();

  return (
    <div className="space-y-6">
      <SectionIntro>
        <div className="flex items-center gap-2 mb-2">
          <span className="text-blue-600 font-semibold flex items-center">
            <Smartphone className="w-4 h-4 mr-1.5" /> Expérience Mobile
          </span>
        </div>
        Plus de 60% des recherches web sont effectuées sur mobile. Google
        utilise l'indexation mobile-first, ce qui signifie que la version mobile
        de votre site détermine en grande partie votre classement dans les
        résultats de recherche.
      </SectionIntro>

      {/* Mobile Score Card */}
      <div className="p-4 neo-card bg-white">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-medium text-gray-800">
            Score d'Optimisation Mobile
          </h4>
          <div className="flex items-center">
            <div
              className={`w-16 h-16 rounded-full flex items-center justify-center text-white font-bold ${
                mobileScore >= 80
                  ? "bg-green-500"
                  : mobileScore >= 60
                  ? "bg-blue-500"
                  : mobileScore >= 40
                  ? "bg-yellow-500"
                  : "bg-red-500"
              }`}
            >
              {mobileScore}
            </div>
            <span className="ml-2 text-sm text-gray-600">/100</span>
          </div>
        </div>

        <div className="h-2 bg-gray-200 rounded-full overflow-hidden mb-4">
          <div
            className={`h-full ${
              mobileScore >= 80
                ? "bg-green-500"
                : mobileScore >= 60
                ? "bg-blue-500"
                : mobileScore >= 40
                ? "bg-yellow-500"
                : "bg-red-500"
            }`}
            style={{ width: `${mobileScore}%` }}
          ></div>
        </div>

        <p className="text-sm text-gray-600 mb-2">
          Expérience mobile:{" "}
          <span className={mobileRating.color}>{mobileRating.label}</span>
        </p>

        <p className="text-xs text-gray-500">
          {mobileScore < 80
            ? "Des améliorations sont nécessaires pour offrir une meilleure expérience aux utilisateurs mobiles."
            : "Votre site offre une bonne expérience aux utilisateurs mobiles."}
        </p>
      </div>

      {/* Mobile UX Evaluation - Compact */}
      <div className="p-3 neo-card bg-gradient-to-r from-gray-50 to-blue-50">
        <h4 className="font-medium text-gray-800 mb-2 text-sm">
          Expérience Utilisateur Mobile
        </h4>
        <div className="space-y-2">
          {!details.fontSizes && (
            <div className="flex items-start gap-2 p-2 bg-red-50 rounded-lg border-l-4 border-red-500">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </div>
              <div>
                <h5 className="text-xs font-medium text-red-800">
                  Texte trop petit
                </h5>
                <p className="text-xs text-gray-700">
                  Les utilisateurs doivent zoomer pour lire votre contenu,
                  augmentant le taux de rebond de 50%.
                </p>
              </div>
            </div>
          )}

          {!details.touchTargets && (
            <div className="flex items-start gap-2 p-2 bg-red-50 rounded-lg border-l-4 border-red-500">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </div>
              <div>
                <h5 className="text-xs font-medium text-red-800">
                  Boutons trop petits
                </h5>
                <p className="text-xs text-gray-700">
                  Les utilisateurs cliquent souvent sur le mauvais élément,
                  réduisant les conversions jusqu'à 40%.
                </p>
              </div>
            </div>
          )}

          {!details.responsiveDesign && (
            <div className="flex items-start gap-2 p-2 bg-red-50 rounded-lg border-l-4 border-red-500">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-4 w-4 text-red-500" />
              </div>
              <div>
                <h5 className="text-xs font-medium text-red-800">
                  Défilement horizontal
                </h5>
                <p className="text-xs text-gray-700">
                  Les utilisateurs doivent faire défiler horizontalement, créant
                  une expérience frustrante.
                </p>
              </div>
            </div>
          )}

          {details.responsiveDesign &&
            details.fontSizes &&
            details.touchTargets && (
              <div className="flex items-start gap-2 p-2 bg-green-50 rounded-lg border-l-4 border-green-500">
                <div className="flex-shrink-0">
                  <Check className="h-4 w-4 text-green-500" />
                </div>
                <div>
                  <h5 className="text-xs font-medium text-green-800">
                    Expérience utilisateur fluide
                  </h5>
                  <p className="text-xs text-gray-700">
                    Votre site offre une bonne expérience mobile avec texte
                    lisible et éléments interactifs accessibles.
                  </p>
                </div>
              </div>
            )}
        </div>
      </div>

      {/* Mobile Optimization Checklist */}
      <div className="p-4 neo-card bg-white">
        <h4 className="font-medium text-gray-800 mb-3">
          Checklist d'Optimisation Mobile
        </h4>
        <div className="space-y-3">
          <div
            className={`flex items-center p-3 rounded-lg ${
              details.responsiveDesign ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-3">
              {details.responsiveDesign ? (
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                  <Check className="h-5 w-5" />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                  <X className="h-5 w-5" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <h5 className="text-sm font-medium text-gray-800">
                  <InfoTooltip
                    term="Design Responsive"
                    explanation="Un design qui s'adapte automatiquement à différentes tailles d'écran, offrant une expérience optimale sur tous les appareils."
                  />
                </h5>
                <span
                  className={`text-xs ${
                    details.responsiveDesign ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {details.responsiveDesign ? "Validé" : "Non validé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                {details.responsiveDesign
                  ? "Votre site s'adapte correctement aux différentes tailles d'écran."
                  : "Votre site ne s'adapte pas correctement aux écrans mobiles, ce qui nuit à l'expérience utilisateur."}
              </p>
            </div>
          </div>

          <div
            className={`flex items-center p-3 rounded-lg ${
              details.viewportConfiguration ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-3">
              {details.viewportConfiguration ? (
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                  <Check className="h-5 w-5" />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                  <X className="h-5 w-5" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <h5 className="text-sm font-medium text-gray-800">
                  <InfoTooltip
                    term="Configuration Viewport"
                    explanation="La balise viewport contrôle comment votre page s'affiche sur les appareils mobiles, assurant un affichage correct et évitant le zoom horizontal."
                  />
                </h5>
                <span
                  className={`text-xs ${
                    details.viewportConfiguration
                      ? "text-green-600"
                      : "text-red-600"
                  }`}
                >
                  {details.viewportConfiguration ? "Validé" : "Non validé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                {details.viewportConfiguration
                  ? "La balise viewport est correctement configurée."
                  : "La balise viewport est manquante ou mal configurée, ce qui peut causer des problèmes d'affichage sur mobile."}
              </p>
              {!details.viewportConfiguration && (
                <div className="mt-2 bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                  <pre>
                    {
                      '<meta name="viewport" content="width=device-width, initial-scale=1">'
                    }
                  </pre>
                </div>
              )}
            </div>
          </div>

          <div
            className={`flex items-center p-3 rounded-lg ${
              details.touchTargets ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-3">
              {details.touchTargets ? (
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                  <Check className="h-5 w-5" />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                  <X className="h-5 w-5" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <h5 className="text-sm font-medium text-gray-800">
                  <InfoTooltip
                    term="Cibles Tactiles"
                    explanation="Les éléments cliquables (boutons, liens) doivent être suffisamment grands et espacés pour être facilement utilisables sur un écran tactile."
                  />
                </h5>
                <span
                  className={`text-xs ${
                    details.touchTargets ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {details.touchTargets ? "Validé" : "Non validé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                {details.touchTargets
                  ? "Les éléments interactifs sont suffisamment grands et espacés pour une utilisation tactile."
                  : "Certains éléments interactifs sont trop petits ou trop proches, rendant la navigation difficile sur mobile."}
              </p>
            </div>
          </div>

          <div
            className={`flex items-center p-3 rounded-lg ${
              details.fontSizes ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-3">
              {details.fontSizes ? (
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                  <Check className="h-5 w-5" />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                  <X className="h-5 w-5" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <h5 className="text-sm font-medium text-gray-800">
                  <InfoTooltip
                    term="Tailles de Police"
                    explanation="Les textes doivent être lisibles sans zoom sur mobile, généralement avec une taille de police d'au moins 16px pour le texte principal."
                  />
                </h5>
                <span
                  className={`text-xs ${
                    details.fontSizes ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {details.fontSizes ? "Validé" : "Non validé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                {details.fontSizes
                  ? "Les textes sont lisibles sans zoom sur mobile."
                  : "Certains textes sont trop petits, obligeant les utilisateurs à zoomer pour les lire."}
              </p>
            </div>
          </div>

          <div
            className={`flex items-center p-3 rounded-lg ${
              details.mobileUsability ? "bg-green-50" : "bg-red-50"
            }`}
          >
            <div className="mr-3">
              {details.mobileUsability ? (
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                  <Check className="h-5 w-5" />
                </div>
              ) : (
                <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center text-red-500">
                  <X className="h-5 w-5" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex justify-between">
                <h5 className="text-sm font-medium text-gray-800">
                  <InfoTooltip
                    term="Utilisabilité Mobile"
                    explanation="L'ensemble de l'expérience utilisateur sur mobile, incluant la navigation, les formulaires et le contenu, doit être facile à utiliser sur un petit écran."
                  />
                </h5>
                <span
                  className={`text-xs ${
                    details.mobileUsability ? "text-green-600" : "text-red-600"
                  }`}
                >
                  {details.mobileUsability ? "Validé" : "Non validé"}
                </span>
              </div>
              <p className="text-xs text-gray-600 mt-1">
                {details.mobileUsability
                  ? "L'expérience utilisateur globale est bien adaptée aux appareils mobiles."
                  : "L'expérience utilisateur globale présente des problèmes sur mobile, ce qui peut frustrer les visiteurs."}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Conversion Impact - Compact */}
      <div className="p-2 neo-card bg-gradient-to-r from-gray-50 to-blue-50">
        <h4 className="font-medium text-gray-800 mb-2 text-sm flex items-center">
          <ArrowRight className="w-4 h-4 mr-1.5 text-blue-600" /> Impact sur les
          Conversions
        </h4>
        <div className="grid grid-cols-2 gap-2">
          <div className="p-2 bg-white rounded-lg shadow-sm">
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0">
                <Info className="h-4 w-4 text-blue-500" />
              </div>
              <div>
                <h5 className="text-xs font-medium text-gray-700">
                  Statistiques
                </h5>
                <p className="text-xs text-gray-600">
                  <strong>53%</strong> quittent si chargement &gt;3s
                </p>
                <p className="text-xs text-gray-600">
                  <strong>70%</strong> influencés par la vitesse
                </p>
              </div>
            </div>
          </div>

          <div className="p-2 bg-white rounded-lg shadow-sm">
            <div className="flex items-start gap-2">
              <div className="flex-shrink-0">
                <Info className="h-4 w-4 text-blue-500" />
              </div>
              <div>
                <h5 className="text-xs font-medium text-gray-700">Impact</h5>
                <p className="text-xs text-gray-600">
                  Conversions: <strong>+15-35%</strong>
                </p>
                <p className="text-xs text-gray-600">
                  Coût acquisition: <strong>-33%</strong>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recommendations */}
      {(!details.responsiveDesign ||
        !details.viewportConfiguration ||
        !details.touchTargets ||
        !details.fontSizes ||
        !details.mobileUsability) && (
        <div className="p-4 neo-card bg-white">
          <h4 className="font-medium text-gray-800 mb-3">
            Actions Recommandées
          </h4>
          <ul className="space-y-3">
            {!details.viewportConfiguration && (
              <li className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                <div className="text-blue-500 text-xl">1️⃣</div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700">
                    Ajouter la balise viewport
                  </h5>
                  <p className="text-xs text-gray-600 mt-1">
                    Priorité: Critique - Ajoutez la balise viewport dans la
                    section head de votre HTML.
                  </p>
                  <div className="mt-2 bg-gray-800 text-green-400 p-2 rounded text-xs font-mono overflow-x-auto">
                    <pre>
                      {
                        '<meta name="viewport" content="width=device-width, initial-scale=1">'
                      }
                    </pre>
                  </div>
                </div>
              </li>
            )}

            {!details.responsiveDesign && (
              <li className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                <div className="text-blue-500 text-xl">
                  {!details.viewportConfiguration ? "2️⃣" : "1️⃣"}
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700">
                    Implémenter un design responsive
                  </h5>
                  <p className="text-xs text-gray-600 mt-1">
                    Priorité: Élevée - Utilisez des frameworks CSS comme
                    Bootstrap ou Tailwind pour créer un design qui s'adapte à
                    toutes les tailles d'écran.
                  </p>
                </div>
              </li>
            )}

            {!details.fontSizes && (
              <li className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                <div className="text-blue-500 text-xl">
                  {!details.viewportConfiguration && !details.responsiveDesign
                    ? "3️⃣"
                    : !details.viewportConfiguration ||
                      !details.responsiveDesign
                    ? "2️⃣"
                    : "1️⃣"}
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700">
                    Augmenter la taille des textes
                  </h5>
                  <p className="text-xs text-gray-600 mt-1">
                    Priorité: Moyenne - Utilisez une taille de police d'au moins
                    16px pour le texte principal et 14px pour le texte
                    secondaire.
                  </p>
                </div>
              </li>
            )}

            {!details.touchTargets && (
              <li className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
                <div className="text-blue-500 text-xl">
                  {!details.viewportConfiguration &&
                  !details.responsiveDesign &&
                  !details.fontSizes
                    ? "4️⃣"
                    : (!details.viewportConfiguration &&
                        !details.responsiveDesign) ||
                      (!details.viewportConfiguration && !details.fontSizes) ||
                      (!details.responsiveDesign && !details.fontSizes)
                    ? "3️⃣"
                    : !details.viewportConfiguration ||
                      !details.responsiveDesign ||
                      !details.fontSizes
                    ? "2️⃣"
                    : "1️⃣"}
                </div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700">
                    Agrandir les éléments interactifs
                  </h5>
                  <p className="text-xs text-gray-600 mt-1">
                    Priorité: Moyenne - Assurez-vous que tous les boutons et
                    liens ont une taille d'au moins 44x44 pixels et sont
                    suffisamment espacés.
                  </p>
                </div>
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};

// Import and re-export ImageDetails component
import ImageDetails from "./ImageDetails";
export { ImageDetails };
