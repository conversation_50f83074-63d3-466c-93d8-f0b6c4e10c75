import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "../lib/utils"; // Corrected import path
import Logo from "../components/ui-custom/Logo";

export default function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const isActive = (path: string) => location.pathname === path;

  return (
    <header
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-300 navbar-blur",
        isScrolled
          ? "py-3 bg-gradient-to-r from-blue-600/20 to-purple-600/20 shadow-lg border-b border-white/10"
          : "py-5 bg-gradient-to-r from-blue-500/15 to-purple-500/15 shadow-sm"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="container mx-auto px-4 md:px-6 flex items-center justify-between">
        <Logo to="/" />

        <nav className="hidden md:flex items-center space-x-8 h-[60px]">
          {/* Fixed height prevents layout shift */}
          <NavLink to="/services" active={isActive("/services")}>
            Services
          </NavLink>
          <NavLink to="/about" active={isActive("/about")}>
            À propos
          </NavLink>
          <NavLink to="/testimonials" active={isActive("/testimonials")}>
            Avis
          </NavLink>

          <div className="ml-4 flex items-center space-x-4">
            <Link
              to="/login"
              className="py-2 px-6 rounded-full bg-white/10 backdrop-blur-sm text-white border border-white/20 shadow-sm hover:shadow-md hover:bg-white/20 transition-all font-medium"
              style={{ minWidth: "110px", textAlign: "center" }}
            >
              Connexion
            </Link>
            <Link
              to="/register"
              className="py-2 px-6 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-sm hover:shadow-md hover:from-blue-700 hover:to-purple-700 transition-all font-medium transform hover:scale-105"
              style={{ minWidth: "110px", textAlign: "center" }}
            >
              S'inscrire
            </Link>
          </div>
        </nav>

        {/* Mobile menu button - you can expand this into a full mobile menu if needed */}
        <button className="md:hidden text-white hover:text-white/80 transition-all">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 6h16M4 12h16M4 18h16"
            />
          </svg>
        </button>
      </div>
    </header>
  );
}

interface NavLinkProps {
  to: string;
  active: boolean;
  children: React.ReactNode;
}

function NavLink({ to, active, children }: NavLinkProps) {
  return (
    <Link
      to={to}
      className={`text-sm font-medium tracking-wide transition-all relative ${
        active
          ? "text-white after:absolute after:bottom-[-4px] after:left-0 after:w-full after:h-[2px] after:bg-gradient-to-r after:from-blue-600 after:to-purple-600 after:rounded-full"
          : "text-white/80 hover:text-white"
      }`}
    >
      {children}
    </Link>
  );
}
