import { useState, useEffect } from "react";
import { <PERSON>, useNavigate, useLocation } from "react-router-dom";
import { Toaster } from "sonner";
import axios from "axios";
import {
  ArrowRight,
  Search,
  Shield,
  ActivitySquare,
  Loader2,
  User,
  KeyRound,
  LogOut,
  Settings,
  UserCog,
  BarChart3,
  X,
} from "lucide-react";
import Card from "../components/ui-custom/Card";
import Button from "../components/ui-custom/Button";
import Logo from "../components/ui-custom/Logo";
import FooterLink from "../components/FooterLink";
import Layout from "../components/Layout";
import ServerErrorMessage from "../components/ServerErrorMessage";
import { useToast } from "@/hooks/use-toast";
import { apiService } from "@/services/apiService";
import { safeStorage } from "@/utils/storage";
import { ScanDepth } from "@/types/report";

interface RecentReport {
  url: string;
  timestamp: string;
  scanDepth?: string | ScanDepth;
}

interface UserInfo {
  id: string;
  email: string;
  role: string | number;
}

// Helper function to determine scan depth based on user role
const getScanDepthFromRole = (
  role: string | number | null | undefined
): ScanDepth => {
  // Handle null, undefined, or non-string/non-number values
  if (role === null || role === undefined) {
    return ScanDepth.BASIC;
  }

  // Convert role to string if it's not already
  const roleStr = String(role);
  const roleToLower = roleStr.toLowerCase();

  // Premium users (role 2) and Admin users (role 3) get COMPREHENSIVE scans
  if (
    roleToLower === "admin" ||
    roleToLower === "premium" ||
    roleToLower === "3" ||
    roleToLower === "2" ||
    roleToLower === "2.0" ||
    roleToLower === "3.0"
  ) {
    return ScanDepth.COMPREHENSIVE;
  }
  // All other users (free users, role 1) get BASIC scans
  return ScanDepth.BASIC;
};

export default function Dashboard() {
  const [url, setUrl] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState("");
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [recentReports, setRecentReports] = useState<RecentReport[]>([]);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // State to track if user is authenticated
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    const loadInitialData = async () => {
      try {
        // Check if user is coming from admin verification flow
        const isAdminVerified = await safeStorage.getItem("isAdminVerified");

        // Fetch current user info - no need for retry logic since apiService.getCurrentUser
        // now has built-in caching and retry mechanisms
        const user = await apiService.getCurrentUser();

        // Update user info and authentication state
        setUserInfo(user);
        setIsAuthenticated(!!user);

        // Check if user is admin and redirect to admin dashboard
        if (user && String(user.role) === "3" && isAdminVerified === "true") {
          navigate("/admin");
          return;
        }

        // We no longer automatically redirect premium users to premium dashboard
        // They will access it through the dashboard button

        // If user is authenticated and not a free user (role 1), load recent scans from database
        if (user && String(user.role) !== "1") {
          try {
            const recentScans = await apiService.getRecentScans();
            // If we have scan data, format it
            if (recentScans && recentScans.length > 0) {
              // Convert the scan depth string to ScanDepth enum if needed
              // Filter out any scans that have been marked as deleted in localStorage
              const formattedScans = recentScans
                .filter((scan) => !isScanDeleted(scan.url))
                .map((scan) => ({
                  url: scan.url,
                  timestamp: scan.timestamp,
                  scanDepth: scan.scanDepth as string | ScanDepth,
                }));
              setRecentReports(formattedScans);
            } else {
              // No scans found or API returned empty array
              setRecentReports([]);
            }
          } catch (scanError) {
            // This shouldn't happen now since apiService.getRecentScans handles errors
            console.error("Error in scan history processing:", scanError);
            setRecentReports([]);
          }
        } else {
          // For non-authenticated users or free users, we'll show an empty list
          setRecentReports([]);
        }

        // If admin verified but no user, try to refresh the page once
        if (
          isAdminVerified === "true" &&
          !user &&
          !window.sessionStorage.getItem("dashboardRefreshed")
        ) {
          window.sessionStorage.setItem("dashboardRefreshed", "true");
          window.location.reload();
          return;
        }

        // If not authenticated, we'll show a login button instead of redirecting
      } catch (error) {
        console.error("Error loading initial data:", error);
        setRecentReports([]);
        setIsAuthenticated(false);
      }
    };

    loadInitialData();

    // Check if we need to refresh scan history after returning from report page
    const refreshScanHistory = sessionStorage.getItem("refreshScanHistory");
    if (refreshScanHistory === "true" && isAuthenticated === true) {
      // Clear the flag
      sessionStorage.removeItem("refreshScanHistory");

      // Refresh scan history from database (only for non-free users)
      const refreshHistory = async () => {
        // Only refresh history for premium/admin users (not free users with role 1)
        if (!userInfo || String(userInfo.role) === "1") {
          setRecentReports([]);
          return;
        }

        try {
          const recentScans = await apiService.getRecentScans();
          // If we have scan data, format it
          if (recentScans && recentScans.length > 0) {
            // Convert the scan depth string to ScanDepth enum if needed
            // Filter out any scans that have been marked as deleted in localStorage
            const formattedScans = recentScans
              .filter((scan) => !isScanDeleted(scan.url))
              .map((scan) => ({
                url: scan.url,
                timestamp: scan.timestamp,
                scanDepth: scan.scanDepth as string | ScanDepth,
              }));
            setRecentReports(formattedScans);
          } else {
            // No scans found or API returned empty array
            setRecentReports([]);
          }
        } catch (error) {
          console.error("Error refreshing scan history:", error);
        }
      };

      refreshHistory();
    }

    // Check if we were redirected from the Report page with an error
    const params = new URLSearchParams(location.search);
    const errorParam = params.get("error");
    const reasonParam = params.get("reason");

    if (errorParam === "backend_unavailable") {
      setResult("Service indisponible");

      // Show toast notification
      toast({
        title: "Service indisponible",
        variant: "destructive",
      });

      // Clear the error parameter from the URL to prevent showing the error again on refresh
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    } else if (errorParam === "domain_not_found") {
      setResult("Domaine introuvable");

      // Show toast notification
      toast({
        title: "Domaine introuvable",
        description:
          "Ce domaine n'existe pas ou n'est pas accessible. Veuillez vérifier l'URL et réessayer.",
        variant: "destructive",
      });

      // Clear the error parameter from the URL to prevent showing the error again on refresh
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    } else if (errorParam === "site_inaccessible") {
      const reason = reasonParam || "";
      setResult(`Site inaccessible: ${reason}`);

      // Show toast notification
      toast({
        title: "Site inaccessible",
        description: `Ce site est probablement inaccessible${
          reason ? ` (${reason})` : ""
        }. Veuillez vérifier l'URL et réessayer.`,
        variant: "destructive",
      });

      // Clear the error parameter from the URL to prevent showing the error again on refresh
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [isAuthenticated, location]);

  const saveToRecentReports = async (url: string) => {
    try {
      // Only proceed if user is authenticated
      if (!isAuthenticated) {
        return;
      }

      // Check if this URL has been deleted by the user
      if (isScanDeleted(url)) {
        // If the user is scanning a previously deleted URL, remove it from the deleted list
        // This allows the URL to reappear in the history
        try {
          const deletedScansJson = localStorage.getItem("deletedScans") || "[]";
          const deletedScans = JSON.parse(deletedScansJson) as string[];
          const updatedDeletedScans = deletedScans.filter(
            (deletedUrl) => deletedUrl !== url
          );
          localStorage.setItem(
            "deletedScans",
            JSON.stringify(updatedDeletedScans)
          );
        } catch (error) {
          console.error("Error updating deleted scans:", error);
        }
      }

      // Determine scan depth based on user role
      const userScanDepth = userInfo
        ? getScanDepthFromRole(userInfo.role)
        : ScanDepth.BASIC;

      // Only save recent reports for premium/admin users (not free users with role 1)
      if (userInfo && String(userInfo.role) !== "1") {
        // Create a new report object
        const newReport = {
          url,
          timestamp: new Date().toISOString(),
          scanDepth: userScanDepth, // Save the scan depth with the report
        };

        // Update the UI immediately for better user experience
        // The actual data will be recorded in the database by the Report page
        // We'll refresh the list from the database after the scan is complete
        const updatedReports = [newReport, ...recentReports.slice(0, 4)];
        setRecentReports(updatedReports);
      }
    } catch (error) {
      console.error("Error saving recent reports:", error);
    }
  };

  // Function to store deleted scan URLs in localStorage
  const storeDeletedScan = (url: string) => {
    try {
      // Get existing deleted scans from localStorage
      const deletedScansJson = localStorage.getItem("deletedScans") || "[]";
      const deletedScans = JSON.parse(deletedScansJson) as string[];

      // Add the new URL if it's not already in the list
      if (!deletedScans.includes(url)) {
        deletedScans.push(url);
        localStorage.setItem("deletedScans", JSON.stringify(deletedScans));
      }
    } catch (error) {
      console.error("Error storing deleted scan:", error);
    }
  };

  // Function to check if a scan URL has been deleted
  const isScanDeleted = (url: string): boolean => {
    try {
      const deletedScansJson = localStorage.getItem("deletedScans") || "[]";
      const deletedScans = JSON.parse(deletedScansJson) as string[];
      return deletedScans.includes(url);
    } catch (error) {
      console.error("Error checking deleted scan:", error);
      return false;
    }
  };

  // Function to remove a report from the recent reports list
  const removeRecentReport = (index: number) => {
    // Ask for confirmation before removing
    if (window.confirm("Voulez-vous supprimer cet élément de l'historique ?")) {
      // Get the URL of the report to be deleted
      const reportToDelete = recentReports[index];

      // Store the URL in localStorage to persist the deletion
      storeDeletedScan(reportToDelete.url);

      // Create a new array without the item at the specified index
      const updatedReports = [...recentReports];
      updatedReports.splice(index, 1);
      setRecentReports(updatedReports);

      // Show success toast
      toast({
        title: "Élément supprimé",
        description: "L'élément a été supprimé de l'historique",
      });
    }
  };

  const isValidUrl = (value: string) => {
    // Trim whitespace from the input value
    const trimmedValue = value.trim();

    // If URL doesn't have a protocol, we'll add https:// later
    // So we need to check if it would be valid with a protocol
    const valueToCheck =
      trimmedValue.startsWith("http://") || trimmedValue.startsWith("https://")
        ? trimmedValue
        : "https://" + trimmedValue;

    const urlPattern = new RegExp(
      "^(https?:\\/\\/)?" + // protocole
        "((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|" + // nom de domaine
        "((\\d{1,3}\\.){3}\\d{1,3}))" + // ou adresse IP (v4)
        "(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*" + // port et chemin
        "(\\?[;&a-z\\d%_.~+=-]*)?" + // paramètres de requête
        "(\\#[-a-z\\d_]*)?$", // fragment
      "i"
    );
    return !!valueToCheck.match(urlPattern);
  };

  // Function to check if a URL is likely to be inaccessible
  const isLikelyInaccessible = (
    url: string
  ): { inaccessible: boolean; reason: string } => {
    // Normalize the URL by adding https:// if needed
    const normalizedUrl = url.trim().startsWith("http")
      ? url.trim()
      : `https://${url.trim()}`;

    try {
      const urlObj = new URL(normalizedUrl);
      const hostname = urlObj.hostname;
      const port = urlObj.port;

      // Check for localhost
      if (hostname === "localhost" || hostname === "127.0.0.1") {
        return {
          inaccessible: true,
          reason: "URL locale",
        };
      }

      // Check for private IP ranges
      if (/^(10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\.)/.test(hostname)) {
        return {
          inaccessible: true,
          reason: "IP privée",
        };
      }

      // Check for reserved IP addresses
      if (
        /^(0\.|169\.254\.|192\.0\.2\.|198\.51\.100\.|203\.0\.113\.|224\.|240\.|255\.255\.255\.255)/.test(
          hostname
        )
      ) {
        return {
          inaccessible: true,
          reason: "IP réservée",
        };
      }

      // Check for unusual ports that are likely not running web services
      if (port && (parseInt(port) < 80 || parseInt(port) > 10000)) {
        return {
          inaccessible: true,
          reason: "Port inhabituel",
        };
      }

      // Check for domain without TLD
      if (!hostname.includes(".") && !hostname.match(/^\d+\.\d+\.\d+\.\d+$/)) {
        return {
          inaccessible: true,
          reason: "Domaine malformé",
        };
      }

      // Check for domain with invalid TLD (less than 2 characters)
      const parts = hostname.split(".");
      if (
        parts.length > 1 &&
        parts[parts.length - 1].length < 2 &&
        !hostname.match(/^\d+\.\d+\.\d+\.\d+$/)
      ) {
        return {
          inaccessible: true,
          reason: "TLD invalide",
        };
      }

      return { inaccessible: false, reason: "" };
    } catch (error) {
      // If URL parsing fails, it's definitely invalid
      return {
        inaccessible: true,
        reason: "URL invalide",
      };
    }
  };

  const handleAnalysis = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!isValidUrl(url)) {
      setResult("Veuillez entrer une URL valide (ex: https://exemple.com)");
      toast({
        title: "URL Invalide",
        description: "Veuillez entrer une URL de site Web valide",
        variant: "destructive",
      });
      return;
    }

    // Authentication is required for all scans
    if (!isAuthenticated || !userInfo) {
      toast({
        title: "Authentification requise",
        description: "Veuillez vous connecter pour effectuer une analyse",
        variant: "destructive",
      });
      navigate("/login", { state: { returnUrl: "/dashboard", scanUrl: url } });
      return;
    }

    // We'll catch backend errors in the try/catch block below

    // Ensure URL has a protocol and is properly formatted
    let processedUrl = url.trim();

    // Remove any leading/trailing whitespace and ensure protocol
    if (
      !processedUrl.startsWith("http://") &&
      !processedUrl.startsWith("https://")
    ) {
      processedUrl = "https://" + processedUrl;
    }

    // Check if the URL is likely to be inaccessible
    const accessibilityCheck = isLikelyInaccessible(processedUrl);
    if (accessibilityCheck.inaccessible) {
      setResult(`Site inaccessible: ${accessibilityCheck.reason}`);

      toast({
        title: "Site inaccessible",
        description: `Ce site est probablement inaccessible (${accessibilityCheck.reason}). Veuillez vérifier l'URL et réessayer.`,
        variant: "destructive",
      });

      return;
    }

    setLoading(true);
    setResult("");

    try {
      // Quick check if the backend is running
      try {
        await axios
          .get(`${import.meta.env.VITE_SCAN_API_URL}/api/health`, {
            timeout: 1000, // Reduced timeout for faster response
          })
          .catch(() => {
            throw new Error("BACKEND_NOT_RUNNING");
          });
      } catch (error) {
        // Backend is not running, show error message
        setResult("Service indisponible");

        toast({
          title: "Service indisponible",
          variant: "destructive",
        });
        return; // Don't proceed with the scan
      }

      // Determine scan depth based on user role
      const userScanDepth = userInfo
        ? getScanDepthFromRole(userInfo.role)
        : ScanDepth.BASIC;

      // Save to recent reports before navigating
      await saveToRecentReports(processedUrl);

      // Set a flag to refresh scan history when returning to dashboard
      sessionStorage.setItem("refreshScanHistory", "true");

      // Navigate to report page immediately to show loading screen
      // This improves perceived performance by showing the loading UI faster
      navigate(
        `/report?url=${encodeURIComponent(processedUrl)}&depth=${userScanDepth}`
      );

      // Return early - the Report page will handle the actual scanning
      return;
    } catch (error: any) {
      // Analysis error occurred

      // Check if the error is due to authentication
      if (error.response?.status === 401 || error.response?.status === 403) {
        toast({
          title: "Session expirée",
          description: "Votre session a expiré. Veuillez vous reconnecter.",
          variant: "destructive",
        });
        navigate("/login", {
          state: { returnUrl: "/dashboard", scanUrl: processedUrl },
        });
      }
      // Check if the error is due to backend not running
      else if (
        error.message === "BACKEND_NOT_RUNNING" ||
        (error.response?.data?.error &&
          error.response.data.error.includes(
            "Le service d'analyse n'est pas disponible"
          ))
      ) {
        setResult("Service indisponible");

        toast({
          title: "Service indisponible",
          variant: "destructive",
        });
        // Don't navigate to report page - stay on dashboard
        return;
      } else {
        setResult("Une erreur s'est produite");
        toast({
          title: "Erreur",
          description: error.message || "Impossible de démarrer l'analyse",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (isAuthenticated === null) {
    return (
      <Layout showNav={false} showFooter={false}>
        <div className="container mx-auto px-4 py-12">
          <div className="flex flex-col justify-center items-center min-h-[50vh] space-y-4">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
            <p className="text-gray-600">Chargement du tableau de bord...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-400 to-purple-500 flex flex-col">
      <header className="py-4 px-6 flex items-center justify-between bg-white/10 backdrop-blur-sm">
        <Logo />
        <nav className="flex items-center space-x-6">
          {userInfo &&
          (String(userInfo.role) === "2" || String(userInfo.role) === "2.0") ? (
            <Link to="/premium-dashboard" className="text-white font-medium">
              Dashboard
            </Link>
          ) : (
            <Link to="/dashboard" className="text-white font-medium">
              Dashboard
            </Link>
          )}

          {isAuthenticated ? (
            <>
              {userInfo && String(userInfo.role) === "3" && (
                <Link
                  to="/admin"
                  className="text-white/80 hover:text-white transition-colors"
                >
                  Admin
                </Link>
              )}
              <Link
                to="/reports"
                className="text-white/80 hover:text-white transition-colors"
              >
                Rapports
              </Link>
              <div className="relative group">
                <button
                  className="flex items-center space-x-2 focus:outline-none"
                  aria-label="User menu"
                >
                  <div
                    className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center text-white"
                    aria-label="User initials"
                  >
                    {userInfo?.email
                      ? userInfo.email.substring(0, 2).toUpperCase()
                      : "MC"}
                  </div>
                  <span className="text-white">
                    {userInfo?.email
                      ? userInfo.email.split("@")[0]
                      : "Mon Compte"}
                  </span>
                </button>

                {/* Menu déroulant */}
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                  {userInfo && String(userInfo.role) === "3" ? (
                    <button
                      onClick={() => navigate("/admin")}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                    >
                      <UserCog className="w-4 h-4 mr-2 text-gray-500" />
                      Tableau de bord admin
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={() => navigate("/profile")}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <User className="w-4 h-4 mr-2 text-gray-500" />
                        Mon profil
                      </button>
                      <button
                        onClick={() => navigate("/profile?tab=password")}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <KeyRound className="w-4 h-4 mr-2 text-gray-500" />
                        Changer mot de passe
                      </button>
                      <button
                        onClick={() => navigate("/profile?tab=settings")}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <Settings className="w-4 h-4 mr-2 text-gray-500" />
                        Paramètres
                      </button>
                    </>
                  )}
                  <hr className="my-1" />
                  <button
                    onClick={async () => {
                      try {
                        await apiService.logout();
                        setIsAuthenticated(false);
                        setUserInfo(null);
                        navigate("/login");
                      } catch (error: any) {
                        toast({
                          title: "Erreur de déconnexion",
                          description:
                            error.message || "La déconnexion a échoué",
                          variant: "destructive",
                        });
                      }
                    }}
                    className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center"
                    aria-label="Logout button"
                  >
                    <LogOut className="w-4 h-4 mr-2 text-red-500" />
                    Déconnexion
                  </button>
                </div>
              </div>
            </>
          ) : (
            <button
              onClick={() => navigate("/login")}
              className="py-2 px-4 bg-gradient-blue-purple hover:opacity-80 rounded-md text-white transition-colors"
              aria-label="Login button"
            >
              Connexion
            </button>
          )}
        </nav>
      </header>

      <main className="container mx-auto px-4 py-12 flex-grow">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            Analyse de sécurité et SEO
          </h1>
          <p className="text-white/80 max-w-2xl mx-auto">
            Vérifiez les vulnérabilités et optimisez le référencement de votre
            site web
          </p>
        </div>

        <Card className="mx-auto max-w-3xl p-8 bg-white/95 rounded-xl shadow-lg">
          <form onSubmit={handleAnalysis} className="space-y-6">
            <div className="space-y-1">
              <label className="text-gray-700 font-medium">
                URL du site à analyser
              </label>
              <p className="text-sm text-gray-500">
                (ex: votresite.com ou example.com )
              </p>
            </div>

            <div className="flex flex-col gap-3">
              <div className="flex gap-3 sm:flex-row">
                <div className="flex items-center flex-1 rounded-lg border border-gray-300 bg-white px-3">
                  <Search className="text-gray-400 w-5 h-5" />
                  <input
                    type="url"
                    placeholder="example.com"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                    onPaste={(e) => {
                      // Get pasted content
                      const pastedText = e.clipboardData.getData("text");

                      // Process the pasted URL
                      let processedUrl = pastedText.trim();

                      // If it's a valid URL without protocol, add https://
                      if (
                        processedUrl &&
                        !processedUrl.startsWith("http://") &&
                        !processedUrl.startsWith("https://") &&
                        isValidUrl(processedUrl)
                      ) {
                        e.preventDefault(); // Prevent default paste
                        setUrl("https://" + processedUrl); // Set with https:// added
                      }
                      // Otherwise let the default paste behavior happen
                    }}
                    className="flex-1 py-3 px-2 bg-transparent outline-none text-gray-700"
                    required
                  />
                </div>
                <Button
                  type="submit"
                  className="rounded-lg px-6 py-3 text-white"
                  loading={loading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Analyse en cours...
                    </>
                  ) : (
                    <>
                      Analyser
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </>
                  )}
                </Button>
              </div>

              {userInfo && (
                <div className="text-sm text-gray-600 flex items-center">
                  <span className="mr-2">Niveau d'analyse:</span>
                  <span className="font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                    {getScanDepthFromRole(userInfo.role) ===
                    ScanDepth.COMPREHENSIVE
                      ? "Complète"
                      : "Basique"}
                  </span>
                  <span className="ml-2 text-xs text-gray-500">
                    (basé sur votre abonnement)
                  </span>
                </div>
              )}
            </div>
          </form>

          {result &&
            (result === "Service indisponible" ? (
              <ServerErrorMessage />
            ) : result === "Domaine introuvable" ? (
              <ServerErrorMessage message="Domaine introuvable" />
            ) : result.startsWith("Site inaccessible") ? (
              <ServerErrorMessage message={result} />
            ) : (
              <div
                className={`mt-6 p-4 rounded-lg ${
                  result.includes("succès")
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {result}
              </div>
            ))}

          {/* Only show recent reports section for premium/admin users (not free users with role 1) */}
          {isAuthenticated && userInfo && String(userInfo.role) !== "1" && (
            <div className="mt-8 border-t pt-6">
              <h3 className="text-lg font-medium text-gray-800 mb-3">
                Rapports récents
              </h3>

              {recentReports.length > 0 ? (
                <ul className="space-y-2">
                  {recentReports.map((report, index) => (
                    <li
                      key={index}
                      className="flex items-center justify-between bg-gray-50 p-3 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <span className="truncate max-w-[70%]">{report.url}</span>
                      <div className="flex items-center">
                        <span className="text-xs text-gray-500 mr-3">
                          {new Date(report.timestamp).toLocaleDateString(
                            "fr-FR"
                          )}
                        </span>
                        <button
                          onClick={async () => {
                            // Disable button during check to prevent multiple clicks
                            const button =
                              document.activeElement as HTMLButtonElement;
                            if (button) button.disabled = true;

                            try {
                              // Make a simple request to check if the backend is running
                              await axios
                                .get(
                                  `${
                                    import.meta.env.VITE_SCAN_API_URL
                                  }/api/health`,
                                  {
                                    timeout: 2000,
                                  }
                                )
                                .catch(() => {
                                  // If we get any error, assume the backend is not running
                                  throw new Error("BACKEND_NOT_RUNNING");
                                });

                              // If we get here, the backend is running, so navigate to the report
                              // Set a flag to refresh scan history when returning to dashboard
                              sessionStorage.setItem(
                                "refreshScanHistory",
                                "true"
                              );
                              navigate(
                                `/report?url=${encodeURIComponent(
                                  report.url
                                )}&depth=${report.scanDepth || ScanDepth.BASIC}`
                              );
                            } catch (error: any) {
                              // Backend is not running, show error message
                              // Backend connectivity error
                              setResult("Service indisponible");

                              toast({
                                title: "Service indisponible",
                                variant: "destructive",
                              });
                            } finally {
                              // Re-enable button
                              if (button) button.disabled = false;
                            }
                          }}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium mr-3"
                        >
                          Voir
                        </button>
                        <button
                          onClick={() => removeRecentReport(index)}
                          className="text-gray-400 hover:text-red-500 transition-colors"
                          title="Supprimer de l'historique"
                          aria-label="Supprimer de l'historique"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-500 text-sm">
                  Aucune analyse récente. Lancez une analyse pour commencer.
                </p>
              )}
            </div>
          )}

          <div className="mt-8">
            <h2 className="text-lg font-medium text-gray-800 mb-4">
              L'analyse inclut :
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[
                {
                  icon: Shield,
                  color: "bg-red-100 text-red-600",
                  title: "Audit de sécurité",
                  desc: "Analyse complète des vulnérabilités",
                },
                {
                  icon: Search,
                  color: "bg-purple-100 text-purple-600",
                  title: "Analyse SEO",
                  desc: "Optimisation pour les moteurs de recherche",
                },
                {
                  icon: ActivitySquare,
                  color: "bg-blue-100 text-blue-600",
                  title: "Performance",
                  desc: "Mesures et recommandations",
                },
              ].map(({ icon: Icon, color, title, desc }) => (
                <div
                  key={title}
                  className="p-6 rounded-xl bg-white shadow-sm border border-gray-100"
                >
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center mb-3 ${color}`}
                  >
                    <Icon className="w-6 h-6" />
                  </div>
                  <h3 className="font-medium text-gray-900 mb-1">{title}</h3>
                  <p className="text-sm text-gray-600">{desc}</p>
                </div>
              ))}
            </div>
          </div>
        </Card>
      </main>

      <footer className="py-12 px-6 text-white bg-gray-800">
        <div className="container mx-auto grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-sm font-semibold text-white mb-4">Produit</h3>
            <ul className="space-y-2">
              <FooterLink href="/features">Fonctionnalités</FooterLink>
              <FooterLink href="/pricing">Tarifs</FooterLink>
              <FooterLink href="/usage">Cas d'usage</FooterLink>
            </ul>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-white mb-4">
              Ressources
            </h3>
            <ul className="space-y-2">
              <FooterLink href="/blog">Blog</FooterLink>
              <FooterLink href="/documentation">Documentation</FooterLink>
              <FooterLink href="/guide">Guide</FooterLink>
            </ul>
          </div>
          <div>
            <h3 className="text-sm font-semibold text-white mb-4">
              Entreprise
            </h3>
            <ul className="space-y-2">
              <FooterLink href="/about">À propos</FooterLink>
              <FooterLink href="/contact">Contact</FooterLink>
              <FooterLink href="/legal">Mentions légales</FooterLink>
            </ul>
          </div>
        </div>
        <div className="container mx-auto mt-8 pt-6 border-t border-gray-600 text-center text-sm text-gray-400">
          © 2024 SiteAnalyzer. Tous droits réservés.
        </div>
      </footer>

      <Toaster position="top-right" richColors />
    </div>
  );
}
