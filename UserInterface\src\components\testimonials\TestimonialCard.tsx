import { cn } from "@/lib/utils";
import { Star } from "lucide-react";
import Card from "../ui-custom/Card";

interface TestimonialCardProps {
  name: string;
  role: string;
  comment: string;
  rating: number;
  className?: string;
}

export default function TestimonialCard({
  name,
  role,
  comment,
  rating,
  className,
}: TestimonialCardProps) {
  return (
    <Card className={cn("", className)} hoverEffect>
      <div className="flex items-center space-x-1 mb-3">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            className={cn(
              "w-4 h-4",
              i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            )}
          />
        ))}
      </div>

      <p className="text-gray-700 italic mb-4">{comment}</p>

      <div className="flex items-center mt-auto">
        <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-medium">
          {name.charAt(0)}
        </div>
        <div className="ml-3">
          <p className="text-sm font-medium text-gray-800">{name}</p>
          <p className="text-xs text-gray-500">{role}</p>
        </div>
      </div>
    </Card>
  );
}
