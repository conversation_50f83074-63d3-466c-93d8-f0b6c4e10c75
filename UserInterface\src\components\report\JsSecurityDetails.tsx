import React from "react";
import {
  HelpCircle,
  AlertTriangle,
  Check,
  Shield,
  Package,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// InfoTooltip Component - Reusable tooltip for technical terms
const InfoTooltip: React.FC<{ term: string; explanation: string }> = ({
  term,
  explanation,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-flex items-center cursor-help">
          {term}
          <HelpCircle className="h-3.5 w-3.5 ml-1 text-gray-400" />
        </span>
      </TooltipTrigger>
      <TooltipContent className="max-w-xs bg-gray-800 text-white border-gray-700">
        <p className="text-xs">{explanation}</p>
      </TooltipContent>
    </Tooltip>
  );
};

// Section Introduction Component
const SectionIntro: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="mb-4 p-3 neo-card bg-gray-50/50 border-l-4 border-blue-500/50">
      <p className="text-sm text-gray-600">{children}</p>
    </div>
  );
};

// Security Impact Component
const SecurityImpact: React.FC<{
  severity: "high" | "medium" | "low";
  children: React.ReactNode;
}> = ({ severity, children }) => {
  const bgColor =
    severity === "high"
      ? "bg-red-500/10 border-red-500/30"
      : severity === "medium"
      ? "bg-yellow-500/10 border-yellow-500/30"
      : "bg-blue-500/10 border-blue-500/30";

  const icon =
    severity === "high" ? (
      <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
    ) : severity === "medium" ? (
      <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />
    ) : (
      <HelpCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
    );

  return (
    <div className={`p-2 rounded-lg flex items-start gap-2 ${bgColor}`}>
      {icon}
      <p className="text-xs text-gray-700">{children}</p>
    </div>
  );
};

interface JsLibrary {
  name: string;
  version: string;
  vulnerabilities: number;
  severity: "critical" | "high" | "medium" | "low" | "none";
  outdated: boolean;
  latestVersion?: string;
  description?: string;
  vulnerabilityDetails?: {
    id: string;
    title: string;
    severity: string;
    description: string;
    fixedIn?: string;
  }[];
}

interface JsSecurityDetailsProps {
  libraries?: JsLibrary[];
  totalVulnerabilities?: number;
  criticalVulnerabilities?: number;
  highVulnerabilities?: number;
  mediumVulnerabilities?: number;
  lowVulnerabilities?: number;
}

const JsSecurityDetails: React.FC<JsSecurityDetailsProps> = ({
  libraries = [],
  totalVulnerabilities = 0,
  criticalVulnerabilities = 0,
  highVulnerabilities = 0,
  mediumVulnerabilities = 0,
  lowVulnerabilities = 0,
}) => {
  // Sort libraries by severity and vulnerability count
  const sortedLibraries = [...libraries].sort((a, b) => {
    const severityOrder = { critical: 0, high: 1, medium: 2, low: 3, none: 4 };
    if (severityOrder[a.severity] !== severityOrder[b.severity]) {
      return severityOrder[a.severity] - severityOrder[b.severity];
    }
    return b.vulnerabilities - a.vulnerabilities;
  });

  // Get libraries with vulnerabilities
  const vulnerableLibraries = libraries.filter(
    (lib) => lib.vulnerabilities > 0
  );
  const outdatedLibraries = libraries.filter((lib) => lib.outdated);
  const secureLibraries = libraries.filter(
    (lib) => lib.vulnerabilities === 0 && !lib.outdated
  );

  // Get popular libraries (React, jQuery, etc.)
  const popularLibraries = libraries.filter(
    (lib) =>
      lib.name.toLowerCase().includes("react") ||
      lib.name.toLowerCase().includes("jquery") ||
      lib.name.toLowerCase().includes("vue") ||
      lib.name.toLowerCase().includes("angular")
  );

  // Helper function to get severity class
  const getSeverityClass = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case "critical":
        return "bg-red-100 text-red-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "low":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-green-100 text-green-800";
    }
  };

  if (libraries.length === 0) {
    return (
      <div className="space-y-4">
        <SectionIntro>
          La sécurité JavaScript analyse les bibliothèques et frameworks
          utilisés par votre site web pour détecter les vulnérabilités connues
          et les versions obsolètes qui pourraient exposer votre site à des
          risques.
        </SectionIntro>
        <p className="text-gray-600">
          Aucune bibliothèque JavaScript n'a été détectée ou l'analyse n'a pas
          pu être effectuée.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <SectionIntro>
        <div className="flex items-center gap-2 mb-2">
          <span className="text-purple-600 font-semibold">
            📦 Bibliothèques JavaScript
          </span>
        </div>
        Les bibliothèques JavaScript sont comme des briques de construction pour
        votre site web. Mais comme tout logiciel, elles peuvent contenir des
        vulnérabilités qui exposent votre site à des risques. Maintenir ces
        bibliothèques à jour est essentiel pour la sécurité de votre site.
      </SectionIntro>

      {/* Library Safety Report Card */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="p-4 neo-card bg-white">
          <h4 className="font-medium text-gray-800 mb-3 flex items-center">
            <Package className="h-5 w-5 mr-2 text-purple-500" />
            Résumé des Bibliothèques
          </h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span className="text-sm text-gray-700">
                Bibliothèques détectées
              </span>
              <span className="font-medium">{libraries.length}</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span className="text-sm text-gray-700">
                Bibliothèques vulnérables
              </span>
              <span
                className={`font-medium ${
                  vulnerableLibraries.length > 0
                    ? "text-red-600"
                    : "text-green-600"
                }`}
              >
                {vulnerableLibraries.length}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span className="text-sm text-gray-700">
                Bibliothèques obsolètes
              </span>
              <span
                className={`font-medium ${
                  outdatedLibraries.length > 0
                    ? "text-orange-600"
                    : "text-green-600"
                }`}
              >
                {outdatedLibraries.length}
              </span>
            </div>
          </div>
        </div>

        <div className="p-4 neo-card bg-white">
          <h4 className="font-medium text-gray-800 mb-3 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
            Vulnérabilités Détectées
          </h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center p-2 bg-red-50 rounded">
              <span className="text-sm text-red-700">Critiques</span>
              <span className="font-medium text-red-700">
                {criticalVulnerabilities}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-orange-50 rounded">
              <span className="text-sm text-orange-700">Élevées</span>
              <span className="font-medium text-orange-700">
                {highVulnerabilities}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-yellow-50 rounded">
              <span className="text-sm text-yellow-700">Moyennes</span>
              <span className="font-medium text-yellow-700">
                {mediumVulnerabilities}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-blue-50 rounded">
              <span className="text-sm text-blue-700">Faibles</span>
              <span className="font-medium text-blue-700">
                {lowVulnerabilities}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Dependency Heatmap */}
      {totalVulnerabilities > 0 && (
        <div className="p-4 neo-card bg-white mb-6">
          <h4 className="font-medium text-gray-800 mb-3">
            Carte de Chaleur des Vulnérabilités
          </h4>
          <div className="space-y-2">
            {criticalVulnerabilities > 0 && (
              <div className="flex items-center">
                <div className="w-24 text-xs text-gray-600">Critique</div>
                <div className="flex-1 h-6 bg-gray-100 rounded overflow-hidden">
                  <div
                    className="h-full bg-red-500"
                    style={{
                      width: `${
                        (criticalVulnerabilities / totalVulnerabilities) * 100
                      }%`,
                    }}
                  ></div>
                </div>
                <div className="w-10 text-right text-xs text-gray-600 ml-2">
                  {criticalVulnerabilities}
                </div>
              </div>
            )}
            {highVulnerabilities > 0 && (
              <div className="flex items-center">
                <div className="w-24 text-xs text-gray-600">Élevée</div>
                <div className="flex-1 h-6 bg-gray-100 rounded overflow-hidden">
                  <div
                    className="h-full bg-orange-500"
                    style={{
                      width: `${
                        (highVulnerabilities / totalVulnerabilities) * 100
                      }%`,
                    }}
                  ></div>
                </div>
                <div className="w-10 text-right text-xs text-gray-600 ml-2">
                  {highVulnerabilities}
                </div>
              </div>
            )}
            {mediumVulnerabilities > 0 && (
              <div className="flex items-center">
                <div className="w-24 text-xs text-gray-600">Moyenne</div>
                <div className="flex-1 h-6 bg-gray-100 rounded overflow-hidden">
                  <div
                    className="h-full bg-yellow-500"
                    style={{
                      width: `${
                        (mediumVulnerabilities / totalVulnerabilities) * 100
                      }%`,
                    }}
                  ></div>
                </div>
                <div className="w-10 text-right text-xs text-gray-600 ml-2">
                  {mediumVulnerabilities}
                </div>
              </div>
            )}
            {lowVulnerabilities > 0 && (
              <div className="flex items-center">
                <div className="w-24 text-xs text-gray-600">Faible</div>
                <div className="flex-1 h-6 bg-gray-100 rounded overflow-hidden">
                  <div
                    className="h-full bg-blue-500"
                    style={{
                      width: `${
                        (lowVulnerabilities / totalVulnerabilities) * 100
                      }%`,
                    }}
                  ></div>
                </div>
                <div className="w-10 text-right text-xs text-gray-600 ml-2">
                  {lowVulnerabilities}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Secure Libraries Section - Only show if there are no vulnerable libraries but there are secure ones */}
      {vulnerableLibraries.length === 0 && secureLibraries.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-800">
            Bibliothèques Sécurisées
          </h4>
          <div className="space-y-4">
            {/* Show popular libraries first if available */}
            {(popularLibraries.length > 0 ? popularLibraries : secureLibraries)
              .slice(0, 3)
              .map((lib, index) => (
                <div
                  key={`secure-lib-${index}`}
                  className="p-4 neo-card border-l-4 border-green-500 bg-green-50"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h5 className="font-medium text-gray-800 flex items-center">
                        <InfoTooltip
                          term={`${lib.name} ${
                            lib.version || "version inconnue"
                          }`}
                          explanation={
                            lib.description ||
                            `Bibliothèque JavaScript utilisée par votre site web.`
                          }
                        />
                        <span className="ml-2 text-xs px-2 py-0.5 rounded bg-green-100 text-green-800">
                          SÉCURISÉ
                        </span>
                      </h5>
                      <p className="text-xs text-gray-600 mt-1">
                        Aucune vulnérabilité détectée
                      </p>
                    </div>
                  </div>

                  <div className="mt-3">
                    <h6 className="text-xs font-medium text-gray-700">
                      Bonnes pratiques:
                    </h6>
                    <div className="flex items-start gap-2 mt-1">
                      <div className="text-green-500 font-bold text-sm">✅</div>
                      <div>
                        <p className="text-sm text-gray-700">
                          Continuez à maintenir cette bibliothèque à jour et à
                          suivre les meilleures pratiques de sécurité
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}

            {/* If we have more than 3 secure libraries, show a summary card */}
            {secureLibraries.length > 3 && (
              <div className="p-4 neo-card border-l-4 border-green-500 bg-green-50">
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="font-medium text-gray-800">
                      {secureLibraries.length - 3} autres bibliothèques
                      sécurisées
                    </h5>
                    <p className="text-xs text-gray-600 mt-1">
                      Toutes vos bibliothèques JavaScript sont sécurisées et à
                      jour
                    </p>
                  </div>
                  <span className="text-xs px-2 py-1 rounded bg-green-100 text-green-800">
                    EXCELLENT
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Vulnerable Libraries */}
      {vulnerableLibraries.length > 0 && (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-800">
            Bibliothèques Vulnérables
          </h4>
          <div className="space-y-4">
            {vulnerableLibraries.map((lib, index) => (
              <div
                key={`vuln-lib-${index}`}
                className={`p-4 neo-card border-l-4 ${
                  lib.severity === "critical"
                    ? "border-red-500 bg-red-50"
                    : lib.severity === "high"
                    ? "border-orange-500 bg-orange-50"
                    : lib.severity === "medium"
                    ? "border-yellow-500 bg-yellow-50"
                    : "border-blue-500 bg-blue-50"
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="font-medium text-gray-800 flex items-center">
                      <InfoTooltip
                        term={`${lib.name} ${lib.version}`}
                        explanation={
                          lib.description ||
                          `Bibliothèque JavaScript utilisée par votre site web.`
                        }
                      />
                      <span
                        className={`ml-2 text-xs px-2 py-0.5 rounded ${getSeverityClass(
                          lib.severity
                        )}`}
                      >
                        {lib.severity.toUpperCase()}
                      </span>
                    </h5>
                    <p className="text-xs text-gray-600 mt-1">
                      {lib.vulnerabilities} vulnérabilité
                      {lib.vulnerabilities > 1 ? "s" : ""} détectée
                      {lib.vulnerabilities > 1 ? "s" : ""}
                    </p>
                  </div>
                  {lib.outdated && lib.latestVersion && (
                    <div className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      Mise à jour disponible: {lib.latestVersion}
                    </div>
                  )}
                </div>

                {lib.vulnerabilityDetails &&
                  lib.vulnerabilityDetails.length > 0 && (
                    <div className="mt-3 space-y-2">
                      <h6 className="text-xs font-medium text-gray-700">
                        Détails des vulnérabilités:
                      </h6>
                      <ul className="space-y-2">
                        {lib.vulnerabilityDetails.map((vuln, vIndex) => (
                          <li
                            key={`vuln-detail-${index}-${vIndex}`}
                            className="text-xs bg-white p-2 rounded border border-gray-200"
                          >
                            <div className="flex items-center">
                              <span
                                className={`inline-block w-2 h-2 rounded-full mr-2 ${
                                  vuln.severity === "critical"
                                    ? "bg-red-500"
                                    : vuln.severity === "high"
                                    ? "bg-orange-500"
                                    : vuln.severity === "medium"
                                    ? "bg-yellow-500"
                                    : "bg-blue-500"
                                }`}
                              ></span>
                              <strong>{vuln.title}</strong>
                              {vuln.id && (
                                <span className="ml-1 text-gray-500">
                                  ({vuln.id})
                                </span>
                              )}
                            </div>
                            <p className="mt-1 text-gray-600">
                              {vuln.description}
                            </p>
                            {vuln.fixedIn && (
                              <p className="mt-1 text-green-600">
                                Corrigé dans la version {vuln.fixedIn}
                              </p>
                            )}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                <div className="mt-3">
                  <h6 className="text-xs font-medium text-gray-700">
                    Action recommandée:
                  </h6>
                  <div className="flex items-start gap-2 mt-1">
                    <div className="text-purple-500 font-bold text-sm">✅</div>
                    <div>
                      {lib.latestVersion ? (
                        <p className="text-sm text-gray-700">
                          Mettre à jour vers la version {lib.latestVersion}{" "}
                          (Guide de migration de 30 min)
                        </p>
                      ) : (
                        <p className="text-sm text-gray-700">
                          Remplacer par une alternative plus sécurisée ou mettre
                          à jour vers la dernière version
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* All Libraries Table */}
      <details className="neo-card p-4">
        <summary className="font-medium text-gray-800 cursor-pointer">
          Voir toutes les bibliothèques JavaScript ({libraries.length})
        </summary>
        <div className="mt-4 overflow-x-auto">
          <table className="w-full">
            <thead className="text-left bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-gray-600">Bibliothèque</th>
                <th className="px-4 py-2 text-gray-600">Version</th>
                <th className="px-4 py-2 text-gray-600">Statut</th>
                <th className="px-4 py-2 text-gray-600">Vulnérabilités</th>
              </tr>
            </thead>
            <tbody>
              {sortedLibraries.map((lib, index) => (
                <tr
                  key={`lib-${index}`}
                  className="border-t border-gray-200 hover:bg-gray-100 transition"
                >
                  <td className="px-4 py-3 text-gray-800">
                    <InfoTooltip
                      term={lib.name}
                      explanation={
                        lib.description ||
                        `Bibliothèque JavaScript utilisée par votre site web.`
                      }
                    />
                  </td>
                  <td className="px-4 py-3 text-gray-700">
                    {lib.version}
                    {lib.outdated && lib.latestVersion && (
                      <span className="ml-2 text-xs text-blue-600">
                        (Dernière: {lib.latestVersion})
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-3">
                    <span
                      className={`px-2 py-1 rounded text-xs ${
                        lib.vulnerabilities > 0
                          ? getSeverityClass(lib.severity)
                          : lib.outdated
                          ? "bg-blue-100 text-blue-800"
                          : "bg-green-100 text-green-800"
                      }`}
                    >
                      {lib.vulnerabilities > 0
                        ? lib.severity.toUpperCase()
                        : lib.outdated
                        ? "OBSOLÈTE"
                        : "SÉCURISÉ"}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-gray-700">
                    {lib.vulnerabilities > 0 ? (
                      <span className="text-red-600 font-medium">
                        {lib.vulnerabilities}
                      </span>
                    ) : (
                      <span className="text-green-600">0</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </details>

      {/* Recommendations */}
      <div className="p-4 neo-card bg-gradient-to-r from-gray-50 to-purple-50">
        <h4 className="font-medium text-gray-800 mb-3">Recommandations</h4>
        <ul className="space-y-3">
          {vulnerableLibraries.length > 0 && (
            <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
              <div className="text-red-500 text-xl">🔄</div>
              <div>
                <h5 className="text-sm font-medium text-gray-700">
                  Mettre à jour les bibliothèques vulnérables
                </h5>
                <p className="text-xs text-gray-600 mt-1">
                  Priorisez la mise à jour des {vulnerableLibraries.length}{" "}
                  bibliothèques contenant des vulnérabilités connues.
                </p>
              </div>
            </li>
          )}

          {outdatedLibraries.length > 0 && vulnerableLibraries.length === 0 && (
            <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
              <div className="text-blue-500 text-xl">📦</div>
              <div>
                <h5 className="text-sm font-medium text-gray-700">
                  Mettre à jour les bibliothèques obsolètes
                </h5>
                <p className="text-xs text-gray-600 mt-1">
                  Bien que non vulnérables, {outdatedLibraries.length}{" "}
                  bibliothèques sont obsolètes et devraient être mises à jour.
                </p>
              </div>
            </li>
          )}

          {vulnerableLibraries.length === 0 &&
            outdatedLibraries.length === 0 &&
            secureLibraries.length > 0 && (
              <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
                <div className="text-green-500 text-xl">✅</div>
                <div>
                  <h5 className="text-sm font-medium text-gray-700">
                    Excellente sécurité des bibliothèques JavaScript
                  </h5>
                  <p className="text-xs text-gray-600 mt-1">
                    Toutes vos bibliothèques JavaScript ({libraries.length})
                    sont sécurisées et à jour. Continuez à maintenir cette bonne
                    pratique.
                  </p>
                </div>
              </li>
            )}

          <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
            <div className="text-purple-500 text-xl">🔒</div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                Utiliser un gestionnaire de dépendances
              </h5>
              <p className="text-xs text-gray-600 mt-1">
                Utilisez npm ou yarn avec des outils comme npm audit pour
                surveiller automatiquement les vulnérabilités.
              </p>
            </div>
          </li>

          <li className="flex items-start gap-2 p-3 bg-white rounded-lg shadow-sm">
            <div className="text-green-500 text-xl">🔄</div>
            <div>
              <h5 className="text-sm font-medium text-gray-700">
                Mettre en place des mises à jour régulières
              </h5>
              <p className="text-xs text-gray-600 mt-1">
                Planifiez des mises à jour mensuelles de vos dépendances
                JavaScript pour maintenir votre site sécurisé.
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default JsSecurityDetails;
