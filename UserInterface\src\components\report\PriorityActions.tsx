import React from "react";
import { ReportAction } from "@/types/report";
import ActionPriorityGroup from "./ActionPriorityGroup";
import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { CheckCircle } from "lucide-react";
import { useTranslation } from "react-i18next";

interface PriorityActionsProps {
  actions: ReportAction[];
}

const PriorityActions: React.FC<PriorityActionsProps> = ({ actions }) => {
  const { t } = useTranslation();
  const highPriorityActions = actions.filter((action) => action.priority === 3);
  const mediumPriorityActions = actions.filter(
    (action) => action.priority === 2
  );
  const lowPriorityActions = actions.filter((action) => action.priority === 1);

  // Security/SEO counts removed as they're no longer needed for the summary

  const hasSecurityActions = (actions: ReportAction[]) =>
    actions.some(
      (action) =>
        action.type === "security" ||
        action.type === "SSL/TLS" ||
        action.type === "HTTP Headers" ||
        action.type === "Vulnerabilities" ||
        action.type === "JavaScript Security" ||
        action.type === "API Security"
    );

  return (
    <div
      id="recommended-actions"
      className="p-6 rounded-xl bg-[#F8F9FA] border-2 border-gray-200 shadow-[0_8px_32px_rgba(169,203,255,0.2)] hover:shadow-[0_8px_32px_rgba(169,203,255,0.3)] transition-all duration-500 neo-card"
    >
      <div className="mb-5">
        <div className="flex items-center gap-3 mb-2">
          <h2 className="text-xl font-bold text-gray-800">
            {t("report.action_plan")}
          </h2>
        </div>
        <div className="w-full h-1 bg-gradient-to-r from-[#1eaedb] via-[#9b87f5] to-[#d946ef] rounded-full shadow-[0_0_6px_rgba(155,135,245,0.3)] mb-4"></div>

        {/* Summary section removed as requested */}
      </div>

      {actions.length > 0 ? (
        <div className="space-y-6">
          {highPriorityActions.length > 0 && (
            <div className="animate-fade-in">
              <ActionPriorityGroup
                title={t("report.critical_actions")}
                actions={highPriorityActions}
                color={
                  hasSecurityActions(highPriorityActions) ? "security" : "seo"
                }
                priority={3}
                defaultOpen={false}
              />
            </div>
          )}

          {mediumPriorityActions.length > 0 && (
            <div
              className="animate-fade-in"
              style={{ animationDelay: "100ms" }}
            >
              <ActionPriorityGroup
                title={t("report.important_actions")}
                actions={mediumPriorityActions}
                color={
                  hasSecurityActions(mediumPriorityActions) ? "security" : "seo"
                }
                priority={2}
                defaultOpen={false}
              />
            </div>
          )}

          {lowPriorityActions.length > 0 && (
            <div
              className="animate-fade-in"
              style={{ animationDelay: "200ms" }}
            >
              <ActionPriorityGroup
                title={t("report.suggested_improvements")}
                actions={lowPriorityActions}
                color={
                  hasSecurityActions(lowPriorityActions) ? "security" : "seo"
                }
                priority={1}
                defaultOpen={false}
              />
            </div>
          )}
        </div>
      ) : (
        <Alert className="bg-[#F5F5F5] border-2 border-gray-200 shadow-[0_8px_32px_rgba(22,163,74,0.2)] hover:shadow-[0_8px_32px_rgba(22,163,74,0.3)] transition-all duration-300">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0 drop-shadow-[0_0_8px_rgba(22,163,74,0.5)]" />
            <div>
              <AlertTitle className="text-gray-800 font-medium text-lg">
                {t("report.no_actions")}
              </AlertTitle>
              <AlertDescription className="text-gray-700">
                {t("report.no_actions_desc")}
              </AlertDescription>
            </div>
          </div>
        </Alert>
      )}

      {actions.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200 text-sm text-gray-700 text-center font-medium">
          <p>{t("report.review_implement")}</p>
        </div>
      )}
    </div>
  );
};

export default PriorityActions;
