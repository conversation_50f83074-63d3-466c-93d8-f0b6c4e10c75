import { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { CheckCircle } from "lucide-react";
import Layout from "@/components/Layout";
import Card from "@/components/ui-custom/Card";
import Button from "@/components/ui-custom/Button";

export default function SuccessReset() {
  const navigate = useNavigate();

  useEffect(() => {
    // Simulate auto-redirect after 5 seconds
    const timer = setTimeout(() => {
      navigate("/login");
    }, 5000);

    return () => clearTimeout(timer);
  }, [navigate]);

  return (
    <Layout showNav={true} navType="second">
      <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
        <Card className="w-full max-w-md backdrop-blur-sm rounded-xl shadow-lg p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-10 h-10 text-green-500" />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Mot de passe réinitialisé !
          </h1>

          <p className="text-gray-600 mb-6">
            Votre mot de passe a été modifié avec succès.
          </p>

          <Link to="/login">
            <Button className="w-full">Se connecter</Button>
          </Link>
        </Card>
      </div>
    </Layout>
  );
}
