using Microsoft.EntityFrameworkCore;
using SiteCheckerApp.Models;

namespace SiteCheckerApp.Data
{
      public class AppDbContext : DbContext
      {
            public AppDbContext(DbContextOptions<AppDbContext> options)
                : base(options)
            {
            }

            public DbSet<User> Users { get; set; }
            public DbSet<RefreshToken> RefreshTokens { get; set; }
            public DbSet<ScanHistory> ScanHistories { get; set; }

            protected override void OnModelCreating(ModelBuilder modelBuilder)
            {
                  modelBuilder.Entity<User>(entity =>
                  {
                        entity.HasKey(e => e.Id);

                        entity.Property(e => e.Username)
                        .IsRequired()
                        .HasMaxLength(255);

                        entity.Property(e => e.Email)
                        .IsRequired()
                        .HasMaxLength(255);

                        entity.HasIndex(e => e.Email).IsUnique();

                        entity.Property(e => e.PasswordHash)
                        .IsRequired();

                        entity.Property(e => e.VerificationToken)
                        .HasMaxLength(100);

                        entity.Property(e => e.VerificationTokenExpiry);

                        entity.Property(e => e.Otp)
                        .IsRequired(false);

                        entity.Property(e => e.OtpExpiry);

                        entity.Property(e => e.PasswordResetToken)
                        .IsRequired(false);

                        entity.Property(e => e.PasswordResetTokenExpiry);

                        entity.Property(e => e.IdRole)
                        .IsRequired();

                        entity.Property(e => e.BillingCycle)
                        .IsRequired()
                        .HasMaxLength(50);

                        entity.Property(e => e.IsVerified)
                        .IsRequired();

                        entity.Property(e => e.IsTwoFactorEnabled)
                        .IsRequired();

                        entity.Property(e => e.CreatedAt)
                        .IsRequired();

                        entity.Property(e => e.UpdatedAt)
                        .IsRequired();

                        entity.HasIndex(e => e.RefreshToken).IsUnique();

                        entity.Property(e => e.TokenVersion)
                            .IsConcurrencyToken()
                            .HasDefaultValue(1);
                  });

                  modelBuilder.Entity<RefreshToken>(entity =>
                  {
                        entity.HasKey(e => e.Id);
                        entity.Property(e => e.Token).IsRequired();
                        entity.Property(e => e.UserId).IsRequired();
                        entity.Property(e => e.Expires).IsRequired();
                        entity.Property(e => e.Created).IsRequired();
                        entity.Property(e => e.Revoked).IsRequired();
                  });
            }
      }
}
