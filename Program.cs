using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using SiteCheckerApp.Services;
using SiteCheckerApp.Repositories;
using Microsoft.EntityFrameworkCore;
using SiteCheckerApp.Data;
using System.Threading.RateLimiting;
using DotNetEnv;

// Charger les variables d'environnement depuis le fichier .env
try
{
    Env.Load();
    Console.WriteLine("Variables d'environnement chargées avec succès depuis le fichier .env");

    // Afficher quelques variables pour vérification
    var jwtKey = Environment.GetEnvironmentVariable("JWT_SECRET_KEY");
    Console.WriteLine($"JWT_SECRET_KEY: {(jwtKey?.Length > 10 ? jwtKey[..10] + "..." : jwtKey)}");
    Console.WriteLine($"JWT_ISSUER: {Environment.GetEnvironmentVariable("JWT_ISSUER")}");
    Console.WriteLine($"JWT_AUDIENCE: {Environment.GetEnvironmentVariable("JWT_AUDIENCE")}");
}
catch (Exception ex)
{
    Console.WriteLine($"Erreur lors du chargement des variables d'environnement: {ex.Message}");
}

var builder = WebApplication.CreateBuilder(args);

// Ajouter une fonction pour remplacer les placeholders dans la configuration
builder.Configuration.AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);
builder.Configuration.AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true);

// Remplacer les placeholders dans la configuration par les valeurs des variables d'environnement
ReplaceConfigPlaceholders(builder.Configuration);

// Set backend URL
builder.WebHost.UseUrls("http://localhost:5012");

// Configure JWT settings
var jwtSettings = builder.Configuration.GetSection("Jwt");
var key = Environment.GetEnvironmentVariable("JWT_SECRET_KEY") ?? jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey is not configured");
var issuer = Environment.GetEnvironmentVariable("JWT_ISSUER") ?? jwtSettings["Issuer"] ?? "https://sitechecker-api.com";
var audience = Environment.GetEnvironmentVariable("JWT_AUDIENCE") ?? jwtSettings["Audience"] ?? "https://sitechecker-client.com";

// Ensure these values are not placeholders
if (issuer.Contains("${") || audience.Contains("${"))
{
    Console.WriteLine("WARNING: JWT issuer or audience contains placeholder values. Authentication will fail!");
    // Use default values if placeholders are detected
    issuer = "https://sitechecker-api.com";
    audience = "https://sitechecker-client.com";
}

// Afficher les valeurs pour le débogage
Console.WriteLine($"Program.cs - JWT Issuer: {issuer}");
Console.WriteLine($"Program.cs - JWT Audience: {audience}");

// Utiliser Encoding.UTF8.GetBytes au lieu de Convert.FromBase64String
var keyBytes = System.Text.Encoding.UTF8.GetBytes(key);

// Vérifier si la clé est assez longue pour HS256 (au moins 32 octets / 256 bits)
if (keyBytes.Length < 32)
{
    Console.WriteLine($"Attention: La clé JWT est trop courte ({keyBytes.Length * 8} bits). Extension à 256 bits.");
    // Étendre la clé à 32 octets en répétant ou en ajoutant des données
    var extendedKeyBytes = new byte[32];

    // Copier les octets existants
    Array.Copy(keyBytes, extendedKeyBytes, Math.Min(keyBytes.Length, 32));

    // Remplir le reste avec des valeurs dérivées de la clé originale
    for (int i = keyBytes.Length; i < 32; i++)
    {
        extendedKeyBytes[i] = (byte)(keyBytes[i % keyBytes.Length] ^ (i * 13));
    }

    keyBytes = extendedKeyBytes;
    Console.WriteLine($"Clé JWT étendue à {keyBytes.Length * 8} bits.");
}

var signingKey = new SymmetricSecurityKey(keyBytes)
{
    KeyId = "sitechecker-key-1"
};

// Add services
builder.Services.AddControllers();

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddCookie(options =>
{
    options.Cookie.Name = "refresh_token";
    options.Cookie.HttpOnly = true;
#if DEBUG
    options.Cookie.SecurePolicy = CookieSecurePolicy.None; // Allow HTTP for local dev
    options.Cookie.SameSite = SameSiteMode.Lax; // Relaxed for local dev
#else
    options.Cookie.SecurePolicy = CookieSecurePolicy.Always; // HTTPS only in production
    options.Cookie.SameSite = SameSiteMode.Strict;
#endif
    options.SlidingExpiration = true;
    options.ExpireTimeSpan = TimeSpan.FromDays(7);
    options.LoginPath = "/api/auth/login";
    options.LogoutPath = "/api/auth/logout";
    options.Events.OnRedirectToLogin = context =>
    {
        context.Response.StatusCode = 401;
        return Task.CompletedTask;
    };
    options.Events.OnRedirectToAccessDenied = context =>
    {
        context.Response.StatusCode = 403;
        return Task.CompletedTask;
    };
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = issuer,
        ValidAudience = audience,
        IssuerSigningKey = signingKey,
        NameClaimType = System.Security.Claims.ClaimTypes.NameIdentifier,
        RoleClaimType = System.Security.Claims.ClaimTypes.Role,
        ValidAlgorithms = [SecurityAlgorithms.HmacSha256],
        ClockSkew = TimeSpan.FromMinutes(5) // Added clock skew tolerance
    };

    options.Events = new JwtBearerEvents
    {
        OnMessageReceived = context =>
        {
            // Try to get token from cookie if Authorization header is missing
            var accessToken = context.Request.Cookies["access_token"];
            if (!string.IsNullOrEmpty(accessToken))
            {
                context.Token = accessToken;
            }
            return Task.CompletedTask;
        },
        OnAuthenticationFailed = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            logger.LogError(context.Exception, "Authentication failed.");
            if (context.Exception != null)
            {
                logger.LogError("Authentication failure details: {Message}", context.Exception.Message);
                logger.LogError("Stack trace: {StackTrace}", context.Exception.StackTrace);
            }
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<Program>>();
            var claims = context.Principal?.Claims;
            if (claims != null)
            {
                foreach (var claim in claims)
                {
                    logger.LogInformation("Token validated claim: Type={Type}, Value={Value}", claim.Type, claim.Value);
                }
            }
            else
            {
                logger.LogWarning("Token validated but no claims found.");
            }
            return Task.CompletedTask;
        }
    };
});

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.WithOrigins(
            "http://localhost:8080",
            "http://localhost:5012"

        )
        .AllowAnyMethod()
        .AllowAnyHeader()
        .AllowCredentials();
    });
});

// Add secure headers (middleware will be applied later)
builder.Services.AddHsts(options =>
{
    options.Preload = true;
    options.IncludeSubDomains = true;
    options.MaxAge = TimeSpan.FromDays(365);
});

// Construire la chaîne de connexion à partir des variables d'environnement
string connectionString = $"Server={Environment.GetEnvironmentVariable("DB_SERVER") ?? "localhost"};" +
                         $"Database={Environment.GetEnvironmentVariable("DB_NAME") ?? "SiteChecker_Db"};" +
                         $"User Id={Environment.GetEnvironmentVariable("DB_USER") ?? "adminUser"};" +
                         $"Password={Environment.GetEnvironmentVariable("DB_PASSWORD") ?? "255869"};" +
                         $"TrustServerCertificate={Environment.GetEnvironmentVariable("DB_TRUST_SERVER_CERTIFICATE") ?? "True"};" +
                         $"MultipleActiveResultSets={Environment.GetEnvironmentVariable("DB_MULTIPLE_ACTIVE_RESULT_SETS") ?? "true"}";

// Masquer le mot de passe pour l'affichage
string connectionStringForDisplay = connectionString;
var passwordValue = Environment.GetEnvironmentVariable("DB_PASSWORD") ?? "255869";
connectionStringForDisplay = connectionStringForDisplay.Replace(passwordValue, "***");

Console.WriteLine($"Connection string: {connectionStringForDisplay}");

// Add EF Core database context
builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlServer(connectionString)
           .EnableSensitiveDataLogging()
           .LogTo(Console.WriteLine, LogLevel.Information));

// Dependency injection for services and repositories
builder.Services.AddScoped<IRefreshTokenService, RefreshTokenService>();
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IEmailService, EmailService>();
builder.Services.AddScoped<IScanHistoryService, ScanHistoryService>();

builder.Services.AddHttpClient<ICaptchaService, CaptchaService>();
builder.Services.AddScoped<ICaptchaService, CaptchaService>();

// Add rate limiting policies
builder.Services.AddRateLimiter(options =>
{
    options.AddPolicy("login", context =>
        RateLimitPartition.GetSlidingWindowLimiter(
            partitionKey: context.Connection.RemoteIpAddress?.ToString() ?? "unknown",
            factory: _ => new SlidingWindowRateLimiterOptions
            {
                Window = TimeSpan.FromMinutes(5),
                SegmentsPerWindow = 5,
                PermitLimit = 10, // 10 attempts per 5 mins
                QueueLimit = 0
            }));

    options.AddPolicy("registration", context =>
        RateLimitPartition.GetFixedWindowLimiter(
            partitionKey: context.Connection.RemoteIpAddress?.ToString() ?? "unknown",
            factory: _ => new FixedWindowRateLimiterOptions
            {
                Window = TimeSpan.FromHours(1),
                PermitLimit = 3, // 3 registrations per hour per IP
                QueueLimit = 0
            }));
});

// Swagger configuration with JWT support
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "SiteChecker API", Version = "v1" });

    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });
});

var app = builder.Build();

// Enable Swagger
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "SiteChecker API V1");
});

// Enable HSTS in non-development
if (!app.Environment.IsDevelopment())
{
    app.UseHsts();
}

// Add secure headers middleware
app.Use(async (context, next) =>
{
    context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
    context.Response.Headers.Append("X-Frame-Options", "DENY");
    context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
    context.Response.Headers.Append("Referrer-Policy", "strict-origin-when-cross-origin");
    context.Response.Headers.Append("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' http://localhost:8080");
    context.Response.Headers.Append("Strict-Transport-Security", "max-age=********; includeSubDomains; preload");
    await next();
});

// app.UseHttpsRedirection(); // optional if you're not using HTTPS in local dev

app.UseRouting();

// 🟢 CORS middleware comes BEFORE auth
app.UseCors("AllowFrontend");

app.UseAuthentication();
app.UseAuthorization();

// Map controllers
app.MapControllers();

// Default route redirects to Swagger
app.MapGet("/", () => Results.Redirect("/swagger"));

// Start the app
app.Run();

// Fonction pour remplacer les placeholders dans la configuration par les valeurs des variables d'environnement
static void ReplaceConfigPlaceholders(IConfiguration configuration)
{
    // Parcourir toutes les sections de la configuration
    foreach (var section in configuration.GetChildren())
    {
        // Si la section a des enfants, appliquer la fonction récursivement
        if (section.GetChildren().Any())
        {
            ReplaceConfigPlaceholders(section);
        }
        else
        {
            // Vérifier si la valeur est un placeholder (format ${VARIABLE})
            var value = section.Value;
            if (!string.IsNullOrEmpty(value))
            {
                // Cas 1: La valeur entière est un placeholder (format ${VARIABLE})
                if (value.StartsWith("${") && value.EndsWith('}'))
                {
                    // Extraire le nom de la variable d'environnement
                    var envVarName = value[2..^1]; // Utilisation de la syntaxe de tranche (slice)

                    // Récupérer la valeur de la variable d'environnement
                    var envVarValue = Environment.GetEnvironmentVariable(envVarName);

                    if (!string.IsNullOrEmpty(envVarValue))
                    {
                        // Remplacer la valeur dans la configuration
                        try
                        {
                            configuration[section.Path] = envVarValue;
                            Console.WriteLine($"Placeholder remplacé: {section.Path} = {envVarValue}");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Erreur lors du remplacement du placeholder {section.Path}: {ex.Message}");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"Variable d'environnement non trouvée: {envVarName}");
                    }
                }
                // Cas 2: La valeur contient plusieurs placeholders (format texte avec ${VARIABLE} à l'intérieur)
                else if (value.Contains("${"))
                {
                    string newValue = value;
                    int startIndex = 0;

                    // Remplacer tous les placeholders dans la chaîne
                    while ((startIndex = newValue.IndexOf("${", startIndex)) >= 0)
                    {
                        int endIndex = newValue.IndexOf('}', startIndex);
                        if (endIndex < 0) break;

                        // Extraire le nom de la variable d'environnement
                        var envVarName = newValue.Substring(startIndex + 2, endIndex - startIndex - 2);

                        // Récupérer la valeur de la variable d'environnement
                        var envVarValue = Environment.GetEnvironmentVariable(envVarName);

                        if (!string.IsNullOrEmpty(envVarValue))
                        {
                            // Remplacer le placeholder dans la chaîne
                            newValue = newValue.Remove(startIndex, endIndex - startIndex + 1).Insert(startIndex, envVarValue);
                            startIndex += envVarValue.Length;
                        }
                        else
                        {
                            Console.WriteLine($"Variable d'environnement non trouvée dans la chaîne: {envVarName}");
                            startIndex = endIndex + 1;
                        }
                    }

                    // Mettre à jour la configuration avec la nouvelle valeur
                    if (newValue != value)
                    {
                        try
                        {
                            configuration[section.Path] = newValue;
                            Console.WriteLine($"Placeholders remplacés dans: {section.Path}");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Erreur lors du remplacement des placeholders dans {section.Path}: {ex.Message}");
                        }
                    }
                }
            }
        }
    }
}
