import { encryptData, decryptData } from "./crypto-utils";
import { openDB } from "idb";

type StorageType = {
  getItem: (key: string) => Promise<string | null>;
  setItem: (key: string, value: string) => Promise<void>;
  removeItem: (key: string) => Promise<void>;
};

// Initialize IndexedDB
let db: any = null;

const initDB = async () => {
  if (db) return db;

  try {
    if (!window.isSecureContext || window.self !== window.top) {
      throw new Error("Not in a secure context or in an iframe");
    }
    db = await openDB("secureStorage", 1, {
      upgrade(db) {
        if (!db.objectStoreNames.contains("secureData")) {
          db.createObjectStore("secureData");
        }
      },
    });
    return db;
  } catch (error) {
    console.warn("IndexedDB initialization failed:", error);
    return null;
  }
};

const secureStorage = (() => {
  const memoryStore: Record<string, string> = {};

  return {
    getItem: async (key: string): Promise<string | null> => {
      try {
        const db = await initDB();
        if (db) {
          const encrypted = await db.get("secureData", key);
          if (!encrypted) return null;
          return decryptData(encrypted);
        }
        return memoryStore[key] || null;
      } catch (error) {
        console.warn("Secure storage access failed:", error);
        return memoryStore[key] || null;
      }
    },

    setItem: async (key: string, value: string): Promise<void> => {
      try {
        const db = await initDB();
        if (db) {
          await db.put("secureData", encryptData(value), key);
        } else {
          memoryStore[key] = value;
        }
      } catch (error) {
        console.warn("Secure storage access failed:", error);
        memoryStore[key] = value;
      }
    },

    removeItem: async (key: string): Promise<void> => {
      try {
        const db = await initDB();
        if (db) {
          await db.delete("secureData", key);
        } else {
          delete memoryStore[key];
        }
      } catch (error) {
        console.warn("Secure storage access failed:", error);
        delete memoryStore[key];
      }
    },
  };
})();

// Safe storage utility with fallbacks
const memoryStorage = new Map<string, string>();

// Check if storage is available
const isStorageAvailable = (): boolean => {
  try {
    const test = "test";
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (e) {
    return false;
  }
};

// Safe storage implementation
export const safeStorage = {
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      if (isStorageAvailable()) {
        await secureStorage.setItem(key, value);
      } else {
        memoryStorage.set(key, value);
      }
    } catch (error) {
      console.warn("Storage access denied, using memory storage");
      memoryStorage.set(key, value);
    }
  },

  getItem: async (key: string): Promise<string | null> => {
    try {
      if (isStorageAvailable()) {
        return await secureStorage.getItem(key);
      }
      return memoryStorage.get(key) || null;
    } catch (error) {
      console.warn("Storage access denied, using memory storage");
      return memoryStorage.get(key) || null;
    }
  },

  removeItem: async (key: string): Promise<void> => {
    try {
      if (isStorageAvailable()) {
        await secureStorage.removeItem(key);
      } else {
        memoryStorage.delete(key);
      }
    } catch (error) {
      console.warn("Storage access denied, using memory storage");
      memoryStorage.delete(key);
    }
  },

  clear: async (): Promise<void> => {
    try {
      if (isStorageAvailable()) {
        const db = await initDB();
        if (db) {
          await db.clear("secureData");
        }
      } else {
        memoryStorage.clear();
      }
    } catch (error) {
      console.warn("Storage access denied, using memory storage");
      memoryStorage.clear();
    }
  },
};
