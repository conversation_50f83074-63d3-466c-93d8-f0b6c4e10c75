import React from "react";
import { Check } from "lucide-react";

interface ValidationMessageProps {
  isValid: boolean | null;
  message: string;
  value: string;
}

const ValidationMessage: React.FC<ValidationMessageProps> = ({
  isValid,
  message,
  value,
}) => {
  if (!value) return null;

  return (
    <div className="space-y-2 mt-2 mb-4">
      <div className="flex items-center gap-2 p-2 rounded-lg bg-white/50">
        <div
          className={`w-4 h-4 rounded-full flex items-center justify-center transition-colors duration-200 ${
            isValid === true ? "bg-green-500" : "bg-red-300"
          }`}
        >
          {isValid === true && <Check className="w-3 h-3 text-white" />}
        </div>
        <span
          className={`text-sm transition-colors duration-200 ${
            isValid === true ? "text-green-600" : "text-red-500"
          }`}
        >
          {message}
        </span>
      </div>
    </div>
  );
};

export default ValidationMessage;
