import React, { JSX } from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  ArrowRight,
  Info,
  ChevronDown,
} from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

// Import interfaces from interfaces.ts
import {
  Finding,
  TechnicalDetail,
  ActionReference,
  ImpactStatement,
} from "./interfaces";

export interface UnifiedTechnicalSectionProps {
  type: "security" | "seo";
  icon: React.ReactNode;
  title: string;
  score: number;
  impactStatement: ImpactStatement;
  findings: Finding[];
  technicalDetails: TechnicalDetail[];
  actionReference: ActionReference;
  hideTitle?: boolean;
}

// Helper function to get gradient based on section type
const getGradient = (type: "security" | "seo") => {
  return type === "security"
    ? "from-blue-600 to-purple-600"
    : "from-purple-600 to-pink-600";
};

// Helper function to get score color and label
const getScoreStyle = (score: number) => {
  if (score >= 80)
    return {
      color: "text-green-600",
      bg: "bg-green-100",
      label: "Excellent",
    };
  if (score >= 60)
    return {
      color: "text-yellow-600",
      bg: "bg-yellow-100",
      label: "Moyen",
    };
  if (score >= 40)
    return {
      color: "text-orange-600",
      bg: "bg-orange-100",
      label: "Faible",
    };
  return {
    color: "text-red-600",
    bg: "bg-red-100",
    label: "Critique",
  };
};

// Technical term tooltip component
const TechnicalTerm: React.FC<{
  term: string;
  explanation: string;
  children?: React.ReactNode;
}> = ({ term, explanation, children }) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className="cursor-help border-b border-dotted border-gray-400 inline-flex items-center">
            {children || term}
            <Info className="h-3 w-3 ml-1 text-gray-400" />
          </span>
        </TooltipTrigger>
        <TooltipContent className="max-w-xs">
          <p className="text-xs">{explanation}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Unified Technical Section Component with Collapsible Sections
const UnifiedTechnicalSectionCollapsible: React.FC<
  UnifiedTechnicalSectionProps
> = ({
  type,
  icon,
  title,
  score,
  impactStatement,
  findings,
  technicalDetails,
  actionReference,
  hideTitle = false,
}) => {
  // Get score style based on score value
  const scoreStyle = getScoreStyle(score);

  return (
    <div className="mb-6 overflow-hidden rounded-lg border border-gray-200 shadow-sm bg-[#F0F7F4]">
      {/* Section Header - Only show if hideTitle is false */}
      {!hideTitle && (
        <div
          className={`p-3 bg-gradient-to-r ${getGradient(
            type
          )} flex items-center justify-between`}
        >
          <div className="flex items-center">
            <div className="text-white mr-2">{icon}</div>
            <h3 className="text-white font-medium">{title}</h3>
          </div>
          <div
            className={`px-2 py-1 rounded-full ${scoreStyle.bg} flex items-center`}
          >
            <span className={`text-sm font-bold ${scoreStyle.color}`}>
              {score}
            </span>
            <span className={`ml-1 text-xs ${scoreStyle.color}`}>/ 100</span>
          </div>
        </div>
      )}

      {/* Use Accordion for all sections - Description and Points Clés sections are open by default */}
      <Accordion
        type="multiple"
        defaultValue={["description", "pointsCles"]}
        className="w-full"
      >
        {/* Description Section - Always open by default */}
        <AccordionItem value="description" className="border-b border-gray-200">
          <AccordionTrigger className="py-3 px-4 hover:no-underline">
            <div className="flex items-center">
              <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full mr-2"></div>
              <h4 className="text-sm font-bold text-gray-800">DESCRIPTION</h4>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 pt-0">
            <div className="p-3 rounded-lg bg-gray-50 border border-gray-200 shadow-sm">
              <p className="text-xs text-gray-700 leading-relaxed">
                {impactStatement.text}
              </p>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* Points Clés Section - Combined with Analysis */}
        {(impactStatement.positivePoints?.length > 0 ||
          impactStatement.negativePoints?.length > 0 ||
          findings.length > 0) && (
          <AccordionItem
            value="pointsCles"
            className="border-b border-gray-200"
          >
            <AccordionTrigger className="py-3 px-4 hover:no-underline">
              <div className="flex items-center">
                <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full mr-2"></div>
                <h4 className="text-sm font-bold text-gray-800">POINTS CLÉS</h4>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-4 pt-0">
              <div className="space-y-4">
                {/* Unified Analysis Section - Using findings for all content with deduplication */}
                {(() => {
                  // Extract all findings
                  const successFindings = findings.filter(
                    (f) => f.status === "success"
                  );
                  const warningFindings = findings.filter(
                    (f) => f.status === "warning"
                  );
                  const errorFindings = findings.filter(
                    (f) => f.status === "error"
                  );

                  // Enhanced deduplication logic with more aggressive matching for SEO content
                  const isDuplicate = (
                    point: string,
                    findingsList: Finding[]
                  ): boolean => {
                    // Normalize the point text for comparison
                    const normalizedPoint = point.toLowerCase().trim();

                    // Extract key terms from the point
                    const extractKeyTerms = (text: string): string[] => {
                      // Remove common words and keep only significant terms
                      const words = text
                        .toLowerCase()
                        .replace(/[^\w\s]/g, " ")
                        .split(/\s+/)
                        .filter(
                          (word) =>
                            word.length > 3 &&
                            ![
                              "dans",
                              "avec",
                              "pour",
                              "votre",
                              "cette",
                              "sont",
                              "vous",
                              "les",
                              "des",
                              "est",
                              "and",
                              "the",
                              "your",
                            ].includes(word)
                        );
                      return words;
                    };

                    const pointTerms = extractKeyTerms(normalizedPoint);

                    // Extract header name if it follows the pattern "Header: Description"
                    const headerRegexMatch = point.match(/^([^:]+):/);
                    if (headerRegexMatch) {
                      const headerName = headerRegexMatch[1]
                        .trim()
                        .toLowerCase();

                      // Check if any finding has this header as its label
                      const hasHeaderMatch = findingsList.some(
                        (f) =>
                          f.label.toLowerCase() === headerName ||
                          f.label.toLowerCase().includes(headerName) ||
                          headerName.includes(f.label.toLowerCase())
                      );

                      if (hasHeaderMatch) return true;
                    }

                    // Check for semantic similarity using key terms
                    for (const finding of findingsList) {
                      const findingLabelTerms = extractKeyTerms(finding.label);
                      const findingImpactTerms = extractKeyTerms(
                        finding.impact
                      );

                      // Check for term overlap
                      const labelOverlap = findingLabelTerms.some((term) =>
                        pointTerms.some(
                          (pointTerm) =>
                            pointTerm.includes(term) || term.includes(pointTerm)
                        )
                      );

                      const impactOverlap = findingImpactTerms.some((term) =>
                        pointTerms.some(
                          (pointTerm) =>
                            pointTerm.includes(term) || term.includes(pointTerm)
                        )
                      );

                      // Direct substring checks
                      const directLabelMatch =
                        normalizedPoint.includes(finding.label.toLowerCase()) ||
                        finding.label.toLowerCase().includes(normalizedPoint);

                      const directImpactMatch =
                        normalizedPoint.includes(
                          finding.impact.toLowerCase()
                        ) ||
                        finding.impact.toLowerCase().includes(normalizedPoint);

                      // If we have significant overlap or direct matches, consider it a duplicate
                      if (
                        labelOverlap ||
                        impactOverlap ||
                        directLabelMatch ||
                        directImpactMatch
                      ) {
                        return true;
                      }
                    }

                    return false;
                  };

                  // Filter out duplicate positive points
                  let uniquePositivePoints =
                    impactStatement.positivePoints?.filter(
                      (point) => !isDuplicate(point, successFindings)
                    ) || [];

                  // Filter out duplicate negative points
                  let uniqueNegativePoints =
                    impactStatement.negativePoints?.filter(
                      (point) =>
                        !isDuplicate(point, [
                          ...warningFindings,
                          ...errorFindings,
                        ])
                    ) || [];

                  // Additional deduplication step: remove duplicates within the same category
                  // This helps with SEO sections that might have similar points
                  const dedupWithinCategory = (points: string[]): string[] => {
                    const result: string[] = [];
                    const seen = new Set<string>();

                    for (const point of points) {
                      // Normalize the point for comparison
                      const normalizedPoint = point.toLowerCase().trim();

                      // Check if we've seen a similar point already
                      let isDuplicate = false;
                      for (const seenPoint of seen) {
                        // If there's significant overlap, consider it a duplicate
                        if (
                          normalizedPoint.includes(seenPoint) ||
                          seenPoint.includes(normalizedPoint) ||
                          // Check for semantic similarity
                          (normalizedPoint.length > 10 &&
                            seenPoint.length > 10 &&
                            (normalizedPoint.substring(0, 10) ===
                              seenPoint.substring(0, 10) ||
                              normalizedPoint.substring(
                                normalizedPoint.length - 10
                              ) === seenPoint.substring(seenPoint.length - 10)))
                        ) {
                          isDuplicate = true;
                          break;
                        }
                      }

                      if (!isDuplicate) {
                        result.push(point);
                        seen.add(normalizedPoint);
                      }
                    }

                    return result;
                  };

                  // Apply additional deduplication
                  uniquePositivePoints =
                    dedupWithinCategory(uniquePositivePoints);
                  uniqueNegativePoints =
                    dedupWithinCategory(uniqueNegativePoints);

                  // Count total unique items
                  const totalPositiveCount =
                    uniquePositivePoints.length + successFindings.length;
                  const totalNegativeCount =
                    uniqueNegativePoints.length +
                    warningFindings.length +
                    errorFindings.length;

                  return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Success findings and unique positive points */}
                      {totalPositiveCount > 0 && (
                        <div className="rounded-lg border border-green-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                          <div className="bg-gradient-to-r from-green-50 to-green-100 p-2 border-b border-green-200 flex items-center">
                            <div className="p-1 rounded-full bg-white shadow-sm mr-2">
                              <Check className="h-4 w-4 text-green-500" />
                            </div>
                            <h5 className="text-xs font-medium text-green-800">
                              Points forts{" "}
                              {totalPositiveCount > 0 &&
                                `(${totalPositiveCount})`}
                            </h5>
                          </div>
                          <div className="divide-y divide-green-100 max-h-64 overflow-y-auto">
                            {/* Include unique positive points from impact statement */}
                            {uniquePositivePoints.map((point, i) => (
                              <div
                                key={`pos-impact-${i}`}
                                className="p-2.5 bg-[#F0F8FF] hover:bg-green-50 transition-colors duration-150"
                              >
                                <div className="flex items-start">
                                  <div className="flex-shrink-0 mt-0.5 mr-2">
                                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                  </div>
                                  <div>
                                    <p className="text-xs text-gray-600 bg-green-50 p-1.5 rounded-md">
                                      {point}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}

                            {/* Include success findings */}
                            {successFindings.map((finding, i) => (
                              <div
                                key={`success-${i}`}
                                className="p-2.5 bg-[#F0F8FF] hover:bg-green-50 transition-colors duration-150"
                              >
                                <div className="flex items-start">
                                  <div className="flex-shrink-0 mt-0.5 mr-2">
                                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                  </div>
                                  <div>
                                    <h6 className="text-xs font-medium text-gray-800 mb-1">
                                      {finding.label}
                                    </h6>
                                    <p className="text-xs text-gray-600 bg-green-50 p-1.5 rounded-md">
                                      {finding.impact}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Warning and Error findings combined with unique negative points */}
                      {totalNegativeCount > 0 && (
                        <div className="rounded-lg border border-red-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                          <div className="bg-gradient-to-r from-red-50 to-red-100 p-2 border-b border-red-200 flex items-center">
                            <div className="p-1 rounded-full bg-white shadow-sm mr-2">
                              <AlertTriangle className="h-4 w-4 text-red-500" />
                            </div>
                            <h5 className="text-xs font-medium text-red-800">
                              Points à améliorer{" "}
                              {totalNegativeCount > 0 &&
                                `(${totalNegativeCount})`}
                            </h5>
                          </div>
                          <div className="divide-y divide-red-100 max-h-64 overflow-y-auto">
                            {/* Include unique negative points from impact statement */}
                            {uniqueNegativePoints.map((point, i) => (
                              <div
                                key={`neg-impact-${i}`}
                                className="p-2.5 bg-[#FDF6E3] hover:bg-red-50 transition-colors duration-150"
                              >
                                <div className="flex items-start">
                                  <div className="flex-shrink-0 mt-0.5 mr-2">
                                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                  </div>
                                  <div>
                                    <p className="text-xs text-gray-600 bg-red-50 p-1.5 rounded-md">
                                      {point}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}

                            {/* Include warning findings */}
                            {warningFindings.map((finding, i) => (
                              <div
                                key={`warning-${i}`}
                                className="p-2.5 bg-[#FFF9F0] hover:bg-yellow-50 transition-colors duration-150"
                              >
                                <div className="flex items-start">
                                  <div className="flex-shrink-0 mt-0.5 mr-2">
                                    <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                  </div>
                                  <div>
                                    <h6 className="text-xs font-medium text-gray-800 mb-1">
                                      {finding.label}
                                    </h6>
                                    <p className="text-xs text-gray-600 bg-yellow-50 p-1.5 rounded-md">
                                      {finding.impact}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}

                            {/* Include error findings */}
                            {errorFindings.map((finding, i) => (
                              <div
                                key={`error-${i}`}
                                className="p-2.5 bg-[#FDF6E3] hover:bg-red-50 transition-colors duration-150"
                              >
                                <div className="flex items-start">
                                  <div className="flex-shrink-0 mt-0.5 mr-2">
                                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                  </div>
                                  <div>
                                    <h6 className="text-xs font-medium text-gray-800 mb-1">
                                      {finding.label}
                                    </h6>
                                    <p className="text-xs text-gray-600 bg-red-50 p-1.5 rounded-md">
                                      {finding.impact}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Technical Details Section - Only show if there are details */}
        {technicalDetails.length > 0 && (
          <AccordionItem
            value="detailsTechniques"
            className="border-b border-gray-200"
          >
            <AccordionTrigger className="py-3 px-4 hover:no-underline">
              <div className="flex items-center">
                <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full mr-2"></div>
                <h4 className="text-sm font-bold text-gray-800">
                  DÉTAILS TECHNIQUES
                </h4>
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-4 pb-4 pt-0">
              {/* Horizontal grid layout for technical details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {technicalDetails.map((detail, i) => (
                  <div
                    key={`detail-${i}`}
                    className="rounded-lg border border-gray-200 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
                  >
                    {/* Header with parameter name and value */}
                    <div className="bg-gray-50 p-2 border-b border-gray-200 flex justify-between items-center">
                      <h5 className="text-sm font-medium text-gray-800">
                        {detail.tooltip ? (
                          <TechnicalTerm
                            term={detail.parameter}
                            explanation={detail.tooltip}
                          >
                            {detail.parameter}
                          </TechnicalTerm>
                        ) : (
                          detail.parameter
                        )}
                      </h5>
                      <div className="px-3 py-1 rounded-full border shadow-sm bg-gray-100 text-gray-800 border-gray-300 text-xs font-medium flex items-center">
                        <Info className="h-3 w-3 text-gray-500 mr-1.5" />
                        {detail.value}
                      </div>
                    </div>

                    {/* Enhanced content with impact and recommendation */}
                    <div className="p-3 bg-[#F8F9FA]">
                      {/* Impact */}
                      <div className="rounded-lg border border-gray-200 overflow-hidden mb-3">
                        <div className="bg-gray-50 p-2 border-b border-gray-200 flex items-center">
                          <div className="p-1 rounded-full bg-white shadow-sm mr-1.5">
                            <Info className="h-3.5 w-3.5 text-gray-500" />
                          </div>
                          <h6 className="text-xs font-medium text-gray-800">
                            Impact
                          </h6>
                        </div>
                        <div className="p-2.5 bg-white">
                          <p className="text-xs text-gray-700 leading-relaxed">
                            {detail.impact}
                          </p>
                        </div>
                      </div>

                      {/* Recommendation if available */}
                      {detail.recommended && (
                        <div className="rounded-lg border border-gray-200 overflow-hidden">
                          <div className="bg-gray-50 p-2 border-b border-gray-200 flex items-center">
                            <div className="p-1 rounded-full bg-white shadow-sm mr-1.5">
                              <Check className="h-3.5 w-3.5 text-gray-500" />
                            </div>
                            <h6 className="text-xs font-medium text-gray-800">
                              Recommandation
                            </h6>
                          </div>
                          <div className="p-2.5 bg-white">
                            <p className="text-xs text-gray-700 leading-relaxed bg-gray-50 p-1.5 rounded-md">
                              {detail.recommended}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        )}

        {/* Score Explanation Section */}
        <AccordionItem
          value="explicationScore"
          className="border-b border-gray-200"
        >
          <AccordionTrigger className="py-3 px-4 hover:no-underline">
            <div className="flex items-center">
              <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full mr-2"></div>
              <h4 className="text-sm font-bold text-gray-800">
                EXPLICATION DU SCORE
              </h4>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 pt-0">
            <div className="rounded-lg border border-gray-200 overflow-hidden shadow-sm">
              <div className="bg-gray-50 p-2 border-b border-gray-200">
                <h5 className="text-sm font-medium text-gray-800">
                  Comment ce score est-il calculé?
                </h5>
              </div>
              <div className="p-3">
                <div className="mb-3">
                  <p className="text-xs text-gray-700">
                    Ce score est calculé en fonction de plusieurs facteurs
                    techniques spécifiques à cette section:
                  </p>
                </div>
                <div className="grid grid-cols-1 gap-2 mb-3">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-xs text-gray-700">
                      <strong>Score 80-100:</strong> Excellente implémentation,
                      conforme aux meilleures pratiques
                    </span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                    <span className="text-xs text-gray-700">
                      <strong>Score 60-79:</strong> Implémentation acceptable
                      avec des améliorations possibles
                    </span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <span className="text-xs text-gray-700">
                      <strong>Score &lt; 60:</strong> Implémentation
                      insuffisante nécessitant des corrections
                    </span>
                  </div>
                </div>
                <div className="rounded-lg bg-gray-50 p-2 border border-gray-200">
                  <p className="text-xs text-gray-700">
                    <strong>Votre score actuel ({score}/100):</strong>{" "}
                    {score >= 80
                      ? "Excellent! Cette section est bien implémentée et conforme aux meilleures pratiques."
                      : score >= 60
                      ? "Acceptable, mais des améliorations sont recommandées pour optimiser cette section."
                      : "Nécessite votre attention. Cette section présente des problèmes importants qui doivent être corrigés."}
                  </p>
                </div>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Simple Section Divider with Action Info */}
      <div className="p-4 bg-[#F5F5F5] border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div
              className={`w-2 h-2 rounded-full ${
                actionReference.count > 0 ? "bg-red-500" : "bg-green-500"
              } mr-2`}
            ></div>
            <span className="text-sm text-gray-600">
              {actionReference.count > 0
                ? `${actionReference.count} problèmes à résoudre`
                : "Aucun problème détecté"}
            </span>
          </div>
          {actionReference.count > 0 && (
            <div
              onClick={() => {
                // Scroll to the main recommended actions section
                const element = document.getElementById("recommended-actions");
                if (element) {
                  element.scrollIntoView({ behavior: "smooth" });
                }
              }}
              className="text-sm text-blue-600 hover:text-blue-800 cursor-pointer flex items-center"
            >
              <span>{actionReference.actionWord} les actions</span>
              <ArrowRight className="h-3 w-3 ml-1" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UnifiedTechnicalSectionCollapsible;
