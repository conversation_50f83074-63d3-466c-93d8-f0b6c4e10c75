import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast, Toaster } from "sonner";
import {
  Users,
  BarChart3,
  Search,
  Filter,
  Edit,
  UserCheck,
  UserX,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import Layout from "../components/Layout";
import Card from "../components/ui-custom/Card";
import Button from "../components/ui-custom/Button";
import Input from "../components/ui-custom/Input";
import { apiService } from "@/services/apiService";

// Types
interface User {
  id: string;
  email: string;
  username: string;
  role: number;
  isVerified: boolean;
  createdAt: string;
  isActive: boolean;
}

interface PlatformStats {
  totalUsers: number;
  freeUsers: number;
  premiumUsers: number;
  totalScans: number;
  securityScans: number;
  seoScans: number;
  registrationsLastMonth: number;
}

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState("users");
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<PlatformStats | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(10);
  const navigate = useNavigate();

  // Load users and stats
  useEffect(() => {
    const loadData = async () => {
      try {
        const user = await apiService.getCurrentUser();

        // Check if user is admin
        if (!user) {
          console.log("Access denied to admin dashboard. User not found");
          toast.error("Accès non autorisé");
          navigate("/dashboard");
          return;
        }

        // Convert role to number for comparison
        const userRoleNum =
          typeof user.role === "string" ? parseInt(user.role) : user.role;

        if (userRoleNum !== 3) {
          console.log(
            `Access denied to admin dashboard. User role: ${user.role}`
          );
          toast.error("Accès non autorisé");
          navigate("/dashboard");
          return;
        }

        console.log("Admin access granted. User role:", user.role);

        // Load real data from API
        await loadUsers();
        await loadStats();
      } catch (error) {
        console.error("Error loading admin data:", error);
        toast.error("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [navigate]);

  // Load users from API
  const loadUsers = async () => {
    try {
      const usersList = await apiService.getAllUsers();
      setUsers(usersList);
      setFilteredUsers(usersList);
    } catch (error) {
      console.error("Error loading users:", error);
      toast.error("Erreur lors du chargement des utilisateurs");
      setUsers([]);
      setFilteredUsers([]);
    }
  };

  // Load platform stats from API
  const loadStats = async () => {
    try {
      const platformStats = await apiService.getPlatformStats();
      setStats({
        totalUsers: platformStats.totalUsers,
        freeUsers: platformStats.freeUsers,
        premiumUsers: platformStats.premiumUsers,
        totalScans: platformStats.totalScans,
        securityScans: platformStats.securityScans,
        seoScans: platformStats.seoScans,
        registrationsLastMonth: platformStats.registrationsLastMonth,
      });
    } catch (error) {
      console.error("Error loading stats:", error);
      toast.error("Erreur lors du chargement des statistiques");
      setStats(null);
    }
  };

  // Filter users based on search term and filters
  useEffect(() => {
    // Start with all users except admins by default
    let result = users.filter((user) => user.role !== 3);

    // If admin role is specifically selected, show only admins
    if (roleFilter === "3") {
      result = users.filter((user) => user.role === 3);
    }
    // Otherwise apply normal role filter if not "all"
    else if (roleFilter !== "all") {
      const roleId = parseInt(roleFilter);
      result = result.filter((user) => user.role === roleId);
    }

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        (user) =>
          user.email.toLowerCase().includes(term) ||
          user.username.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (statusFilter !== "all") {
      const isActive = statusFilter === "active";
      result = result.filter((user) => user.isActive === isActive);
    }

    setFilteredUsers(result);
    setCurrentPage(1); // Reset to first page when filters change
  }, [searchTerm, roleFilter, statusFilter, users]);

  // Pagination logic
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

  // User actions with real API calls
  const handleEditUser = (userId: string) => {
    toast.info(
      `Modification de l'utilisateur ${userId} - Fonctionnalité à implémenter`
    );
  };

  const handleToggleUserStatus = async (
    userId: string,
    currentStatus: boolean
  ) => {
    try {
      setLoading(true);
      // Call the API to update user status
      await apiService.updateUserStatus(userId, !currentStatus);

      // Update local state
      const updatedUsers = users.map((user) =>
        user.id === userId ? { ...user, isActive: !currentStatus } : user
      );

      setUsers(updatedUsers);
      toast.success(`Statut de l'utilisateur modifié avec succès`);
    } catch (error: any) {
      toast.error(error.message || "Erreur lors de la modification du statut");
      console.error("Error updating user status:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleChangeUserRole = async (userId: string, newRole: number) => {
    try {
      setLoading(true);
      // Call the API to update user role
      await apiService.updateUserRole(userId, newRole === 1 ? 2 : 1);

      // Update local state
      const updatedUsers = users.map((user) =>
        user.id === userId ? { ...user, role: newRole === 1 ? 2 : 1 } : user
      );

      setUsers(updatedUsers);
      toast.success(`Rôle de l'utilisateur modifié avec succès`);
    } catch (error: any) {
      toast.error(error.message || "Erreur lors de la modification du rôle");
      console.error("Error updating user role:", error);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setLoading(true);
    try {
      await loadUsers();
      await loadStats();
      toast.success("Données actualisées");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Erreur lors de l'actualisation des données");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-12 flex-grow">
          <div className="flex flex-col justify-center items-center min-h-[50vh] space-y-4">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
            <p className="text-gray-600">
              Chargement du tableau de bord administrateur...
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNav={true} navType="second">
      <div className="container mx-auto px-4 pt-24 pb-12">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">
          Tableau de Bord Administrateur
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Sidebar navigation */}
          <div className="md:col-span-1">
            <Card className="p-4 bg-white/95 rounded-xl shadow-lg">
              <nav className="space-y-2">
                <button
                  onClick={() => setActiveTab("users")}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                    activeTab === "users"
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                      : "hover:bg-gray-100"
                  }`}
                >
                  <Users size={18} />
                  <span>Gestion des utilisateurs</span>
                </button>

                <button
                  onClick={() => setActiveTab("stats")}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center space-x-3 ${
                    activeTab === "stats"
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                      : "hover:bg-gray-100"
                  }`}
                >
                  <BarChart3 size={18} />
                  <span>Statistiques de la plateforme</span>
                </button>
              </nav>
            </Card>
          </div>

          {/* Main content area */}
          <div className="md:col-span-3">
            <Card className="p-6 bg-white/95 rounded-xl shadow-lg">
              {/* Users Management Tab */}
              {activeTab === "users" && (
                <div>
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">
                      Gestion des utilisateurs
                    </h2>
                    <Button onClick={refreshData} variant="outline" size="sm">
                      <RefreshCw size={16} className="mr-2" />
                      Actualiser
                    </Button>
                  </div>

                  {/* Search and filters */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="relative">
                      <Input
                        type="text"
                        placeholder="Rechercher par email ou nom..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        icon={<Search size={18} />}
                      />
                    </div>

                    <div className="relative">
                      <div className="flex items-center">
                        <Filter size={18} className="mr-2 text-gray-500" />
                        <select
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={roleFilter}
                          onChange={(e) => setRoleFilter(e.target.value)}
                        >
                          <option value="all">Tous les rôles</option>
                          <option value="1">Utilisateurs gratuits</option>
                          <option value="2">Utilisateurs premium</option>
                          <option value="3">Administrateurs</option>
                        </select>
                      </div>
                    </div>

                    <div className="relative">
                      <div className="flex items-center">
                        <Filter size={18} className="mr-2 text-gray-500" />
                        <select
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          value={statusFilter}
                          onChange={(e) => setStatusFilter(e.target.value)}
                        >
                          <option value="all">Tous les statuts</option>
                          <option value="active">Actifs</option>
                          <option value="inactive">Inactifs</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Users table */}
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-white border border-gray-200 rounded-lg">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Utilisateur
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Rôle
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statut
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date d'inscription
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {currentUsers.map((user) => (
                          <tr key={user.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {user.username}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {user.email}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span
                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  user.role === 2
                                    ? "bg-purple-100 text-purple-800"
                                    : user.role === 3
                                    ? "bg-blue-100 text-blue-800"
                                    : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {user.role === 1
                                  ? "Gratuit"
                                  : user.role === 2
                                  ? "Premium"
                                  : "Admin"}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span
                                className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                  user.isActive
                                    ? "bg-green-100 text-green-800"
                                    : "bg-red-100 text-red-800"
                                }`}
                              >
                                {user.isActive ? "Actif" : "Inactif"}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(user.createdAt).toLocaleDateString(
                                "fr-FR"
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end space-x-2">
                                {/* Only show actions for non-admin users */}
                                {user.role !== 3 ? (
                                  <>
                                    <button
                                      onClick={() => handleEditUser(user.id)}
                                      className="text-blue-600 hover:text-blue-900"
                                      title="Modifier l'utilisateur"
                                    >
                                      <Edit size={16} />
                                    </button>
                                    <button
                                      onClick={() =>
                                        handleToggleUserStatus(
                                          user.id,
                                          user.isActive
                                        )
                                      }
                                      className={`${
                                        user.isActive
                                          ? "text-red-600 hover:text-red-900"
                                          : "text-green-600 hover:text-green-900"
                                      }`}
                                      title={
                                        user.isActive
                                          ? "Désactiver l'utilisateur"
                                          : "Activer l'utilisateur"
                                      }
                                    >
                                      {user.isActive ? (
                                        <UserX size={16} />
                                      ) : (
                                        <UserCheck size={16} />
                                      )}
                                    </button>
                                    <button
                                      onClick={() =>
                                        handleChangeUserRole(
                                          user.id,
                                          user.role === 1 ? 2 : 1
                                        )
                                      }
                                      className="text-purple-600 hover:text-purple-900"
                                      title={
                                        user.role === 1
                                          ? "Passer en Premium"
                                          : "Passer en Gratuit"
                                      }
                                    >
                                      {user.role === 1 ? "⭐" : "⬇️"}
                                    </button>
                                  </>
                                ) : (
                                  <span className="text-gray-400 italic text-xs">
                                    Compte administrateur
                                  </span>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-between items-center mt-4">
                      <div className="text-sm text-gray-500">
                        Affichage de {indexOfFirstUser + 1} à{" "}
                        {Math.min(indexOfLastUser, filteredUsers.length)} sur{" "}
                        {filteredUsers.length} utilisateurs
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setCurrentPage((prev) => Math.max(prev - 1, 1))
                          }
                          disabled={currentPage === 1}
                        >
                          <ChevronLeft size={16} />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            setCurrentPage((prev) =>
                              Math.min(prev + 1, totalPages)
                            )
                          }
                          disabled={currentPage === totalPages}
                        >
                          <ChevronRight size={16} />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Statistics Tab */}
              {activeTab === "stats" && stats && (
                <div>
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-xl font-semibold">
                      Statistiques de la plateforme
                    </h2>
                    <Button onClick={refreshData} variant="outline" size="sm">
                      <RefreshCw size={16} className="mr-2" />
                      Actualiser
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Users stats */}
                    <Card className="p-4 border border-gray-200">
                      <h3 className="text-lg font-medium mb-4">Utilisateurs</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">Total</div>
                          <div className="text-2xl font-bold">
                            {stats.totalUsers}
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">
                            Nouveaux (30j)
                          </div>
                          <div className="text-2xl font-bold">
                            {stats.registrationsLastMonth}
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">Gratuits</div>
                          <div className="text-2xl font-bold">
                            {stats.freeUsers}
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">Premium</div>
                          <div className="text-2xl font-bold">
                            {stats.premiumUsers}
                          </div>
                        </div>
                      </div>
                    </Card>

                    {/* Scans stats */}
                    <Card className="p-4 border border-gray-200">
                      <h3 className="text-lg font-medium mb-4">Analyses</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">Total</div>
                          <div className="text-2xl font-bold">
                            {stats.totalScans}
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">
                            Moyenne par utilisateur
                          </div>
                          <div className="text-2xl font-bold">
                            {(stats.totalScans / stats.totalUsers).toFixed(1)}
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">Sécurité</div>
                          <div className="text-2xl font-bold">
                            {stats.securityScans}
                          </div>
                        </div>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <div className="text-sm text-gray-500">SEO</div>
                          <div className="text-2xl font-bold">
                            {stats.seoScans}
                          </div>
                        </div>
                      </div>
                    </Card>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>

      <Toaster position="top-right" richColors />
    </Layout>
  );
}
