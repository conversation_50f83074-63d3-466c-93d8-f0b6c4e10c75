/* Print styles for report */
@media print {
  /* General page setup */
  @page {
    margin: 1.5cm;
    size: A4;
  }
  
  html, body {
    width: 210mm;
    height: 297mm;
    font-size: 11pt;
    background: white !important;
    color: black !important;
    margin: 0;
    padding: 0;
  }
  
  /* Hide navigation elements */
  nav, 
  header button,
  .fixed,
  .no-print,
  button[type="button"]:not(.print-include) {
    display: none !important;
  }
  
  /* Ensure all content is visible */
  .container {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Ensure all collapsed content is expanded */
  [aria-expanded="false"] + [aria-hidden="true"],
  [data-state="closed"] + [data-state="closed"] {
    display: block !important;
    height: auto !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* Optimize page breaks */
  .neo-card,
  .technical-section,
  #security-details,
  #seo-details,
  #recommended-actions,
  #download-report {
    page-break-inside: avoid;
    break-inside: avoid;
    margin-bottom: 1cm;
  }
  
  /* Ensure background colors and gradients print */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
  
  /* Improve contrast for better printing */
  .text-gray-600 {
    color: #333 !important;
  }
  
  .text-gray-500 {
    color: #444 !important;
  }
  
  /* Adjust score cards for print */
  .score-card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
    break-inside: avoid;
  }
  
  /* Ensure grid layouts work in print */
  .grid {
    display: block !important;
  }
  
  .grid > * {
    margin-bottom: 1cm;
  }
  
  /* Ensure action items are readable */
  #recommended-actions .grid {
    display: block !important;
  }
  
  #recommended-actions .grid > * {
    margin-bottom: 0.5cm;
  }
  
  /* Ensure code blocks are readable */
  pre, code {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    background-color: #f1f1f1 !important;
    color: #333 !important;
    border: 1px solid #ccc !important;
  }
  
  /* Add page numbers */
  .container::after {
    content: counter(page);
    counter-increment: page;
    position: absolute;
    bottom: 1cm;
    right: 1cm;
    font-size: 10pt;
  }
  
  /* Add header with report title */
  .container::before {
    content: "Rapport d'analyse SiteChecker";
    position: absolute;
    top: 1cm;
    left: 1cm;
    font-size: 10pt;
    color: #666;
  }
  
  /* Ensure all text is visible */
  .line-clamp-2 {
    -webkit-line-clamp: unset !important;
    overflow: visible !important;
    display: block !important;
  }
  
  /* Ensure all implementation steps are visible */
  .implementation-steps {
    display: block !important;
  }
  
  /* Adjust technical section tabs for print */
  .technical-section-tabs {
    display: block !important;
  }
  
  .technical-section-tabs > * {
    display: block !important;
    margin-bottom: 0.5cm;
  }
  
  /* Add section breaks */
  h2, h3 {
    page-break-before: always;
    break-before: always;
  }
  
  h2:first-of-type, h3:first-of-type {
    page-break-before: avoid;
    break-before: avoid;
  }
  
  /* Ensure all images print */
  img, svg {
    max-width: 100% !important;
    page-break-inside: avoid;
    break-inside: avoid;
  }
  
  /* Ensure tables print properly */
  table {
    border-collapse: collapse !important;
    width: 100% !important;
  }
  
  table, th, td {
    border: 1px solid #ccc !important;
  }
  
  th, td {
    padding: 0.2cm !important;
    text-align: left !important;
  }
  
  /* Ensure tooltips are hidden */
  [role="tooltip"] {
    display: none !important;
  }
}
