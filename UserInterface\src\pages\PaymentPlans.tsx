import { useState } from "react";
import { Star, Zap, Check, X, Info } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import { Switch } from "@headlessui/react";
import Logo from "../components/ui-custom/Logo";

const plans = [
  {
    id: "free",
    name: "Gratuit",
    monthlyPrice: 0,
    annualPrice: 0,
    features: [
      {
        text: "Rapports basiques",
        included: true,
        tooltip:
          "Accédez aux sections SSL/TLS, HTTP Headers et Vulnérabilités pour la sécurité, et Meta Tags, Mobile et Performance pour le SEO",
      },
      {
        text: "Visualisation en ligne",
        included: true,
        tooltip: "Consultez vos rapports directement dans l'application",
      },
      {
        text: "Support par email",
        included: true,
        tooltip: "Obtenez de l'aide via notre support email standard",
      },
      {
        text: "Analyses avancées",
        included: false,
        tooltip:
          "Accédez à des analyses de sécurité API, JavaScript et SSL avancées, ainsi qu'à des analyses SEO complètes",
      },
      {
        text: "Téléchargement des rapports",
        included: false,
        tooltip:
          "Téléchargez vos rapports aux formats PDF et HTML pour les partager ou les archiver",
      },
      {
        text: "Analyses illimitées",
        included: false,
        tooltip: "Passez à un plan supérieur pour des analyses sans limite",
      },
      {
        text: "Support prioritaire",
        included: false,
        tooltip: "Bénéficiez d'un support en temps réel avec le plan premium",
      },
    ],
    icon: <Star className="w-5 h-5 text-white" />,
    gradient: "from-blue-500/80 to-purple-500/80",
  },
  {
    id: "pro",
    name: "Premium",
    monthlyPrice: 5,
    annualPrice: 48, // 4Dt/month, 20% discount
    features: [
      {
        text: "Rapports complets",
        included: true,
        tooltip:
          "Accédez à toutes les sections d'analyse de sécurité et SEO, y compris les analyses avancées",
      },
      {
        text: "Visualisation en ligne",
        included: true,
        tooltip:
          "Consultez vos rapports détaillés directement dans l'application",
      },
      {
        text: "Téléchargement des rapports",
        included: true,
        tooltip:
          "Téléchargez vos rapports aux formats PDF et HTML pour les partager ou les archiver",
      },
      {
        text: "Analyses avancées",
        included: true,
        tooltip:
          "Accédez à des analyses de sécurité API, JavaScript et SSL avancées, ainsi qu'à des analyses SEO complètes",
      },
      {
        text: "Analyses illimitées",
        included: true,
        tooltip: "Effectuez autant d'analyses que vous le souhaitez",
      },
      {
        text: "Support prioritaire",
        included: true,
        tooltip: "Bénéficiez d'un support en temps réel et prioritaire",
      },
      {
        text: "Historique complet",
        included: true,
        tooltip:
          "Conservez et consultez l'historique complet de toutes vos analyses",
      },
    ],
    icon: <Zap className="w-5 h-5 text-white" />,
    gradient: "from-indigo-600/80 to-purple-600/80",
    popular: true,
  },
];

const Tooltip = ({
  children,
  content,
}: {
  children: React.ReactNode;
  content: string;
}) => {
  const [visible, setVisible] = useState(false);

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={() => setVisible(true)}
        onMouseLeave={() => setVisible(false)}
        className="inline-flex items-center"
      >
        {children}
        <Info className="w-3 h-3 ml-1 text-white/50 hover:text-white/80" />
      </div>
      {visible && (
        <div className="absolute z-10 w-48 p-2 mt-2 text-sm text-white bg-gray-800 rounded-md shadow-lg">
          {content}
        </div>
      )}
    </div>
  );
};

const FeatureItem = ({
  included,
  text,
  tooltip,
}: {
  included: boolean;
  text: string;
  tooltip?: string;
}) => (
  <li className="flex items-center gap-3">
    <div
      className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center ${
        included ? "bg-green-500/20" : "bg-gray-500/20"
      }`}
    >
      {included ? (
        <Check className="w-3 h-3 text-green-400" />
      ) : (
        <X className="w-3 h-3 text-gray-400" />
      )}
    </div>
    {tooltip ? (
      <Tooltip content={tooltip}>
        <span
          className={`text-sm ${included ? "text-white" : "text-gray-400"}`}
        >
          {text}
        </span>
      </Tooltip>
    ) : (
      <span className={`text-sm ${included ? "text-white" : "text-gray-400"}`}>
        {text}
      </span>
    )}
  </li>
);

export default function PaymentPlans() {
  const [selectedPlan, setSelectedPlan] = useState("free");
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">(
    "monthly"
  );

  // Calculate dynamic pricing and savings
  const plansWithPricing = plans.map((plan) => {
    let price, perMonth, savings;
    if (billingCycle === "monthly") {
      price = plan.monthlyPrice;
      perMonth = plan.monthlyPrice;
      savings = 0;
    } else {
      price = plan.annualPrice;
      perMonth = plan.annualPrice / 12;
      savings = plan.monthlyPrice * 12 - plan.annualPrice;
    }
    return {
      ...plan,
      price,
      perMonth,
      savings,
    };
  });

  return (
    <div className="min-h-screen flex flex-col items-center bg-gradient-to-br from-blue-400 to-purple-500">
      {/* Navbar - Consistent with report page */}
      <div className="w-full py-4 px-6 flex justify-between items-center bg-transparent backdrop-blur-sm fixed top-0 z-50 hover:bg-white/10 transition duration-300">
        <Logo className="cursor-default" to="" />
        <div className="flex gap-8">
          <Link to="/services" className="text-white hover:text-indigo-100">
            Services
          </Link>
          <Link to="/about" className="text-white hover:text-indigo-100">
            À propos
          </Link>
          <Link to="/reviews" className="text-white hover:text-indigo-100">
            Avis
          </Link>
        </div>
      </div>

      <div className="flex-1 flex flex-col items-center justify-center w-full px-6 pb-12 mt-20">
        <h1 className="text-4xl font-bold text-white mb-8 text-center">
          Choisissez votre plan
        </h1>

        {/* Billing toggle - visually enhanced */}
        <div className="flex items-center justify-center gap-6 mb-12 bg-white/10 backdrop-blur-sm p-3 px-6 rounded-full shadow-lg">
          <span
            className={`text-sm font-medium transition-colors ${
              billingCycle === "monthly" ? "text-white" : "text-indigo-200"
            }`}
          >
            Mensuel
          </span>
          <Switch
            checked={billingCycle === "annual"}
            onChange={() =>
              setBillingCycle(billingCycle === "monthly" ? "annual" : "monthly")
            }
            className={`relative inline-flex h-7 w-14 items-center rounded-full transition-colors duration-300 focus:outline-none ${
              billingCycle === "annual" ? "bg-indigo-600" : "bg-gray-400"
            }`}
          >
            <span className="sr-only">Select billing cycle</span>
            <span
              className={`$${
                billingCycle === "annual" ? "translate-x-7" : "translate-x-1"
              } inline-block h-5 w-5 transform rounded-full bg-white shadow-md transition-transform duration-300`}
            />
          </Switch>
          <div className="flex items-center gap-2">
            <span
              className={`text-sm font-medium transition-colors ${
                billingCycle === "annual" ? "text-white" : "text-indigo-200"
              }`}
            >
              Annuel
            </span>
            <span className="text-xs font-medium text-white bg-indigo-600 px-2 py-1 rounded-full">
              {billingCycle === "annual" ? "20% de réduction" : ""}
            </span>
          </div>
        </div>

        {/* Plan cards - visually consistent, dynamic pricing, savings, badges */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-5xl mb-12">
          {plansWithPricing.map((plan) => (
            <div
              key={plan.id}
              onClick={() => setSelectedPlan(plan.id)}
              className={`cursor-pointer rounded-2xl overflow-hidden transition-all duration-300 hover:scale-105 backdrop-blur-sm shadow-xl ${
                selectedPlan === plan.id
                  ? `bg-gradient-to-r ${plan.gradient} ring-2 ring-white/70 shadow-indigo-300/40`
                  : "bg-white/5 hover:bg-white/10"
              }`}
            >
              <div className="p-6 border-b border-white/10 relative">
                {plan.popular && (
                  <div
                    className={`absolute top-0 right-0 bg-white text-indigo-700 text-xs font-bold px-3 py-1 ${
                      selectedPlan === plan.id ? "opacity-100" : "opacity-90"
                    }`}
                  >
                    POPULAIRE
                  </div>
                )}
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-white/20">
                    {plan.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-white">
                    {plan.name}
                  </h3>
                </div>
                <p className="text-4xl font-bold text-white">
                  {billingCycle === "monthly"
                    ? `${plan.monthlyPrice} Dt`
                    : `${plan.annualPrice} Dt`}
                  <span className="text-sm font-normal">
                    {billingCycle === "monthly" ? " /mois" : " /an"}
                  </span>
                </p>
                {billingCycle === "annual" && plan.id !== "free" && (
                  <p className="text-sm text-green-400 mt-1">
                    Économisez {plan.savings} Dt vs mensuel
                  </p>
                )}
                <p className="text-indigo-200 mt-1">
                  {plan.id === "free"
                    ? "Analyses de base pour débutants"
                    : "Analyses complètes et téléchargements"}
                </p>
              </div>
              <div className="p-6 bg-white/5 backdrop-blur-sm">
                <ul className="space-y-4">
                  {plan.features.map((feature, index) => (
                    <FeatureItem
                      key={index}
                      text={feature.text}
                      included={feature.included}
                      tooltip={feature.tooltip}
                    />
                  ))}
                </ul>
                <button
                  className={`w-full py-3 mt-6 rounded-xl font-medium transition-colors ${
                    selectedPlan === plan.id
                      ? "bg-white text-indigo-700 shadow-lg shadow-indigo-600/20"
                      : "bg-white/10 text-white hover:bg-white/20"
                  }`}
                >
                  {selectedPlan === plan.id
                    ? "Plan Sélectionné"
                    : "Choisir ce plan"}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Continue to registration button */}
        <Link
          to="/register"
          state={{ plan: selectedPlan, billingCycle }}
          className="px-8 py-3 bg-white text-indigo-700 rounded-full font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        >
          Continuer vers l'inscription
        </Link>
      </div>

      {/* Footer - Consistent with report page */}
      <footer className="w-full py-12 px-6 text-white bg-gray-800">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-sm font-semibold text-white mb-4">
                Plateforme
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    to="/features"
                    className="text-white/70 hover:text-white"
                  >
                    Fonctionnalités
                  </Link>
                </li>
                <li>
                  <Link
                    to="/pricing"
                    className="text-white/70 hover:text-white"
                  >
                    Tarifs
                  </Link>
                </li>
                <li>
                  <Link to="/login" className="text-white/70 hover:text-white">
                    Connexion
                  </Link>
                </li>
                <li>
                  <Link
                    to="/register"
                    className="text-white/70 hover:text-white"
                  >
                    Inscription
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-white mb-4">Légal</h3>
              <ul className="space-y-2">
                <li>
                  <Link to="/terms" className="text-white/70 hover:text-white">
                    Conditions d'utilisation
                  </Link>
                </li>
                <li>
                  <Link
                    to="/privacy"
                    className="text-white/70 hover:text-white"
                  >
                    Politique de confidentialité
                  </Link>
                </li>
                <li>
                  <Link
                    to="/cookies"
                    className="text-white/70 hover:text-white"
                  >
                    Cookies
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-white mb-4">
                Ressources
              </h3>
              <ul className="space-y-2">
                <li>
                  <Link to="/blog" className="text-white/70 hover:text-white">
                    Blog
                  </Link>
                </li>
                <li>
                  <Link to="/guides" className="text-white/70 hover:text-white">
                    Guides
                  </Link>
                </li>
                <li>
                  <Link
                    to="/support"
                    className="text-white/70 hover:text-white"
                  >
                    Support
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-white mb-4">Contact</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    to="/contact"
                    className="text-white/70 hover:text-white"
                  >
                    Nous contacter
                  </Link>
                </li>
                <li>
                  <Link to="/about" className="text-white/70 hover:text-white">
                    À propos
                  </Link>
                </li>
                <li>
                  <Link
                    to="/careers"
                    className="text-white/70 hover:text-white"
                  >
                    Carrières
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-12 pt-8 border-t border-white/10 text-center">
            <p className="text-sm text-white/60">
              © {new Date().getFullYear()} SiteChecker. Tous droits réservés.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
