import { CheckCircle2, Search, ShieldCheck } from "lucide-react";
import Layout from "../components/Layout";
import Button from "../components/ui-custom/Button";
import Card from "../components/ui-custom/Card";
import FeatureCard from "../components/features/FeatureCard";
import TestimonialCard from "../components/testimonials/TestimonialCard";
import { Link } from "react-router-dom";

export default function Index() {
  return (
    <Layout>
      <section className="pt-32 pb-16 md:pt-40 md:pb-24 px-4 bg-gradient-to-r from-blue-300 to-purple-400">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-12 max-w-3xl mx-auto">
            <h1 className="text-3xl md:text-5xl font-bold text-white mb-6 leading-tight animate-fade-in text-balance">
              Audit Automatique de Sécurité et SEO
            </h1>
            <p className="text-white/90 text-lg mb-8 animate-fade-in delay-100 text-balance">
              Sécurisez et optimisez votre site web en quelques clics grâce à
              notre plateforme d'audit automatique.
            </p>
            <div className="flex justify-center">
              <Link to="/login">
                <Button
                  withArrow
                  size="lg"
                  className="animate-fade-in delay-200 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 transform hover:scale-105 backdrop-blur-sm shadow-lg hover:shadow-xl"
                  aria-label="Commencer gratuitement"
                >
                  Commencer gratuitement
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-8">
            Tout ce dont vous avez besoin
          </h2>

          <div className="grid md:grid-cols-3 gap-6 mt-12">
            <FeatureCard
              icon={ShieldCheck}
              title="Audit de sécurité"
              description="Détection des vulnérabilités et recommandations pour sécuriser votre site."
              className="animate-fade-in"
            />
            <FeatureCard
              icon={Search}
              title="Référencement SEO"
              description="Analyse complète du SEO pour améliorer votre positionnement sur les moteurs de recherche."
              className="animate-fade-in delay-100"
            />
            <FeatureCard
              icon={CheckCircle2}
              title="Performance"
              description="Optimisation des temps de chargement et de l'expérience utilisateur."
              className="animate-fade-in delay-200"
            />
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-white/5">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-12">
            Comment ça marche ?
          </h2>

          <div className="grid md:grid-cols-4 gap-8">
            <StepCard
              number={1}
              title="Créez votre compte"
              description="Inscrivez-vous en quelques secondes"
              delay={0}
            />
            <StepCard
              number={2}
              title="Ajoutez votre site"
              description="Entrez l'URL de votre site web"
              delay={100}
            />
            <StepCard
              number={3}
              title="Rapport d'audit"
              description="Recevez une analyse détaillée"
              delay={200}
            />
            <StepCard
              number={4}
              title="Des résultats"
              description="Appliquez nos recommandations"
              delay={300}
            />
          </div>
        </div>
      </section>

      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-8">
            Nos dernières mises à jour
          </h2>

          <div className="grid md:grid-cols-2 gap-8 mt-12">
            <Card
              className="flex flex-col items-start animate-fade-in"
              hoverEffect
            >
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                Audit de sécurité avancé
              </h3>
              <p className="text-gray-600 mb-4">
                Notre nouvelle fonctionnalité d'audit de sécurité permet de
                détecter plus de 100 types de vulnérabilités et de failles de
                sécurité.
              </p>
              <ul className="space-y-2 mb-6">
                <CheckItem text="Détection des injections SQL" />
                <CheckItem text="Vérification des certificats SSL" />
                <CheckItem text="Test de protection DDoS" />
                <CheckItem text="Audit des en-têtes HTTP" />
              </ul>
              <Button variant="secondary" className="mt-auto">
                En savoir plus
              </Button>
            </Card>

            <Card
              className="flex flex-col items-start animate-fade-in delay-100"
              hoverEffect
            >
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                Optimisation SEO avancée
              </h3>
              <p className="text-gray-600 mb-4">
                Améliorez votre référencement naturel grâce à nos outils
                d'analyse et nos recommandations personnalisées.
              </p>
              <ul className="space-y-2 mb-6">
                <CheckItem text="Analyse des mots-clés" />
                <CheckItem text="Vérification des balises méta" />
                <CheckItem text="Audit de la structure du site" />
                <CheckItem text="Analyse des backlinks" />
              </ul>
              <Button variant="secondary" className="mt-auto">
                En savoir plus
              </Button>
            </Card>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-white/5">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-8">
            À Propos d'AuditMaster
          </h2>

          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 shadow-sm">
              <p className="text-white/90 mb-6">
                Chez AuditMaster, nous sommes passionnés par la sécurité web et
                le référencement. Notre mission est de rendre ces technologies
                accessibles à tous.
              </p>

              <ul className="space-y-4 mb-6">
                <ListItem
                  title="Plus de 10.000 audits réalisés"
                  description="Notre plateforme a déjà analysé des milliers de sites web"
                />
                <ListItem
                  title="100+ vulnérabilités détectées"
                  description="Notre technologie identifie plus de 100 types de failles"
                />
                <ListItem
                  title="Support 24/7"
                  description="Notre équipe est disponible pour vous aider à tout moment"
                />
              </ul>
            </div>

            <div className="space-y-6">
              <TestimonialCard
                name="Jean Dupont"
                role="Développeur Web"
                comment="Un outil indispensable pour tous les propriétaires de site web. J'ai pu corriger des failles de sécurité dont je n'avais pas conscience."
                rating={5}
              />

              <TestimonialCard
                name="Sophie Martin"
                role="Responsable Marketing"
                comment="Grâce à SiteChecker, notre trafic organique a augmenté de 40% en seulement 3 mois. Je recommande vivement !"
                rating={5}
              />
            </div>
          </div>
        </div>
      </section>

      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <h2 className="text-2xl md:text-3xl font-bold text-white text-center mb-8">
            Ce qu'en disent nos clients
          </h2>

          <div className="grid md:grid-cols-3 gap-6 mt-12">
            <TestimonialCard
              name="Thomas Durand"
              role="E-commerce Owner"
              comment="Un outil complet qui m'a permis d'optimiser mon site et d'améliorer ma visibilité sur Google."
              rating={5}
              className="animate-fade-in"
            />
            <TestimonialCard
              name="Marie Leclerc"
              role="Blogueuse"
              comment="Interface intuitive et rapports détaillés. J'ai pu corriger facilement les problèmes identifiés."
              rating={4}
              className="animate-fade-in delay-100"
            />
            <TestimonialCard
              name="Pierre Moreau"
              role="Agence Web"
              comment="Nous utilisons SiteChecker pour tous nos clients. Un gain de temps considérable pour nos audits."
              rating={5}
              className="animate-fade-in delay-200"
            />
          </div>

          <div className="mt-12 text-center">
            <Button
              size="lg"
              variant="outline"
              className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm transition-all duration-300 transform hover:scale-105"
              aria-label="Voir tous les témoignages"
            >
              Voir tous les témoignages
            </Button>
          </div>
        </div>
      </section>

      <section className="py-16 px-4 bg-white/5">
        <div className="container mx-auto max-w-lg text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-white mb-6">
            Prêt à améliorer votre présence en ligne ?
          </h2>
          <p className="text-white/80 mb-8">
            Rejoignez des milliers d'utilisateurs qui ont déjà optimisé leur
            site web grâce à notre plateforme.
          </p>
          <Link to="/register">
            <Button
              size="lg"
              withArrow
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white transition-all duration-300 transform hover:scale-105 backdrop-blur-sm shadow-lg hover:shadow-xl"
              aria-label="Commencer gratuitement"
            >
              Commencer gratuitement
            </Button>
          </Link>
          <p className="text-sm text-white/60 mt-4">
            Aucune carte de crédit requise
          </p>
        </div>
      </section>
    </Layout>
  );
}

function CheckItem({ text }: { text: string }) {
  return (
    <div className="flex items-start">
      <CheckCircle2 className="w-5 h-5 text-sitechecker-blue mt-0.5 flex-shrink-0" />
      <span className="ml-2 text-gray-700">{text}</span>
    </div>
  );
}

function StepCard({
  number,
  title,
  description,
  delay = 0,
}: {
  number: number;
  title: string;
  description: string;
  delay?: number;
}) {
  return (
    <div
      className="flex flex-col items-center text-center animate-fade-in"
      style={{ animationDelay: `${delay}ms` }}
    >
      <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center text-sitechecker-blue font-bold text-xl mb-4">
        {number}
      </div>
      <h3 className="text-lg font-medium text-white mb-2">{title}</h3>
      <p className="text-white/70 text-sm">{description}</p>
    </div>
  );
}

function ListItem({
  title,
  description,
}: {
  title: string;
  description: string;
}) {
  return (
    <div className="flex items-start">
      <div className="bg-sitechecker-blue/20 p-1 rounded-full mt-1">
        <CheckCircle2 className="w-4 h-4 text-sitechecker-blue" />
      </div>
      <div className="ml-3">
        <h4 className="text-white font-medium">{title}</h4>
        <p className="text-white/70 text-sm">{description}</p>
      </div>
    </div>
  );
}
