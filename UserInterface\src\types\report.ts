// Common types
export type StatusType = "success" | "warning" | "error";
export type PriorityType = 1 | 2 | 3; // 1 = High, 2 = Medium, 3 = Low

// Security Types
export interface SecurityDetail {
  message: string;
  status: StatusType;
  recommendation?: string;
}

export interface SecurityCategory {
  name: string;
  score: number;
  details: SecurityDetail[];
}

export interface SslDetail {
  certificateType: string;
  expirationDate: string;
  issuer: string;
  cipherStrength: string;
  forwardSecrecy: boolean;
  hstsEnabled: boolean;
  ocspStapling: boolean;
  vulnerabilities: string[];
  supportsTls13: boolean;
  severity: string;
  // Champs additionnels pour les détails du certificat
  not_valid_after?: string;
  not_valid_before?: string;
  subject?: string;
}

export interface HttpHeader {
  name: string;
  value: string;
  description?: string;
  securityImpact?: string;
  recommended?: boolean;
  importance?: string;
  status?: StatusType;
}

export interface VulnerabilityDetail {
  id: string;
  name: string;
  cveId?: string;
  severity: "low" | "medium" | "high" | "critical";
  description: string;
  affectedComponent?: string;
  fixAvailable?: boolean;
  fixVersion?: string;
  type?: string;
  impact?: string;
  fixDescription?: string;
}

export interface JsLibrary {
  name: string;
  version: string;
  vulnerabilities: number;
  severity: "critical" | "high" | "medium" | "low" | "none";
  outdated: boolean;
  latestVersion?: string;
  description?: string;
  vulnerabilityDetails?: {
    id: string;
    title: string;
    severity: string;
    description: string;
    fixedIn?: string;
  }[];
}

export interface ApiEndpoint {
  url: string;
  method: string;
  authentication: boolean;
  rateLimit: boolean;
  inputValidation: boolean;
  securityHeaders: boolean;
  issues: {
    title: string;
    description: string;
    severity: "critical" | "high" | "medium" | "low";
    recommendation: string;
  }[];
}

export interface ApiAuthIssue {
  title: string;
  description: string;
  severity: "critical" | "high" | "medium" | "low";
  recommendation: string;
}

export interface SecurityReport {
  success: boolean;
  error?: string;
  scan_date: string;
  overall_score: number;
  categories: SecurityCategory[];
  ssl_details: SslDetail;
  http_headers: HttpHeader[];
  vulnerabilities: VulnerabilityDetail[];
  recommended_actions: ReportAction[];
  ssl_score?: number;
  headers_score?: number;
  vulnerabilities_score?: number;
  js_security_score?: number;
  api_security_score?: number;

  // JS Security details
  js_libraries?: JsLibrary[];
  js_total_vulnerabilities?: number;
  js_critical_vulnerabilities?: number;
  js_high_vulnerabilities?: number;
  js_medium_vulnerabilities?: number;
  js_low_vulnerabilities?: number;

  // API Security details
  api_endpoints?: ApiEndpoint[];
  api_auth_issues?: ApiAuthIssue[];
  api_detected?: boolean;
}

// SEO Types
export interface SeoDetail {
  message: string;
  status: StatusType;
  recommendation?: string;
}

export interface SeoCategory {
  name: string;
  score: number;
  details: SeoDetail[];
}

export interface MetaTag {
  name: string;
  content: string;
  importance: "high" | "medium" | "low";
  description: string;
  status: StatusType;
  recommendation?: string;
}

export interface PerformanceMetrics {
  responseTime: string;
  compressionRatio?: string;
  cacheStatus?: string;
  pageSize?: string;
  firstContentfulPaint?: string;
  largestContentfulPaint?: string;
  timeToInteractive?: string;
  cumulativeLayoutShift?: string;
  speedIndex?: string;
  resourceWaterfallAnalysis?: Array<{
    url: string;
    type: string;
    size: string;
    loadTime: string;
  }>;
}

export interface MobileOptimization {
  responsiveDesign: boolean;
  viewportConfiguration: boolean;
  touchTargets: boolean;
  fontSizes: boolean;
  mobileUsability: boolean;
}

export interface ContentAnalysis {
  wordCount: number;
  readabilityScore?: number;
  keywordDensity?: Record<string, number>;
  headingsStructure?: {
    h1Count: number;
    h2Count: number;
    h3Count: number;
    h4Count: number;
    h5Count: number;
    h6Count: number;
    headingsInOrder: boolean;
  };
  semanticAnalysis?: {
    keyPhrases: string[];
    sentiment: string;
  };
  structuredData?: Array<{
    type: string;
    valid: boolean;
    errors?: string[];
  }>;
  contentQualityScore?: number;
}

export interface ImageAnalysis {
  totalImages: number;
  imagesWithAlt: number;
  imagesWithoutAlt: number;
  imagesCompressed: number;
  imagesUncompressed: number;
  imagesWithLazyLoading: number;
  largeImages: string[];
  missingAltImages: string[];
}

export interface SeoReport {
  success: boolean;
  error?: string;
  scan_date: string;
  overall_score: number;
  categories: SeoCategory[];
  meta_tags: MetaTag[];
  performance_metrics: PerformanceMetrics;
  mobile_optimization: MobileOptimization;
  url_structure: UrlStructureItem[];
  content_analysis: ContentAnalysis | null;
  image_analysis: ImageAnalysis | null;
  recommended_actions: ReportAction[];
  meta_score?: number;
  mobile_score?: number;
  performance_score?: number;
  content_score?: number;
  image_score?: number;
}

// Advanced Scan Types
export interface CertificateDetail {
  subject: string;
  issuer: string;
  validFrom: string;
  validTo: string;
  fingerprint: string;
}

export interface AdvancedSslDetail {
  protocolVersions: string[];
  keyExchange: string;
  certificateChain: CertificateDetail[];
  vulnerabilities: string[];
}

export interface BrowserScanDetail {
  screenshotUrl: string;
  consoleErrors: string[];
  blockedResources: string[];
  jsExecutionTime: string;
  domLoadTime: string;
}

// Common Action Type
export interface ReportAction {
  title: string;
  description: string;
  priority: PriorityType;
  type:
    | "security"
    | "seo"
    | "meta_tags"
    | "mobile_optimization"
    | "content"
    | "url_structure"
    | "images"
    | "performance"
    | "SSL/TLS"
    | "HTTP Headers"
    | "Vulnerabilities"
    | "JavaScript Security"
    | "API Security";
  implementationDifficulty?: "easy" | "medium" | "hard";
  estimatedImpact?: "low" | "medium" | "high";
}

export enum ScanDepth {
  BASIC = "basic",
  COMPREHENSIVE = "comprehensive",
}

export interface ScanRequest {
  url: string;
  scan_depth: ScanDepth;
  max_redirects?: number;
  timeout?: number;
  user_agent?: string;
  frontend_port?: number;
}

export interface CategoryItem {
  name: string;
  score: number;
  details: CategoryDetailItem[];
  recommendations?: string[];
}

export interface CategoryDetailItem {
  message: string;
  status: StatusType;
  recommendation?: string;
}

export interface Vulnerability {
  name: string;
  status: "present" | "absent" | "error";
  severity: "high" | "medium" | "low";
  details: string;
  recommendation: string;
}

export interface UrlStructureItem {
  type: string;
  detail: string;
  status: StatusType;
  recommendation?: string;
}

export interface RecommendedAction {
  title: string;
  description: string;
  priority: PriorityLevel;
  type: string;
  implementation_difficulty: "low" | "medium" | "high";
  estimated_impact: "low" | "medium" | "high";
}

export enum PriorityLevel {
  HIGH = "high",
  MEDIUM = "medium",
  LOW = "low",
}

// French translations
export const FRENCH_TRANSLATIONS = {
  categories: {
    security: {
      ssl: "SSL/TLS",
      headers: "En-têtes HTTP",
      vulnerabilities: "Vulnérabilités",
      "javascript security": "Sécurité JavaScript",
      "api security": "Sécurité API",
    },
    seo: {
      meta: "Balises Meta",
      mobile: "Optimisation Mobile",
      performance: "Performance",
      content: "Analyse du Contenu",
      "image optimization": "Optimisation des Images",
      images: "Images",
    },
  },
  status: {
    success: "Succès",
    warning: "Attention",
    error: "Erreur",
  },
  priority: {
    high: "Élevée",
    medium: "Moyenne",
    low: "Basse",
  },
  impact: {
    high: "Élevé",
    medium: "Moyen",
    low: "Faible",
  },
  difficulty: {
    high: "Difficile",
    medium: "Moyen",
    low: "Facile",
  },
  recommendations: {
    title: "Recommandations",
    description: "Voici les recommandations pour améliorer votre site",
  },
  browserMetrics: {
    title: "Métriques du Navigateur",
    consoleErrors: "Erreurs de la Console",
    blockedResources: "Ressources Bloquées",
    jsExecutionTime: "Temps d'Exécution JS",
    domLoadTime: "Temps de Chargement DOM",
  },
};
