import React, { useState } from "react";
import axios from "axios";
import { Link, useNavigate } from "react-router-dom";
import Layout from "../components/Layout";
import Card from "../components/ui-custom/Card";
import Input from "../components/ui-custom/Input";
import Button from "../components/ui-custom/Button";
import Logo from "../components/ui-custom/Logo";
import { Switch } from "@headlessui/react";
import { showSuccessToast, showErrorToast } from "../utils/toastUtils";
import { Star, Zap, Check, X } from "lucide-react";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";
import { PasswordCriteriaList } from "../components/PasswordCriteriaList";
import ValidationMessage from "../components/ValidationMessage";

export default function Register() {
  // Add username validation function
  const isValidUsername = (username: string): boolean => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  };

  const [username, setUsername] = useState("");
  const [usernameAvailable, setUsernameAvailable] = useState<boolean | null>(
    null
  );
  const [usernameMessage, setUsernameMessage] = useState<string>("");
  const [email, setEmail] = useState("");
  const [emailAvailable, setEmailAvailable] = useState<boolean | null>(null);
  const [emailMessage, setEmailMessage] = useState<string>("");
  const [password, setPassword] = useState("");
  // Only allow "free" or "pro" (premium) plans
  const [plan, setPlan] = useState<"free" | "pro">("free");
  const [billingCycle, setBillingCycle] = useState("monthly");
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const [passwordCriteria, setPasswordCriteria] = useState({
    length: false,
    uppercase: false,
    number: false,
    specialChar: false,
  });

  const { executeRecaptcha } = useGoogleReCaptcha();

  const updatePasswordCriteria = (password: string) => {
    setPasswordCriteria({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
      specialChar: /[!@#$%^&*]/.test(password),
    });
  };

  // Debounce helper
  function debounce<Func extends (...args: any[]) => void>(
    func: Func,
    wait: number
  ) {
    let timeout: ReturnType<typeof setTimeout>;
    return (...args: Parameters<Func>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  }

  const checkUsernameAvailability = async (usernameToCheck: string) => {
    if (!usernameToCheck) {
      setUsernameAvailable(null);
      setUsernameMessage("");
      return;
    }
    try {
      const response = await axios.get(
        `http://localhost:5012/api/auth/check-username?username=${encodeURIComponent(
          usernameToCheck
        )}`
      );
      const available = !response.data.exists;
      if (available) {
        setUsernameAvailable(true);
        setUsernameMessage("Nom d'utilisateur disponible");
      } else {
        setUsernameAvailable(false);
        setUsernameMessage(
          "Ce nom d'utilisateur est déjà pris. Veuillez en choisir un autre."
        );
      }
    } catch (error) {
      setUsernameAvailable(null);
      setUsernameMessage("Erreur lors de la vérification");
    }
  };

  const debouncedCheckUsername = React.useCallback(
    debounce(checkUsernameAvailability, 500),
    []
  );

  const checkEmailAvailability = async (emailToCheck: string) => {
    if (!emailToCheck) {
      setEmailAvailable(null);
      setEmailMessage("");
      return;
    }
    try {
      const response = await axios.get(
        `http://localhost:5012/api/auth/check-email?email=${encodeURIComponent(
          emailToCheck
        )}`
      );
      if (response.data.exists === false) {
        setEmailAvailable(true);
        setEmailMessage("Email disponible");
      } else {
        setEmailAvailable(false);
        setEmailMessage(
          "Ce compte existe déjà. Veuillez vous connecter ou réinitialiser votre mot de passe."
        );
      }
    } catch (error) {
      setEmailAvailable(null);
      setEmailMessage("Erreur lors de la vérification");
    }
  };

  const debouncedCheckEmail = React.useCallback(
    debounce(checkEmailAvailability, 500),
    []
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !email || !password) {
      showErrorToast("Veuillez remplir tous les champs");
      return;
    }

    if (usernameAvailable === false) {
      showErrorToast("Nom d'utilisateur déjà pris");
      return;
    }

    if (emailAvailable === false) {
      showErrorToast(
        "Ce compte existe déjà. Veuillez vous connecter ou réinitialiser votre mot de passe."
      );
      return;
    }

    if (!Object.values(passwordCriteria).every(Boolean)) {
      showErrorToast("Le mot de passe ne répond pas aux critères de sécurité");
      return;
    }

    if (!executeRecaptcha) {
      showErrorToast("Captcha is not yet available");
      return;
    }

    setLoading(true);
    try {
      let token = "";
      let isV2Fallback = false;

      // Try reCAPTCHA v3 first
      if (executeRecaptcha) {
        try {
          console.log("Executing reCAPTCHA v3 with action: register");
          token = await executeRecaptcha("register");
          console.log(
            "reCAPTCHA v3 token received:",
            token ? "Token received" : "No token"
          );
        } catch (error) {
          console.error("reCAPTCHA v3 execution failed:", error);
        }
      }

      // If v3 fails or is not available, fall back to v2
      if (!token) {
        isV2Fallback = true;
        console.log("Falling back to reCAPTCHA v2");
        showErrorToast(
          "reCAPTCHA v3 failed. Please complete the challenge below."
        );
        token = await triggerRecaptchaV2();
        console.log(
          "reCAPTCHA v2 token received:",
          token ? "Token received" : "No token"
        );
      }

      if (!token) {
        showErrorToast("Captcha verification failed. Please try again.");
        setLoading(false);
        return;
      }

      const response = await axios.post(
        "http://localhost:5012/api/auth/register",
        {
          username,
          email,
          password,
          plan: plan === "pro" ? "Premium" : "Free",
          billingCycle:
            billingCycle.charAt(0).toUpperCase() + billingCycle.slice(1),
          captchaToken: token,
          isV2Fallback: isV2Fallback.toString(),
        }
      );

      const data = response.data;
      console.log("API Response:", data);
      showSuccessToast(data.message || "Inscription réussie");

      if (response.status === 200) {
        showSuccessToast(
          typeof data.message === "string"
            ? data.message
            : "Inscription réussie"
        );
        navigate(`/check-email?email=${encodeURIComponent(email)}`);
      } else {
        showErrorToast(
          typeof data.message === "string"
            ? data.message
            : "Erreur lors de l'inscription"
        );
      }
    } catch (error: any) {
      console.error("API Error:", error);
      if (error.response && error.response.data) {
        console.log("Error response data:", error.response.data);
      }

      if (error.response && error.response.data && error.response.data.errors) {
        console.log("Validation errors:", error.response.data.errors); // Debugging log
        // Handle validation errors dictionary
        const errors = error.response.data.errors;
        for (const key in errors) {
          if (errors.hasOwnProperty(key)) {
            const messages = errors[key];
            messages.forEach((msg: string) => {
              showErrorToast(msg);
            });
          }
        }
      } else if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        const message = error.response.data.message;
        if (message === "Email is already registered") {
          showErrorToast(
            "Ce compte existe déjà. Veuillez vous connecter ou réinitialiser votre mot de passe."
          );
        } else if (message === "Username is already taken") {
          showErrorToast(
            "Ce nom d'utilisateur est déjà pris. Veuillez en choisir un autre."
          );
        } else {
          showErrorToast(message);
        }
      } else {
        showErrorToast("Erreur de connexion au serveur");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout showNav={false} showFooter={!loading}>
      <div className="min-h-screen flex flex-col items-center bg-gradient-to-br from-blue-400 to-purple-500">
        <div className="w-full py-4 px-6 flex justify-between items-center bg-transparent backdrop-blur-sm fixed top-0 z-50 hover:bg-white/10 transition duration-300">
          <Logo to="/" />
          <div className="flex gap-8">
            <a href="#" className="text-white hover:text-indigo-100">
              Services
            </a>
            <a href="#" className="text-white hover:text-indigo-100">
              À propos
            </a>
            <a href="#" className="text-white hover:text-indigo-100">
              Avis
            </a>
          </div>
        </div>

        <div className="flex-1 flex flex-col items-center justify-center w-full px-6 pb-12 mt-20">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">
            Choisissez votre plan
          </h1>

          <div className="flex items-center justify-center gap-6 mb-12 bg-white/10 backdrop-blur-sm p-3 px-6 rounded-full shadow-lg">
            <span
              className={`text-sm font-medium ${
                billingCycle === "monthly" ? "text-white" : "text-indigo-200"
              }`}
            >
              Mensuel
            </span>

            <Switch
              checked={billingCycle === "annual"}
              onChange={() =>
                setBillingCycle(
                  billingCycle === "monthly" ? "annual" : "monthly"
                )
              }
              className={`relative inline-flex h-7 w-14 items-center rounded-full transition-colors duration-300 focus:outline-none ${
                billingCycle === "annual" ? "bg-indigo-600" : "bg-gray-400"
              }`}
            >
              <span className="sr-only">Select billing cycle</span>
              <span
                className={`${
                  billingCycle === "annual" ? "translate-x-7" : "translate-x-1"
                } inline-block h-5 w-5 transform rounded-full bg-white shadow-md transition-transform duration-300`}
              />
            </Switch>

            <div className="flex items-center gap-2">
              <span
                className={`text-sm font-medium ${
                  billingCycle === "annual" ? "text-white" : "text-indigo-200"
                }`}
              >
                Annuel
              </span>
              <span className="text-xs font-medium text-white bg-indigo-600 px-2 py-1 rounded-full">
                25% de réduction
              </span>
            </div>
          </div>

          {/* Plan cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full max-w-5xl mb-12">
            {/* Free Plan */}
            <div
              className={`rounded-2xl overflow-hidden transition-all duration-300 hover:scale-105 backdrop-blur-sm shadow-xl ${
                plan === "free"
                  ? "ring-2 ring-white/70 shadow-indigo-300/20"
                  : "bg-white/5"
              }`}
              onClick={() => setPlan("free")}
            >
              <div className="p-6 backdrop-blur-sm bg-gradient-to-r from-blue-500/30 to-purple-500/30 border-b border-white/10">
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-white/20">
                    <Star className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Gratuit</h3>
                </div>
                <p className="text-4xl font-bold text-white">0 Dt</p>
                <p className="text-indigo-200 mt-1">
                  Analyses de base pour débutants
                </p>
              </div>

              <div className="p-6 backdrop-blur-sm bg-white/5">
                <ul className="space-y-4">
                  <FeatureItem included text="Rapports basiques" />
                  <FeatureItem included text="Visualisation en ligne" />
                  <FeatureItem included text="Support par email" />
                  <FeatureItem text="Analyses avancées" />
                  <FeatureItem text="Téléchargement des rapports" />
                  <FeatureItem text="Analyses illimitées" />
                  <FeatureItem text="Support prioritaire" />
                </ul>

                <button
                  className={`w-full py-3 mt-6 rounded-xl font-medium transition-colors ${
                    plan === "free"
                      ? "bg-white text-indigo-700"
                      : "bg-white/10 text-white hover:bg-white/20"
                  }`}
                >
                  {plan === "free" ? "Plan Sélectionné" : "Sélectionner"}
                </button>
              </div>
            </div>

            {/* Pro Plan */}
            <div
              className={`rounded-2xl overflow-hidden transition-all duration-300 hover:scale-105 backdrop-blur-sm shadow-xl ${
                plan === "pro"
                  ? "ring-2 ring-white/70 shadow-indigo-300/40 transform scale-105"
                  : "bg-white/5"
              }`}
              onClick={() => setPlan("pro")}
            >
              <div className="p-6 backdrop-blur-sm bg-gradient-to-r from-indigo-600/80 to-purple-600/80 border-b border-white/10 relative overflow-hidden">
                {plan === "pro" && (
                  <div className="absolute top-0 right-0 bg-white text-indigo-700 text-xs font-bold px-3 py-1 transform rotate-0 translate-x-2 -translate-y-0">
                    POPULAIRE
                  </div>
                )}
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-white/20">
                    <Zap className="w-5 h-5 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white">Premium</h3>
                </div>
                <p className="text-4xl font-bold text-white">
                  5Dt <span className="text-sm font-normal">/mois</span>
                </p>
                <p className="text-indigo-200 mt-1">
                  Analyses complètes et téléchargements
                </p>
              </div>

              <div className="p-6 backdrop-blur-sm bg-white/5">
                <ul className="space-y-4">
                  <FeatureItem included text="Rapports complets" />
                  <FeatureItem included text="Visualisation en ligne" />
                  <FeatureItem included text="Téléchargement des rapports" />
                  <FeatureItem included text="Analyses avancées" />
                  <FeatureItem included text="Analyses illimitées" />
                  <FeatureItem included text="Support prioritaire" />
                  <FeatureItem included text="Historique complet" />
                </ul>

                <button
                  className={`w-full py-3 mt-6 rounded-xl font-medium transition-colors ${
                    plan === "pro"
                      ? "bg-indigo-600 text-white shadow-lg shadow-indigo-600/20"
                      : "bg-white/10 text-white hover:bg-white/20"
                  }`}
                >
                  {plan === "pro" ? "Plan Sélectionné" : "Sélectionner"}
                </button>
              </div>
            </div>
          </div>

          <Card className="w-full max-w-md bg-white/95 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
            <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
              Créer votre compte
            </h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <Input
                type="text"
                placeholder="Nom d'utilisateur"
                value={username}
                onChange={(e) => {
                  setUsername(e.target.value);
                  if (!isValidUsername(e.target.value)) {
                    setUsernameAvailable(false);
                    setUsernameMessage(
                      "3-20 caractères (lettres, chiffres, underscore)"
                    );
                    return;
                  }
                  debouncedCheckUsername(e.target.value);
                }}
                required
              />
              <ValidationMessage
                isValid={usernameAvailable}
                message={usernameMessage}
                value={username}
              />
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  debouncedCheckEmail(e.target.value);
                }}
                required
              />
              <ValidationMessage
                isValid={emailAvailable}
                message={emailMessage}
                value={email}
              />
              <div>
                <Input
                  type="password"
                  placeholder="Mot de passe"
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value);
                    updatePasswordCriteria(e.target.value);
                  }}
                  required
                  autoComplete="off"
                  data-lpignore="true"
                />
                <PasswordCriteriaList
                  criteria={passwordCriteria}
                  password={password}
                />
              </div>
              <Button
                type="submit"
                className="w-full rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90 text-white shadow-lg shadow-indigo-600/20"
                loading={loading}
                disabled={
                  loading ||
                  usernameAvailable === false ||
                  emailAvailable === false
                }
              >
                S'inscrire
              </Button>
            </form>
            <p className="mt-6 text-center text-sm text-gray-600">
              Déjà inscrit ?{" "}
              <Link
                to="/login"
                className="text-indigo-600 hover:underline font-medium"
              >
                Connectez-vous
              </Link>
            </p>
          </Card>
        </div>
      </div>
    </Layout>
  );
}

const FeatureItem = ({
  included = false,
  text,
}: {
  included?: boolean;
  text: string;
}) => (
  <li className="flex items-center gap-3">
    {included ? (
      <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-green-500/20">
        <Check className="w-3 h-3 text-green-400" />
      </div>
    ) : (
      <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 rounded-full bg-gray-500/20">
        <X className="w-3 h-3 text-gray-400" />
      </div>
    )}
    <span className={`text-sm ${included ? "text-white" : "text-gray-400"}`}>
      {text}
    </span>
  </li>
);
async function triggerRecaptchaV2(): Promise<string> {
  return new Promise((resolve) => {
    // Create a container for the reCAPTCHA
    const container = document.createElement("div");
    container.className = "g-recaptcha-container";
    document.body.appendChild(container);

    // Load reCAPTCHA script if not already loaded
    if (!window.grecaptcha || !window.grecaptcha.render) {
      const script = document.createElement("script");
      script.src = "https://www.google.com/recaptcha/api.js";
      script.async = true;
      script.defer = true;

      script.onload = () => {
        renderCaptcha();
      };

      document.head.appendChild(script);
    } else {
      renderCaptcha();
    }

    function renderCaptcha() {
      // Render the reCAPTCHA
      window.grecaptcha.ready(() => {
        const widgetId = window.grecaptcha.render(container, {
          sitekey:
            import.meta.env.VITE_RECAPTCHA_SITE_KEY_V2 ||
            "6LdtTCYrAAAAAN6mv1dND-5_0vCBJgeX_hkIGGGc",
          callback: (response: string) => {
            // Clean up
            document.body.removeChild(container);
            resolve(response);
          },
          "expired-callback": () => {
            document.body.removeChild(container);
            resolve("");
          },
        });
      });
    }
  });
}
