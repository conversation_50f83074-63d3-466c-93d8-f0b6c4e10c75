import axios from "axios";
import {
  SecurityReport,
  SeoReport,
  AdvancedSslDetail,
  BrowserScanDetail,
  ScanRequest,
  ScanDepth,
  ImageAnalysis,
} from "../types/report";
import { ReportGenerator } from "../utils/ReportGenerator";
import { safeStorage } from "../utils/storage";

// Override base URL to FastAPI backend for scan endpoints
const SCAN_API_BASE_URL = "http://localhost:8000";
const AUTH_API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:5012";

// Create a separate axios instance for scan endpoints
const scanApiClient = axios.create({
  baseURL: SCAN_API_BASE_URL,
  withCredentials: true, // Enable sending credentials
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "Cache-Control": "no-cache",
    Pragma: "no-cache",
  },
  timeout: 180000, // Default timeout of 3 minutes
  maxContentLength: 100 * 1024 * 1024, // 100MB max response size
  maxBodyLength: 100 * 1024 * 1024, // 100MB max request size
  // Retry configuration
  validateStatus: function (status) {
    return (status >= 200 && status < 300) || status === 429; // Retry on 429 (too many requests)
  },
});

let isRefreshing = false;
let failedQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token!);
    }
  });
  failedQueue = [];
};

// Add request interceptor for scan API client
scanApiClient.interceptors.request.use(
  async (config) => {
    // Include credentials for cookies
    config.withCredentials = true;

    try {
      // Get the current access token from the centralized token manager
      // This will either return a cached token, wait for an in-progress refresh,
      // or initiate a new refresh if needed
      const token = await apiService.refreshToken();

      // If we have a token, add it to the Authorization header
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      } else {
        // We'll rely on the backend's development mode fallback
        // No need to log a warning as the token manager already handles that
      }
    } catch (error) {
      // Error handling is done in the token manager
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token refresh
scanApiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle 401/403 errors (unauthorized/forbidden)
    if (
      (error.response?.status === 401 || error.response?.status === 403) &&
      !originalRequest._retry
    ) {
      // If we're already refreshing a token, queue this request
      if (isRefreshing) {
        try {
          await new Promise<void>((resolve, reject) => {
            failedQueue.push({
              resolve: () => resolve(),
              reject,
            });
          });
          return scanApiClient(originalRequest);
        } catch (err) {
          return Promise.reject(err);
        }
      }

      // Mark this request as retried
      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Try to refresh the token using cookies
        await apiService.refreshToken();

        // With cookie-based auth, we don't need to update headers
        // The cookies will be sent automatically

        // Process any queued requests
        processQueue(null, "");
        isRefreshing = false;

        // Retry the original request
        return scanApiClient(originalRequest);
      } catch (refreshError) {
        console.error("Token refresh failed:", refreshError);

        // Process any queued requests with the error
        processQueue(refreshError, null);
        isRefreshing = false;

        // Don't redirect here, let the component handle authentication
        return Promise.reject({
          ...error,
          isAuthError: true, // Add a flag to indicate this is an auth error
          message: "Authentication failed. Please log in again.",
        });
      }
    }

    return Promise.reject(error);
  }
);

const requestCache = new Map();

const getScanParams = (url: string, scanDepth: ScanDepth = ScanDepth.BASIC) => {
  // Ensure URL has a protocol
  let processedUrl = url;
  if (
    !processedUrl.startsWith("http://") &&
    !processedUrl.startsWith("https://")
  ) {
    processedUrl = "https://" + processedUrl;
  }

  // Use consistent parameters for all websites
  // This ensures a solid and reliable scanning process
  return {
    url: processedUrl,
    scan_depth: scanDepth,
    max_redirects: 10, // Increased for all sites to handle complex redirects
    timeout: 180, // 3 minutes timeout for all sites
    user_agent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
    frontend_port: 8080,
  };
};

// Types pour les opérations de profil utilisateur
interface UpdateProfileRequest {
  username: string;
  email: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Types pour les opérations d'administration
interface UserInfo {
  id: string;
  email: string;
  username: string;
  role: number;
  isVerified: boolean;
  isActive: boolean;
  createdAt: string;
}

interface PlatformStats {
  totalUsers: number;
  freeUsers: number;
  premiumUsers: number;
  adminUsers: number;
  totalScans: number;
  securityScans: number;
  seoScans: number;
  registrationsLastMonth: number;
  averageScansPerUser: number;
}

// Define the RecentScan interface
interface RecentScan {
  url: string;
  timestamp: string;
  scanDepth: string;
  scanType?: string;
  score?: number;
  isSuccessful?: boolean;
}

export const apiService = {
  // Method to get recent scans for the current user
  async getRecentScans(limit?: number): Promise<RecentScan[]> {
    try {
      // Try to get recent scans from the API using the ScanHistory controller endpoint
      // This endpoint provides more detailed scan history for premium users
      const response = await axios.get(
        `${AUTH_API_BASE_URL}/api/scanhistory/recent${
          limit ? `?limit=${limit}` : ""
        }`,
        {
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error: any) {
      console.error("Error fetching recent scans:", error);

      // Fallback to the auth endpoint if the scanhistory endpoint fails
      try {
        const fallbackResponse = await axios.get(
          `${AUTH_API_BASE_URL}/api/auth/recent-scans`,
          {
            withCredentials: true,
          }
        );
        return fallbackResponse.data;
      } catch (fallbackError) {
        console.error("Fallback also failed:", fallbackError);
        // If both endpoints fail, return an empty array
        console.warn("Returning empty array for recent scans due to API error");
        return [];
      }
    }
  },

  // Method to get detailed scan history for premium users
  async getPremiumScanHistory(limit: number = 50): Promise<RecentScan[]> {
    try {
      // This endpoint is only accessible to premium users
      const response = await axios.get(
        `${AUTH_API_BASE_URL}/api/scanhistory/premium/history?limit=${limit}`,
        {
          withCredentials: true,
        }
      );
      return response.data;
    } catch (error: any) {
      console.error("Error fetching premium scan history:", error);

      // If the user is not premium or there's another error, fall back to regular scan history
      if (error.response?.status === 403) {
        console.warn(
          "User does not have premium access, falling back to regular scan history"
        );
        return this.getRecentScans(limit);
      }

      // If the endpoint doesn't exist or there's another error, return an empty array
      console.warn(
        "Returning empty array for premium scan history due to API error"
      );
      return [];
    }
  },

  // Méthode pour mettre à jour le profil utilisateur
  async updateUserProfile(data: UpdateProfileRequest): Promise<void> {
    try {
      await axios.put(`${AUTH_API_BASE_URL}/api/auth/update-profile`, data, {
        withCredentials: true,
      });
    } catch (error: any) {
      console.error("Error updating user profile:", error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error("Erreur lors de la mise à jour du profil");
    }
  },

  // Admin API methods
  async getAllUsers(): Promise<UserInfo[]> {
    try {
      const response = await axios.get(`${AUTH_API_BASE_URL}/api/admin/users`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error: any) {
      console.error("Error fetching users:", error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error("Erreur lors de la récupération des utilisateurs");
    }
  },

  async updateUserRole(userId: string, role: number): Promise<void> {
    try {
      await axios.put(
        `${AUTH_API_BASE_URL}/api/admin/users/${userId}/role`,
        { role },
        { withCredentials: true }
      );
    } catch (error: any) {
      console.error(`Error updating role for user ${userId}:`, error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error("Erreur lors de la mise à jour du rôle de l'utilisateur");
    }
  },

  async updateUserStatus(userId: string, isActive: boolean): Promise<void> {
    try {
      await axios.put(
        `${AUTH_API_BASE_URL}/api/admin/users/${userId}/status`,
        { isActive },
        { withCredentials: true }
      );
    } catch (error: any) {
      console.error(`Error updating status for user ${userId}:`, error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error(
        "Erreur lors de la mise à jour du statut de l'utilisateur"
      );
    }
  },

  async getPlatformStats(): Promise<PlatformStats> {
    try {
      const response = await axios.get(`${AUTH_API_BASE_URL}/api/admin/stats`, {
        withCredentials: true,
      });
      return response.data;
    } catch (error: any) {
      console.error("Error fetching platform stats:", error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error(
        "Erreur lors de la récupération des statistiques de la plateforme"
      );
    }
  },

  // Record scan history
  async recordScan(
    url: string,
    scanType: string,
    scanDepth: string,
    score: number | null,
    isSuccessful: boolean = true,
    errorMessage?: string
  ): Promise<void> {
    try {
      await axios.post(
        `${AUTH_API_BASE_URL}/api/scanhistory`,
        {
          url,
          scanType,
          scanDepth,
          score,
          isSuccessful,
          errorMessage,
        },
        {
          withCredentials: true,
        }
      );
    } catch (error: any) {
      console.error("Error recording scan:", error);
      // Don't throw here - we don't want to interrupt the user experience
      // if scan recording fails
    }
  },

  // Méthode pour changer le mot de passe
  async changePassword(data: ChangePasswordRequest): Promise<void> {
    try {
      await axios.post(`${AUTH_API_BASE_URL}/api/auth/change-password`, data, {
        withCredentials: true,
      });
    } catch (error: any) {
      console.error("Error changing password:", error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error("Erreur lors du changement de mot de passe");
    }
  },

  // Méthode pour réinitialiser le mot de passe depuis le profil
  async resetPasswordFromProfile(data: {
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> {
    try {
      await axios.post(
        `${AUTH_API_BASE_URL}/api/auth/reset-password-from-profile`,
        {
          newPassword: data.newPassword,
          confirmNewPassword: data.confirmPassword,
        },
        {
          withCredentials: true,
        }
      );
    } catch (error: any) {
      console.error("Error resetting password from profile:", error);
      if (error.response?.data?.message) {
        throw new Error(error.response.data.message);
      }
      throw new Error("Erreur lors de la réinitialisation du mot de passe");
    }
  },

  async fetchSecurityData(
    url: string,
    scanDepth: ScanDepth = ScanDepth.BASIC,
    abortSignal?: AbortSignal,
    onRetryProgress?: (
      progress: number,
      retryCount: number,
      maxRetries: number
    ) => void
  ): Promise<SecurityReport> {
    const cacheKey = `security-${url}-${scanDepth}`;
    if (requestCache.has(cacheKey)) {
      return requestCache.get(cacheKey);
    }

    const promise = (async () => {
      try {
        // Get scan parameters first to determine appropriate timeout
        const params = getScanParams(url, scanDepth);

        // Use the provided abort signal if available, otherwise create a new controller
        const controller = abortSignal
          ? { signal: abortSignal }
          : new AbortController();

        try {
          // Enhanced retry logic for all error types
          let retryCount = 0;
          const maxRetries = 5; // Increased max retries for better reliability

          while (retryCount <= maxRetries) {
            try {
              // Get token from cookies for authentication
              const cookies = document.cookie.split(";");
              const accessTokenCookie = cookies.find((cookie) =>
                cookie.trim().startsWith("access_token=")
              );
              const accessToken = accessTokenCookie
                ? accessTokenCookie.split("=")[1].trim()
                : null;

              // Make the request with consistent configuration
              const response = await scanApiClient.post(
                `/api/scan/security`,
                params,
                {
                  signal:
                    abortSignal ||
                    (controller instanceof AbortController
                      ? controller.signal
                      : undefined),
                  timeout: params.timeout * 1000,
                  headers: accessToken
                    ? {
                        Authorization: `Bearer ${accessToken}`,
                        "Cache-Control": "no-cache",
                        Pragma: "no-cache",
                      }
                    : {
                        "Cache-Control": "no-cache",
                        Pragma: "no-cache",
                      },
                }
              );

              // Validate the response data
              if (!response.data) {
                throw new Error("Empty response received from server");
              }

              return response.data;
            } catch (requestError: any) {
              // Check specifically for backend not running (connection refused)
              if (
                requestError.message === "Network Error" ||
                requestError.code === "ERR_NETWORK" ||
                requestError.code === "ERR_CONNECTION_REFUSED" ||
                (requestError.message === "Network Error" &&
                  !requestError.response)
              ) {
                console.error(
                  "Backend service is not running - connection error"
                );
                // This is likely a case where the backend is not running
                throw new Error("BACKEND_NOT_RUNNING");
              }

              // Retry on any error except backend not running
              if (retryCount < maxRetries) {
                retryCount++;
                // Exponential backoff with jitter - wait longer for each retry
                const baseWaitTime = 1000 * Math.pow(2, retryCount);
                // Add jitter (±20%) to prevent thundering herd problem
                const jitter = baseWaitTime * 0.2 * (Math.random() - 0.5);
                const waitTime = baseWaitTime + jitter;

                console.warn(
                  `Retry ${retryCount}/${maxRetries} for ${url} after ${Math.round(
                    waitTime
                  )}ms`
                );

                // Call the progress callback if provided
                if (onRetryProgress) {
                  // Calculate a small increment based on retry count to show activity
                  const retryProgress = 10 + retryCount * 2; // 12%, 14%, 16%, 18%, 20% for retries
                  onRetryProgress(retryProgress, retryCount, maxRetries);
                }

                await new Promise((resolve) => setTimeout(resolve, waitTime));
              } else {
                // For other errors or if we've exceeded max retries, throw the error
                throw requestError;
              }
            }
          }

          // This should never be reached due to the throw in the catch block
          throw new Error("Max retries exceeded");
        } catch (innerError) {
          throw innerError;
        }
      } catch (error: any) {
        // Special case for backend not running
        if (error.message === "BACKEND_NOT_RUNNING") {
          console.error("Backend service is not running");
          return {
            success: false,
            error:
              "Le service d'analyse n'est pas disponible. Veuillez vérifier que le backend est en cours d'exécution.",
            overall_score: 0,
            categories: [],
            ssl_details: {},
            http_headers: [],
            vulnerabilities: [],
            recommended_actions: [
              {
                title: "Vérifier le service backend",
                description:
                  "Le service d'analyse (backend) n'est pas accessible. Il est peut-être arrêté ou ne répond pas.",
                priority: 3, // Use numeric priority (3 = high)
                type: "security", // Add required type field
                implementationDifficulty: "medium", // Add required field
                estimatedImpact: "high", // Add required field
              },
            ],
            scan_date: new Date().toISOString(),
          };
        }

        // If it's a server error (500) or a timeout/network error, return a fallback report
        if (
          error.response?.status === 500 ||
          error.response?.status === 408 ||
          axios.isCancel(error) ||
          error.code === "ECONNABORTED" ||
          error.message === "Network Error" ||
          error.message === "canceled" ||
          error.message.includes("timeout") ||
          error.message.includes("exceeded")
        ) {
          console.warn(
            `Server error or timeout in security scan for ${url}, returning fallback report with error: ${error.message}`
          );

          // Special case for fst.rnu.tn
          const isFstRnuSite = url.includes("fst.rnu.tn");

          let errorMessage = "";
          if (
            error.message === "canceled" ||
            error.message.includes("timeout") ||
            error.message.includes("exceeded") ||
            error.code === "ECONNABORTED" ||
            error.response?.status === 408
          ) {
            errorMessage = isFstRnuSite
              ? "Scan timed out - fst.rnu.tn is known to respond very slowly. Try a different website."
              : "Scan timed out - the server took too long to respond";
          } else {
            errorMessage = `Server error occurred during scan: ${error.message}`;
          }

          return {
            success: false,
            error: errorMessage,
            overall_score: 0,
            categories: [],
            ssl_details: {},
            http_headers: [],
            vulnerabilities: [],
            recommended_actions: [
              {
                title: isFstRnuSite
                  ? "Try a different website"
                  : "Try again later",
                description: isFstRnuSite
                  ? "The website fst.rnu.tn is known to have performance issues that make it difficult to scan."
                  : "The server might be experiencing high load or connectivity issues.",
                priority: 3, // Use numeric priority (3 = high)
                type: "security", // Add required type field
                implementationDifficulty: "medium", // Add required field
                estimatedImpact: "high", // Add required field
              },
            ],
            scan_date: new Date().toISOString(),
          };
        }

        console.error("Error in security scan:", error);
        throw error;
      } finally {
        requestCache.delete(cacheKey);
      }
    })();

    requestCache.set(cacheKey, promise);
    return promise;
  },

  async fetchSeoData(
    url: string,
    scanDepth: ScanDepth = ScanDepth.BASIC,
    onRetryProgress?: (
      progress: number,
      retryCount: number,
      maxRetries: number
    ) => void
  ): Promise<SeoReport> {
    const cacheKey = `seo-${url}-${scanDepth}`;
    if (requestCache.has(cacheKey)) {
      return requestCache.get(cacheKey);
    }

    const promise = (async () => {
      try {
        // Get scan parameters first to determine appropriate timeout
        const params = getScanParams(url, scanDepth);

        // Create a controller for the request
        const controller = new AbortController();

        try {
          // Enhanced retry logic for all error types
          let retryCount = 0;
          const maxRetries = 5; // Increased max retries for better reliability

          while (retryCount <= maxRetries) {
            try {
              // Get token from cookies for authentication
              const cookies = document.cookie.split(";");
              const accessTokenCookie = cookies.find((cookie) =>
                cookie.trim().startsWith("access_token=")
              );
              const accessToken = accessTokenCookie
                ? accessTokenCookie.split("=")[1].trim()
                : null;

              // Make the request with consistent configuration
              const response = await scanApiClient.post(
                `/api/scan/seo`,
                params,
                {
                  signal: controller.signal,
                  timeout: params.timeout * 1000,
                  headers: accessToken
                    ? {
                        Authorization: `Bearer ${accessToken}`,
                        "Cache-Control": "no-cache",
                        Pragma: "no-cache",
                      }
                    : {
                        "Cache-Control": "no-cache",
                        Pragma: "no-cache",
                      },
                }
              );

              // Validate the response data
              if (!response.data) {
                throw new Error("Empty response received from server");
              }

              return response.data;
            } catch (requestError: any) {
              // Check specifically for backend not running (connection refused)
              if (
                requestError.message === "Network Error" ||
                requestError.code === "ERR_NETWORK" ||
                requestError.code === "ERR_CONNECTION_REFUSED" ||
                (requestError.message === "Network Error" &&
                  !requestError.response)
              ) {
                console.error(
                  "Backend service is not running - connection error"
                );
                // This is likely a case where the backend is not running
                throw new Error("BACKEND_NOT_RUNNING");
              }

              // Retry on any error except backend not running
              if (retryCount < maxRetries) {
                retryCount++;
                // Exponential backoff with jitter - wait longer for each retry
                const baseWaitTime = 1000 * Math.pow(2, retryCount);
                // Add jitter (±20%) to prevent thundering herd problem
                const jitter = baseWaitTime * 0.2 * (Math.random() - 0.5);
                const waitTime = baseWaitTime + jitter;

                console.warn(
                  `Retry ${retryCount}/${maxRetries} for ${url} after ${Math.round(
                    waitTime
                  )}ms`
                );

                // Call the progress callback if provided
                if (onRetryProgress) {
                  // Calculate a small increment based on retry count to show activity
                  const retryProgress = 50 + retryCount * 2; // 52%, 54%, 56%, 58%, 60% for retries
                  onRetryProgress(retryProgress, retryCount, maxRetries);
                }

                await new Promise((resolve) => setTimeout(resolve, waitTime));
              } else {
                // For other errors or if we've exceeded max retries, throw the error
                throw requestError;
              }
            }
          }

          // This should never be reached due to the throw in the catch block
          throw new Error("Max retries exceeded");
        } catch (innerError) {
          throw innerError;
        }
      } catch (error: any) {
        // Special case for backend not running
        if (error.message === "BACKEND_NOT_RUNNING") {
          console.error("Backend service is not running");
          return {
            success: false,
            error:
              "Le service d'analyse n'est pas disponible. Veuillez vérifier que le backend est en cours d'exécution.",
            overall_score: 0,
            categories: [],
            meta_tags: [],
            mobile_optimization: {},
            image_optimization: {},
            recommended_actions: [
              {
                title: "Vérifier le service backend",
                description:
                  "Le service d'analyse (backend) n'est pas accessible. Il est peut-être arrêté ou ne répond pas.",
                priority: 3, // Use numeric priority (3 = high)
                type: "seo", // Add required type field
                implementationDifficulty: "medium", // Add required field
                estimatedImpact: "high", // Add required field
              },
            ],
            scan_date: new Date().toISOString(),
          };
        }

        // If it's a server error (500) or a timeout/network error, return a fallback report
        if (
          error.response?.status === 500 ||
          error.response?.status === 408 ||
          axios.isCancel(error) ||
          error.code === "ECONNABORTED" ||
          error.message === "Network Error" ||
          error.message === "canceled" ||
          error.message.includes("timeout") ||
          error.message.includes("exceeded")
        ) {
          console.warn(
            `Server error or timeout in SEO scan for ${url}, returning fallback report with error: ${error.message}`
          );

          // Special case for fst.rnu.tn
          const isFstRnuSite = url.includes("fst.rnu.tn");

          let errorMessage = "";
          if (
            error.message === "canceled" ||
            error.message.includes("timeout") ||
            error.message.includes("exceeded") ||
            error.code === "ECONNABORTED" ||
            error.response?.status === 408
          ) {
            errorMessage = isFstRnuSite
              ? "Scan timed out - fst.rnu.tn is known to respond very slowly. Try a different website."
              : "Scan timed out - the server took too long to respond";
          } else {
            errorMessage = `Server error occurred during scan: ${error.message}`;
          }

          return {
            success: false,
            error: errorMessage,
            overall_score: 0,
            categories: [],
            meta_tags: [],
            mobile_optimization: {},
            image_optimization: {},
            recommended_actions: [
              {
                title: isFstRnuSite
                  ? "Try a different website"
                  : "Try again later",
                description: isFstRnuSite
                  ? "The website fst.rnu.tn is known to have performance issues that make it difficult to scan."
                  : "The server might be experiencing high load or connectivity issues.",
                priority: 3, // Use numeric priority (3 = high)
                type: "seo", // Add required type field
                implementationDifficulty: "medium", // Add required field
                estimatedImpact: "high", // Add required field
              },
            ],
            scan_date: new Date().toISOString(),
          };
        }

        console.error("Error in SEO scan:", error);
        throw error;
      } finally {
        requestCache.delete(cacheKey);
      }
    })();

    requestCache.set(cacheKey, promise);
    return promise;
  },

  async fetchImageAnalysis(url: string): Promise<ImageAnalysis> {
    const cacheKey = `images-${url}`;
    if (requestCache.has(cacheKey)) {
      return requestCache.get(cacheKey);
    }

    const promise = (async () => {
      try {
        const params = getScanParams(url);
        const response = await scanApiClient.post(`/api/scan/images`, params);
        return response.data;
      } catch (error) {
        console.error("Error in image analysis:", error);
        throw error;
      } finally {
        requestCache.delete(cacheKey);
      }
    })();

    requestCache.set(cacheKey, promise);
    return promise;
  },

  // Helper function to check if user has premium/admin privileges
  async hasAdvancedScanAccess(): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      if (!user) return false;

      // Convert role to string and normalize
      const roleStr = String(user.role || "").toLowerCase();

      // Check if user has premium or admin role
      return (
        roleStr === "admin" ||
        roleStr === "premium" ||
        roleStr === "2" ||
        roleStr === "3" ||
        roleStr === "2.0" ||
        roleStr === "3.0"
      );
    } catch (error) {
      return false;
    }
  },

  async advancedSslScan(url: string): Promise<AdvancedSslDetail> {
    // Check if user has access to advanced scans
    const hasAccess = await this.hasAdvancedScanAccess();
    if (!hasAccess) {
      // Silently return null without making the API call
      return null as unknown as AdvancedSslDetail;
    }

    try {
      const params = getScanParams(url);

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => {
          console.log(
            `Advanced SSL scan timeout triggered after ${params.timeout} seconds for ${url}`
          );
          controller.abort("Advanced SSL scan timeout exceeded");
        },
        params.timeout * 1500 // 1.5x the timeout to give axios a chance to timeout first
      );

      try {
        // Add retry logic for errors
        let retryCount = 0;
        const maxRetries = 2;

        while (retryCount <= maxRetries) {
          try {
            // Add Authorization header with token from cookies
            const cookies = document.cookie.split(";");
            const accessTokenCookie = cookies.find((cookie) =>
              cookie.trim().startsWith("access_token=")
            );
            const accessToken = accessTokenCookie
              ? accessTokenCookie.split("=")[1].trim()
              : null;

            const response = await scanApiClient.post(
              `/api/scan/advanced/ssl`,
              params,
              {
                signal: controller.signal,
                timeout: params.timeout * 1000,
                headers: accessToken
                  ? {
                      Authorization: `Bearer ${accessToken}`,
                      "Cache-Control": "no-cache",
                      Pragma: "no-cache",
                    }
                  : {
                      "Cache-Control": "no-cache",
                      Pragma: "no-cache",
                    },
              }
            );
            return ReportGenerator.mapAdvancedSslDetails(response.data);
          } catch (requestError: any) {
            // If it's a 500 error, timeout, or network error and we haven't exceeded max retries, try again
            if (
              (requestError.response?.status === 500 ||
                axios.isCancel(requestError) ||
                requestError.code === "ECONNABORTED" ||
                requestError.message === "Network Error" ||
                requestError.message === "canceled" ||
                requestError.message.includes("timeout") ||
                requestError.message.includes("exceeded")) &&
              retryCount < maxRetries
            ) {
              retryCount++;
              // Exponential backoff
              const waitTime = 1000 * Math.pow(2, retryCount);
              console.warn(
                `Retry ${retryCount}/${maxRetries} for advanced SSL scan after ${waitTime}ms`
              );
              await new Promise((resolve) => setTimeout(resolve, waitTime));
            } else {
              throw requestError;
            }
          }
        }

        throw new Error("Max retries exceeded");
      } finally {
        clearTimeout(timeoutId);
      }
    } catch (error) {
      console.error("Error in advanced SSL scan:", error);
      throw error;
    }
  },

  async advancedBrowserScan(url: string): Promise<BrowserScanDetail> {
    // Check if user has access to advanced scans
    const hasAccess = await this.hasAdvancedScanAccess();
    if (!hasAccess) {
      // Silently return null without making the API call
      return null as unknown as BrowserScanDetail;
    }

    try {
      const params = getScanParams(url);

      // Add timeout to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => {
          console.log(
            `Advanced browser scan timeout triggered after ${params.timeout} seconds for ${url}`
          );
          controller.abort("Advanced browser scan timeout exceeded");
        },
        params.timeout * 1500 // 1.5x the timeout to give axios a chance to timeout first
      );

      try {
        // Add retry logic for errors
        let retryCount = 0;
        const maxRetries = 2;

        while (retryCount <= maxRetries) {
          try {
            // Add Authorization header with token from cookies
            const cookies = document.cookie.split(";");
            const accessTokenCookie = cookies.find((cookie) =>
              cookie.trim().startsWith("access_token=")
            );
            const accessToken = accessTokenCookie
              ? accessTokenCookie.split("=")[1].trim()
              : null;

            const response = await scanApiClient.post(
              `/api/scan/advanced/browser`,
              params,
              {
                signal: controller.signal,
                timeout: params.timeout * 1000,
                headers: accessToken
                  ? {
                      Authorization: `Bearer ${accessToken}`,
                      "Cache-Control": "no-cache",
                      Pragma: "no-cache",
                    }
                  : {
                      "Cache-Control": "no-cache",
                      Pragma: "no-cache",
                    },
              }
            );
            return ReportGenerator.mapBrowserScan(response.data);
          } catch (requestError: any) {
            // If it's a 500 error, timeout, or network error and we haven't exceeded max retries, try again
            if (
              (requestError.response?.status === 500 ||
                axios.isCancel(requestError) ||
                requestError.code === "ECONNABORTED" ||
                requestError.message === "Network Error" ||
                requestError.message === "canceled" ||
                requestError.message.includes("timeout") ||
                requestError.message.includes("exceeded")) &&
              retryCount < maxRetries
            ) {
              retryCount++;
              // Exponential backoff
              const waitTime = 1000 * Math.pow(2, retryCount);
              console.warn(
                `Retry ${retryCount}/${maxRetries} for advanced browser scan after ${waitTime}ms`
              );
              await new Promise((resolve) => setTimeout(resolve, waitTime));
            } else {
              throw requestError;
            }
          }
        }

        throw new Error("Max retries exceeded");
      } finally {
        clearTimeout(timeoutId);
      }
    } catch (error) {
      console.error("Error in advanced browser scan:", error);
      throw error;
    }
  },

  async runFullScan(
    url: string,
    onProgress?: (progress: number) => void
  ): Promise<{
    security: SecurityReport;
    seo: SeoReport;
    advanced?: {
      ssl: AdvancedSslDetail;
      browser: BrowserScanDetail;
    };
  }> {
    try {
      onProgress?.(10);
      const securityReport = await this.fetchSecurityData(
        url,
        ScanDepth.COMPREHENSIVE
      );
      onProgress?.(50);
      const seoReport = await this.fetchSeoData(url, ScanDepth.COMPREHENSIVE);
      onProgress?.(100);

      let advanced = null;

      // Check if user has access to advanced scans
      const hasAccess = await this.hasAdvancedScanAccess();

      if (hasAccess) {
        try {
          const [sslData, browserData] = await Promise.all([
            this.advancedSslScan(url),
            this.advancedBrowserScan(url),
          ]);

          // Only set advanced data if we got results
          if (sslData && browserData) {
            advanced = {
              ssl: sslData,
              browser: browserData,
            };
          }
        } catch (advancedError) {
          // Only log error for non-permission related errors
          if (advancedError.response?.status !== 403) {
            console.error("Advanced scan failed:", advancedError);
          }
        }
      }

      return {
        security: securityReport,
        seo: seoReport,
        ...(advanced && { advanced }),
      };
    } catch (error) {
      console.error("Error in full scan:", error);
      throw error;
    }
  },

  // Controller for cancelling scans
  _scanAbortController: null as AbortController | null,

  // Method to cancel ongoing scans
  cancelScan: async function () {
    // First, abort any ongoing frontend requests
    if (this._scanAbortController) {
      this._scanAbortController.abort();
      this._scanAbortController = null;
      console.log("Frontend scan requests cancelled by user");
    }

    // Clear all request caches to ensure a fresh start for new scans
    requestCache.clear();

    // Then, tell the backend to cancel any ongoing scans
    try {
      // Get the current URL being scanned from localStorage
      const currentScanUrl = localStorage.getItem("currentScanUrl");

      if (currentScanUrl) {
        // Call the backend cancel endpoint
        await scanApiClient.post("/api/scan/cancel", {
          url: currentScanUrl,
        });
        console.log(`Backend scan for ${currentScanUrl} cancelled by user`);

        // Clear the current scan URL
        localStorage.removeItem("currentScanUrl");
      }

      // Clear any other scan-related data from localStorage
      localStorage.removeItem("scanInProgress");
      localStorage.removeItem("scanStartTime");

      return true;
    } catch (error) {
      console.error("Error cancelling scan on backend:", error);

      // Even if the backend call fails, clear local storage items
      localStorage.removeItem("currentScanUrl");
      localStorage.removeItem("scanInProgress");
      localStorage.removeItem("scanStartTime");

      return true; // Still return true as we've cancelled the frontend requests
    }
  },

  // Batch API method to perform all scans with a single token refresh
  async batchScanUrl(
    url: string,
    onProgress?: (progress: number) => void
  ): Promise<{
    security: SecurityReport | null;
    seo: SeoReport | null;
    advancedSsl: AdvancedSslDetail | null;
    advancedBrowser: BrowserScanDetail | null;
    errors: string[];
  }> {
    // Store the current URL being scanned in localStorage for cancellation
    localStorage.setItem("currentScanUrl", url);

    // Create a new abort controller for this scan
    this._scanAbortController = new AbortController();
    // Get a token once for all requests - only if needed
    await this.refreshToken(); // Token not used directly but refreshes auth state

    // Check if user has access to advanced scans to adjust progress percentages
    const isPremiumOrAdmin = await this.hasAdvancedScanAccess();

    const errors: string[] = [];
    const results = {
      security: null as SecurityReport | null,
      seo: null as SeoReport | null,
      advancedSsl: null as AdvancedSslDetail | null,
      advancedBrowser: null as BrowserScanDetail | null,
      errors,
    };

    // Initial progress
    console.log(
      `Batch scan started for ${url}, isPremiumOrAdmin: ${isPremiumOrAdmin}`
    );
    onProgress?.(10);
    console.log(`Progress updated: 10%`);

    // Security scan
    try {
      console.log(`Starting security scan...`);

      // Create a function to handle retry progress updates
      const handleRetryProgress = (
        retryProgress: number,
        retryCount: number,
        maxRetries: number
      ) => {
        // Calculate a small increment based on retry count to show activity
        // This creates a pulsing effect during retries (e.g., 12%, 14%, 16%)
        console.log(
          `Security scan retry ${retryCount}/${maxRetries}, updating progress to ${retryProgress}%`
        );
        onProgress?.(retryProgress);
      };

      // Pass the abort signal and retry progress handler to the security scan
      try {
        results.security = await this.fetchSecurityData(
          url,
          undefined,
          this._scanAbortController?.signal,
          handleRetryProgress
        );
      } catch (error) {
        console.error("Security scan failed with error:", error);
        // If the security scan fails, we'll still try the SEO scan
        // The fetchSecurityData function should have returned a fallback report
        // but just in case it didn't, we'll check if results.security exists
        if (!results.security) {
          console.warn("No security results returned, using fallback report");
          // Create a comprehensive fallback report with all required fields
          results.security = {
            success: false,
            error: "Security scan failed - the server took too long to respond",
            overall_score: 0,
            categories: [
              {
                name: "SSL/TLS",
                score: 0,
                details: [
                  {
                    message: "Could not analyze SSL/TLS configuration",
                    status: "warning",
                    recommendation: "Try scanning again later",
                  },
                ],
              },
              {
                name: "HTTP Headers",
                score: 0,
                details: [
                  {
                    message: "Could not analyze HTTP headers",
                    status: "warning",
                    recommendation: "Try scanning again later",
                  },
                ],
              },
              {
                name: "Vulnerabilities",
                score: 0,
                details: [
                  {
                    message: "Could not analyze for vulnerabilities",
                    status: "warning",
                    recommendation: "Try scanning again later",
                  },
                ],
              },
            ],
            ssl_details: {
              certificateType: "Unknown",
              expirationDate: "Unknown",
              issuer: "Unknown",
              cipherStrength: "0",
              forwardSecrecy: false,
              hstsEnabled: false,
              ocspStapling: false,
              vulnerabilities: [],
              supportsTls13: false,
              severity: "Unknown",
            },
            http_headers: [],
            vulnerabilities: [],
            recommended_actions: [
              {
                title: "Try again later",
                description:
                  "The server might be experiencing high load or connectivity issues.",
                priority: 3,
                type: "security",
                implementationDifficulty: "medium",
                estimatedImpact: "high",
              },
              {
                title: "Check website availability",
                description:
                  "Ensure the website is accessible before scanning.",
                priority: 2,
                type: "security",
                implementationDifficulty: "easy",
                estimatedImpact: "medium",
              },
            ],
            scan_date: new Date().toISOString(),
          } as any as SecurityReport;
        }
      }

      // Check if we got a backend not running error
      if (
        results.security &&
        !results.security.success &&
        results.security.error &&
        results.security.error.includes(
          "Le service d'analyse n'est pas disponible"
        )
      ) {
        // This is a backend not running error, add it to errors and return early
        console.error("Backend service is not running");
        errors.push(
          "Le service d'analyse n'est pas disponible. Veuillez vérifier que le backend est en cours d'exécution."
        );
        onProgress?.(100); // Set progress to 100% to complete the loading
        return results;
      }

      // Different progress percentage based on user type
      const securityProgress = isPremiumOrAdmin ? 40 : 50;
      console.log(
        `Security scan completed, updating progress to ${securityProgress}%`
      );
      onProgress?.(securityProgress);
    } catch (error: any) {
      // Check if the scan was cancelled
      if (axios.isCancel(error)) {
        console.log(`Security scan cancelled by user`);
        errors.push("Security scan cancelled by user");
        onProgress?.(isPremiumOrAdmin ? 40 : 50);
        return results;
      }

      // Check for backend not running error
      if (
        error.message === "BACKEND_NOT_RUNNING" ||
        error.message === "Network Error" ||
        error.code === "ERR_NETWORK" ||
        error.code === "ERR_CONNECTION_REFUSED" ||
        (error.message && error.message.includes("net::ERR_CONNECTION_REFUSED"))
      ) {
        console.error("Backend service is not running");
        errors.push(
          "Le service d'analyse n'est pas disponible. Veuillez vérifier que le backend est en cours d'exécution."
        );
        onProgress?.(100); // Set progress to 100% to complete the loading

        // Create a special error object that will be detected in Report.tsx
        results.security = {
          success: false,
          error: "BACKEND_NOT_RUNNING",
          overall_score: 0,
          categories: [],
          ssl_details: {
            certificateType: "Unknown",
            expirationDate: "Unknown",
            issuer: "Unknown",
            cipherStrength: "0",
            forwardSecrecy: false,
            hstsEnabled: false,
            ocspStapling: false,
            vulnerabilities: [],
            supportsTls13: false,
            severity: "Unknown",
          },
          http_headers: [],
          vulnerabilities: [],
          recommended_actions: [],
          scan_date: new Date().toISOString(),
        };

        return results;
      }

      // Only add detailed error for non-auth errors
      if (error.response?.status !== 401 && error.response?.status !== 403) {
        console.error(
          `Security scan error: ${error.message || "Unknown error"}`
        );
        errors.push(`Security scan error: ${error.message || "Unknown error"}`);
      } else {
        console.warn(`Security scan requires authentication`);
        errors.push("Security scan requires authentication");
      }
      onProgress?.(isPremiumOrAdmin ? 40 : 50);
    }

    // SEO scan
    try {
      console.log(`Starting SEO scan...`);
      // Create a function to handle retry progress updates for SEO scan
      const handleSeoRetryProgress = (
        retryProgress: number,
        retryCount: number,
        maxRetries: number
      ) => {
        console.log(
          `SEO scan retry ${retryCount}/${maxRetries}, updating progress to ${retryProgress}%`
        );
        onProgress?.(retryProgress);
      };

      // Pass the retry progress handler to the SEO scan
      try {
        results.seo = await this.fetchSeoData(
          url,
          undefined,
          handleSeoRetryProgress
        );
      } catch (error) {
        console.error("SEO scan failed with error:", error);
        // If the SEO scan fails, we'll still try to return what we have
        // The fetchSeoData function should have returned a fallback report
        // but just in case it didn't, we'll check if results.seo exists
        if (!results.seo) {
          console.warn("No SEO results returned, using fallback report");
          // Create a comprehensive fallback report with all required fields
          results.seo = {
            success: false,
            error: "SEO scan failed - the server took too long to respond",
            overall_score: 0,
            categories: [
              {
                name: "Meta Tags",
                score: 0,
                details: [
                  {
                    message: "Could not analyze meta tags",
                    status: "warning",
                    recommendation: "Try scanning again later",
                  },
                ],
              },
              {
                name: "Mobile Optimization",
                score: 0,
                details: [
                  {
                    message: "Could not analyze mobile optimization",
                    status: "warning",
                    recommendation: "Try scanning again later",
                  },
                ],
              },
              {
                name: "Performance",
                score: 0,
                details: [
                  {
                    message: "Could not analyze performance metrics",
                    status: "warning",
                    recommendation: "Try scanning again later",
                  },
                ],
              },
            ],
            meta_tags: [],
            mobile_optimization: {
              responsiveDesign: false,
              viewportConfiguration: false,
              touchTargets: false,
              fontSizes: false,
              mobileUsability: false,
            },
            performance_metrics: {
              loadTime: 0,
              firstContentfulPaint: 0,
              speedIndex: 0,
              timeToInteractive: 0,
              score: 0,
            },
            url_structure: {
              hasCleanUrls: false,
              hasBreadcrumbs: false,
              hasCanonicalTags: false,
              score: 0,
            },
            content_analysis: {
              hasHeadings: false,
              keywordDensity: 0,
              readabilityScore: 0,
              score: 0,
            },
            image_analysis: {
              imagesWithAltText: 0,
              totalImages: 0,
              imagesWithOptimizedSize: 0,
              score: 0,
            },
            recommended_actions: [
              {
                title: "Try again later",
                description:
                  "The server might be experiencing high load or connectivity issues.",
                priority: 3,
                type: "seo",
                implementationDifficulty: "medium",
                estimatedImpact: "high",
              },
              {
                title: "Check website availability",
                description:
                  "Ensure the website is accessible before scanning.",
                priority: 2,
                type: "seo",
                implementationDifficulty: "easy",
                estimatedImpact: "medium",
              },
            ],
            scan_date: new Date().toISOString(),
          } as any as SeoReport;
        }
      }

      // Check if we got a backend not running error
      if (
        results.seo &&
        !results.seo.success &&
        results.seo.error &&
        results.seo.error.includes("Le service d'analyse n'est pas disponible")
      ) {
        // This is a backend not running error, add it to errors and return early
        console.error("Backend service is not running");
        errors.push(
          "Le service d'analyse n'est pas disponible. Veuillez vérifier que le backend est en cours d'exécution."
        );
        onProgress?.(100); // Set progress to 100% to complete the loading
        return results;
      }

      // Different progress percentage based on user type
      // For free users, this is the final step (100%)
      const seoProgress = isPremiumOrAdmin ? 70 : 100;
      console.log(`SEO scan completed, updating progress to ${seoProgress}%`);
      onProgress?.(seoProgress);
    } catch (error: any) {
      // Check if the scan was cancelled
      if (axios.isCancel(error)) {
        console.log(`SEO scan cancelled by user`);
        errors.push("SEO scan cancelled by user");
        onProgress?.(isPremiumOrAdmin ? 70 : 100);
        return results;
      }

      // Check for backend not running error
      if (
        error.message === "BACKEND_NOT_RUNNING" ||
        error.message === "Network Error" ||
        error.code === "ERR_NETWORK" ||
        error.code === "ERR_CONNECTION_REFUSED" ||
        (error.message && error.message.includes("net::ERR_CONNECTION_REFUSED"))
      ) {
        console.error("Backend service is not running");
        errors.push(
          "Le service d'analyse n'est pas disponible. Veuillez vérifier que le backend est en cours d'exécution."
        );
        onProgress?.(100); // Set progress to 100% to complete the loading

        // Create a special error object that will be detected in Report.tsx
        if (!results.security) {
          results.security = {
            success: false,
            error: "BACKEND_NOT_RUNNING",
            overall_score: 0,
            categories: [],
            ssl_details: {
              certificateType: "Unknown",
              expirationDate: "Unknown",
              issuer: "Unknown",
              cipherStrength: "0",
              forwardSecrecy: false,
              hstsEnabled: false,
              ocspStapling: false,
              vulnerabilities: [],
              supportsTls13: false,
              severity: "Unknown",
            },
            http_headers: [],
            vulnerabilities: [],
            recommended_actions: [],
            scan_date: new Date().toISOString(),
          };
        }

        return results;
      }

      // Only add detailed error for non-auth errors
      if (error.response?.status !== 401 && error.response?.status !== 403) {
        console.error(`SEO scan error: ${error.message || "Unknown error"}`);
        errors.push(`SEO scan error: ${error.message || "Unknown error"}`);
      } else {
        console.warn(`SEO scan requires authentication`);
        errors.push("SEO scan requires authentication");
      }
      onProgress?.(isPremiumOrAdmin ? 70 : 100);
    }

    // Advanced scans for premium/admin users only
    if (isPremiumOrAdmin) {
      try {
        console.log(`Starting advanced scans for premium/admin user...`);
        // Run advanced scans in parallel only for premium/admin users
        const [sslResult, browserResult] = await Promise.all([
          this.advancedSslScan(url).catch((error: any) => {
            // Only log non-auth errors
            if (
              error.response?.status !== 401 &&
              error.response?.status !== 403
            ) {
              console.error(
                `Advanced SSL scan error: ${error.message || "Unknown error"}`
              );
              errors.push(
                `Advanced SSL scan error: ${error.message || "Unknown error"}`
              );
            }
            return null;
          }),
          this.advancedBrowserScan(url).catch((error: any) => {
            // Only log non-auth errors
            if (
              error.response?.status !== 401 &&
              error.response?.status !== 403
            ) {
              console.error(
                `Advanced browser scan error: ${
                  error.message || "Unknown error"
                }`
              );
              errors.push(
                `Advanced browser scan error: ${
                  error.message || "Unknown error"
                }`
              );
            }
            return null;
          }),
        ]);

        results.advancedSsl = sslResult;
        results.advancedBrowser = browserResult;

        // Final progress for premium/admin users
        console.log(`Advanced scans completed, updating progress to 100%`);
        onProgress?.(100);
      } catch (error: any) {
        // Only log warning for non-permission related errors
        if (error.response?.status !== 401 && error.response?.status !== 403) {
          console.error(
            `Advanced scans error: ${error.message || "Unknown error"}`
          );
          errors.push(
            `Advanced scans error: ${error.message || "Unknown error"}`
          );
        }
        onProgress?.(100);
      }
    } else {
      // For free users, silently set advanced scan results to null without logging warnings
      console.log(`Free user - skipping advanced scans`);
      results.advancedSsl = null;
      results.advancedBrowser = null;
    }

    // Clean up after scan completion
    console.log(`Scan completed, cleaning up resources...`);
    localStorage.removeItem("currentScanUrl");
    this._scanAbortController = null;

    console.log(
      `Returning scan results with security: ${!!results.security}, seo: ${!!results.seo}, advancedSsl: ${!!results.advancedSsl}, advancedBrowser: ${!!results.advancedBrowser}`
    );
    return results;
  },

  // Cache for current user data
  _currentUserCache: {
    data: null as {
      id: string;
      email: string;
      username?: string;
      role: string;
    } | null,
    timestamp: 0,
    expiryTime: 60000, // 1 minute cache validity
    fetchPromise: null as Promise<{
      id: string;
      email: string;
      username?: string;
      role: string;
    } | null> | null,
  },

  async getCurrentUser(): Promise<{
    id: string;
    email: string;
    username?: string;
    role: string;
  } | null> {
    // Check if we have a valid cached user
    const cache = this._currentUserCache;
    const now = Date.now();

    // Return cached data if it's still valid
    if (cache.data && now - cache.timestamp < cache.expiryTime) {
      return cache.data;
    }

    // If there's already a fetch in progress, return that promise
    if (cache.fetchPromise) {
      return cache.fetchPromise;
    }

    // Create a new fetch promise
    cache.fetchPromise = (async () => {
      try {
        // Use a simple timeout to prevent hanging
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        try {
          // Get the access token from cookies if available
          const cookies = document.cookie.split(";");
          const accessTokenCookie = cookies.find((cookie) =>
            cookie.trim().startsWith("access_token=")
          );
          const accessToken = accessTokenCookie
            ? accessTokenCookie.split("=")[1].trim()
            : null;

          // Make the request with credentials to include cookies and add Authorization header if we have a token
          const response = await axios.get(`${AUTH_API_BASE_URL}/api/auth/me`, {
            withCredentials: true,
            signal: controller.signal,
            headers: accessToken
              ? { Authorization: `Bearer ${accessToken}` }
              : undefined,
          });

          // Update cache
          cache.data = response.data;
          cache.timestamp = now;
          return response.data;
        } catch (error: any) {
          // Check if it's an abort error
          if (axios.isCancel(error)) {
            return cache.data; // Return previous cached data if available
          }

          // If we get a 401 Unauthorized, try to refresh the token
          if (error.response?.status === 401) {
            try {
              // Try to refresh the token
              const newToken = await this.refreshToken();

              if (!newToken) {
                return null;
              }

              // Try again after token refresh with a new timeout
              const retryController = new AbortController();
              const retryTimeoutId = setTimeout(
                () => retryController.abort(),
                5000
              );

              try {
                // Store the token in a cookie to ensure it's available for API requests
                if (newToken) {
                  document.cookie = `access_token=${newToken}; path=/; secure; samesite=strict`;
                }

                // Try again after token refresh
                const retryResponse = await axios.get(
                  `${AUTH_API_BASE_URL}/api/auth/me`,
                  {
                    withCredentials: true,
                    signal: retryController.signal,
                    headers: {
                      Authorization: `Bearer ${newToken}`,
                    },
                  }
                );

                // Update cache
                cache.data = retryResponse.data;
                cache.timestamp = now;
                return retryResponse.data;
              } finally {
                clearTimeout(retryTimeoutId);
              }
            } catch (refreshError) {
              // If refresh fails, user is not authenticated
              cache.data = null;
              return null;
            }
          }

          // For other errors, return null
          cache.data = null;
          return null;
        } finally {
          clearTimeout(timeoutId);
        }
      } finally {
        cache.fetchPromise = null;
      }
    })();

    return cache.fetchPromise;
  },

  async logout(): Promise<void> {
    try {
      // Call the logout endpoint which will clear the cookies on the server side
      await axios.post(
        `${AUTH_API_BASE_URL}/api/auth/logout`,
        {},
        {
          withCredentials: true,
        }
      );
      // No need to manually remove tokens as they're handled by cookies
      // Just clear any local storage items used for UI state
      await safeStorage.removeItem("hasLoggedIn");
    } catch (error) {
      console.error("Logout failed:", error);
      throw error;
    }
  },

  // Centralized token management - using a regular object instead of static property
  _tokenManager: {
    cachedToken: null as string | null,
    tokenExpiryTime: 0,
    lastRefreshAttempt: 0,
    refreshCooldownPeriod: 10000, // 10 seconds cooldown
    refreshFailureCount: 0,
    maxConsecutiveFailures: 3,
    refreshInProgress: false,
    refreshPromise: null as Promise<string | null> | null,

    // Token refresh status for UI feedback
    status: {
      lastRefreshTime: 0,
      lastRefreshSuccess: false,
      lastRefreshError: null as Error | null,
    },
  },

  // Getter for token manager to ensure it's initialized
  get tokenManager() {
    return this._tokenManager;
  },

  async refreshToken(): Promise<string | null> {
    const now = Date.now();
    const tm = this.tokenManager;

    // Check if we have a cached token that's still valid (with 30 second buffer)
    if (tm.cachedToken && now < tm.tokenExpiryTime - 30000) {
      return tm.cachedToken;
    }

    // If a refresh is already in progress, return the existing promise
    // This prevents multiple simultaneous refresh attempts
    if (tm.refreshInProgress && tm.refreshPromise) {
      return tm.refreshPromise;
    }

    // Check if we're in a cooldown period after failures
    if (tm.refreshFailureCount >= tm.maxConsecutiveFailures) {
      const timeSinceLastAttempt = now - tm.lastRefreshAttempt;
      const cooldownMultiplier = Math.min(
        tm.refreshFailureCount - tm.maxConsecutiveFailures + 1,
        5
      );
      const currentCooldown = tm.refreshCooldownPeriod * cooldownMultiplier;

      if (timeSinceLastAttempt < currentCooldown) {
        // Return the expired token as a fallback without logging
        return tm.cachedToken;
      }
    }

    // Set refresh in progress flag and create a new promise
    tm.refreshInProgress = true;
    tm.lastRefreshAttempt = now;

    // Create a new refresh promise
    tm.refreshPromise = (async () => {
      try {
        // Check if we have admin verification status
        const isAdminVerified = await safeStorage.getItem("isAdminVerified");

        // Use a simple timeout to prevent hanging
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        try {
          // Make the refresh token request with credentials to include cookies
          const response = await axios.post(
            `${AUTH_API_BASE_URL}/api/auth/refresh-token`,
            {}, // Empty body for cookie-based auth
            {
              withCredentials: true, // Include credentials to send cookies
              signal: controller.signal,
              // Add retry and timeout options
              timeout: 5000,
              maxRedirects: 0, // Prevent redirects
            }
          );

          // With cookie-based auth, we don't need to manually store the token
          // The server sets the cookies automatically
          // But we can return the token for use in the current session
          if (response.data?.accessToken) {
            // Reset failure count on success
            tm.refreshFailureCount = 0;

            // Cache the token with an expiry time (15 minutes)
            tm.cachedToken = response.data.accessToken;
            tm.tokenExpiryTime = now + 15 * 60 * 1000;

            // Update status
            tm.status.lastRefreshTime = now;
            tm.status.lastRefreshSuccess = true;
            tm.status.lastRefreshError = null;

            // Store the token in a cookie to ensure it's available for API requests
            document.cookie = `access_token=${response.data.accessToken}; path=/; secure; samesite=strict`;

            // If we're an admin and verified, make sure we keep that state
            if (isAdminVerified === "true") {
              await safeStorage.setItem("hasLoggedIn", "true");
            }

            return response.data.accessToken;
          }

          // Increment failure count for empty response
          tm.refreshFailureCount++;
          tm.status.lastRefreshSuccess = false;
          return tm.cachedToken; // Return the expired token as a fallback
        } finally {
          clearTimeout(timeoutId);
        }
      } catch (error) {
        // Increment failure count
        tm.refreshFailureCount++;

        // Update status
        tm.status.lastRefreshTime = now;
        tm.status.lastRefreshSuccess = false;
        tm.status.lastRefreshError = error as Error;

        // Check if it's an abort error
        if (axios.isCancel(error)) {
          return tm.cachedToken; // Return the expired token as a fallback without logging
        }

        // Only log error if it's not a 401/403 response (expected for unauthenticated users)
        const status = (error as any).response?.status;
        if (status !== 401 && status !== 403) {
          console.error("Refresh token failed with unexpected error");
        }

        // Check if we need to redirect to login
        const isAdminVerified = await safeStorage.getItem("isAdminVerified");
        const isAdminLogin = await safeStorage.getItem("isAdminLogin");

        // If we're in admin flow but not verified, clear admin login state
        if (isAdminLogin === "true" && isAdminVerified !== "true") {
          await safeStorage.removeItem("isAdminLogin");
          await safeStorage.removeItem("adminEmail");
          await safeStorage.removeItem("adminPassword");
        }

        // Don't throw the error, just return the expired token as a fallback
        return tm.cachedToken;
      } finally {
        // Reset the in-progress flag
        tm.refreshInProgress = false;
        tm.refreshPromise = null;
      }
    })();

    // Return the promise
    return tm.refreshPromise;
  },
};
