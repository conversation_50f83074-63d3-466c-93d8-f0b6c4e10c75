namespace SiteCheckerApp.DTOs
{
    public class LoginResponse
    {
        public string Token { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public bool RequiresTwoFactor { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserRole Role { get; set; }
        public Guid UserId { get; set; }
        public string Email { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
    }

    public enum UserRole
    {
        User = 1,
        PremiumUser = 2,
        Admin = 3
    }
}
