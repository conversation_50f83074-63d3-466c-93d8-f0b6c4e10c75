using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using SiteCheckerApp.DTOs;
using SiteCheckerApp.Models;
using SiteCheckerApp.Services;
using System.Security.Claims;

namespace SiteCheckerApp.Controllers
{
    [EnableCors("AllowFrontend")]
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin,3")] // Accept both string "Admin" and numeric "3" roles
    public class AdminController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<AdminController> _logger;

        public AdminController(
            IUserService userService,
            ILogger<AdminController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        [HttpGet("users")]
        public async Task<ActionResult<List<UserInfoResponse>>> GetAllUsers()
        {
            try
            {
                var users = await _userService.GetAllUsersAsync();

                var userResponses = users.Select(user => new UserInfoResponse
                {
                    Id = user.Id,
                    Email = user.Email,
                    Username = user.Username,
                    Role = user.IdRole,
                    IsVerified = user.IsVerified,
                    IsActive = user.LockoutEnd == null || user.LockoutEnd < DateTime.UtcNow,
                    CreatedAt = user.CreatedAt
                }).ToList();

                return Ok(userResponses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all users");
                return StatusCode(500, new { message = "An error occurred while retrieving users." });
            }
        }

        [HttpPut("users/{id}/role")]
        public async Task<IActionResult> UpdateUserRole(string id, [FromBody] UpdateUserRoleRequest request)
        {
            try
            {
                // Prevent changing admin roles
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { message = "User not found." });
                }

                // Don't allow changing admin roles
                if (user.IdRole == 3)
                {
                    return BadRequest(new { message = "Cannot change role of admin users." });
                }

                // Only allow changing between free (1) and premium (2) roles
                if (request.Role != 1 && request.Role != 2)
                {
                    return BadRequest(new { message = "Invalid role. Only roles 1 (free) and 2 (premium) are allowed." });
                }

                var success = await _userService.UpdateUserRoleAsync(id, request.Role);
                if (!success)
                {
                    return BadRequest(new { message = "Failed to update user role." });
                }

                return Ok(new { message = "User role updated successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating role for user with ID {UserId}", id);
                return StatusCode(500, new { message = "An error occurred while updating the user role." });
            }
        }

        [HttpPut("users/{id}/status")]
        public async Task<IActionResult> UpdateUserStatus(string id, [FromBody] UpdateUserStatusRequest request)
        {
            try
            {
                // Prevent changing admin status
                var user = await _userService.GetUserByIdAsync(id);
                if (user == null)
                {
                    return NotFound(new { message = "User not found." });
                }

                // Don't allow changing admin status
                if (user.IdRole == 3)
                {
                    return BadRequest(new { message = "Cannot change status of admin users." });
                }

                // Get the current admin's ID
                var adminId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(adminId))
                {
                    return Unauthorized(new { message = "Admin ID not found in token." });
                }

                // Don't allow admins to change their own status
                if (id == adminId)
                {
                    return BadRequest(new { message = "Admins cannot change their own status." });
                }

                var success = await _userService.UpdateUserStatusAsync(id, request.IsActive);
                if (!success)
                {
                    return BadRequest(new { message = "Failed to update user status." });
                }

                return Ok(new { message = "User status updated successfully." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating status for user with ID {UserId}", id);
                return StatusCode(500, new { message = "An error occurred while updating the user status." });
            }
        }

        [HttpGet("stats")]
        public async Task<ActionResult<PlatformStatsResponse>> GetPlatformStats()
        {
            try
            {
                var stats = await _userService.GetPlatformStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving platform statistics");
                return StatusCode(500, new { message = "An error occurred while retrieving platform statistics." });
            }
        }
    }
}
