/* Modern sidebar styles */

/* Base sidebar styles */
#report-sidebar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  bottom: 0 !important;
  height: 100vh !important;
  z-index: 1000 !important; /* Increased z-index to ensure it's above all content */
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 5px 25px rgba(37, 99, 235, 0.15) !important;
  will-change: transform !important;
  transform: translateZ(0) !important;
  -webkit-backface-visibility: hidden !important;
  backface-visibility: hidden !important;
  isolation: isolate !important;
  overflow: hidden !important;
  backdrop-filter: blur(12px) !important;
  -webkit-backdrop-filter: blur(12px) !important;
  border-right: 1px solid rgba(219, 234, 254, 0.3) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform: translateX(0) !important; /* Ensure it's always visible */
  position: fixed !important; /* Double declaration to ensure it overrides any other styles */
  overflow-y: hidden !important; /* Prevent the sidebar itself from scrolling */
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.97),
    rgba(243, 244, 246, 0.97)
  ) !important;
  /* Ensure the sidebar is fixed for all screen sizes */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  height: 100% !important;
}

/* Ensure the sidebar has proper width */
#report-sidebar.w-64 {
  width: 16rem !important;
}

#report-sidebar.w-16 {
  width: 4rem !important;
}

/* Ensure the sidebar is not printed */
@media print {
  #report-sidebar {
    display: none !important;
  }
}

/* Ensure the sidebar header and footer are sticky */
#report-sidebar > div > div:first-child {
  position: sticky !important;
  top: 0 !important;
  z-index: 30 !important;
  width: 100% !important;
}

#report-sidebar > div > div:last-child {
  position: sticky !important;
  bottom: 0 !important;
  z-index: 30 !important;
  width: 100% !important;
}

/* Ensure the sidebar content is scrollable */
#report-sidebar > div > div.overflow-y-auto {
  overflow-y: auto !important;
  -webkit-overflow-scrolling: touch !important;
  flex: 1 1 auto !important;
  min-height: 0 !important;
  max-height: calc(100vh - 100px) !important;
  height: auto !important;
  position: relative !important;
  z-index: 20 !important;
  overscroll-behavior: contain !important;
}

/* Custom scrollbar styling */
#report-sidebar .scrollbar-thin::-webkit-scrollbar {
  width: 4px !important;
}

#report-sidebar .scrollbar-thin::-webkit-scrollbar-track {
  background: transparent !important;
}

#report-sidebar .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3) !important;
  border-radius: 20px !important;
  transition: background-color 0.3s ease !important;
}

#report-sidebar .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(99, 102, 241, 0.6) !important;
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.4) !important;
}

/* Ensure the main content has proper margin and scrolling behavior */
.report-content {
  margin-left: var(--sidebar-width, 16rem) !important;
  transition: margin-left 0.3s ease !important;
  position: relative !important;
  min-height: 100vh !important;
  width: calc(100% - var(--sidebar-width, 16rem)) !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* Apply specific styles only when in report page */
.in-report-page .report-content {
  height: 100vh !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent !important;
}

.in-report-page .report-content::-webkit-scrollbar {
  width: 6px !important;
}

.in-report-page .report-content::-webkit-scrollbar-track {
  background: transparent !important;
}

.in-report-page .report-content::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3) !important;
  border-radius: 20px !important;
  transition: background-color 0.3s ease !important;
}

.in-report-page .report-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(99, 102, 241, 0.6) !important;
  box-shadow: 0 0 8px rgba(99, 102, 241, 0.4) !important;
}

.sidebar-collapsed .report-content {
  margin-left: 4rem !important;
  width: calc(100% - 4rem) !important;
}

/* Add a class to the body to ensure proper stacking context */
body.has-report-sidebar {
  position: relative !important; /* Create a new stacking context */
  width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Only apply overflow hidden when in report page */
body.has-report-sidebar.in-report-page {
  overflow: hidden !important; /* Prevent scrolling on body */
  height: 100vh !important;
}

/* Specific styling for the report page container */
.report-page-container {
  height: 100vh !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure html has proper height */
html {
  height: 100% !important;
  overflow-x: hidden !important;
}

/* Ensure smooth transitions */
.transition-all {
  transition-property: all !important;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important;
  transition-duration: 300ms !important;
}

/* Modern navigation item styles */
#report-sidebar a,
#report-sidebar button {
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-radius: 0.5rem !important;
}

#report-sidebar a:hover,
#report-sidebar button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1) !important;
}

#report-sidebar a:active,
#report-sidebar button:active {
  transform: translateY(1px) !important;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1) !important;
}

/* Add subtle hover animation */
#report-sidebar a::before,
#report-sidebar button::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: radial-gradient(
    circle at center,
    rgba(59, 130, 246, 0.1) 0%,
    transparent 70%
  ) !important;
  opacity: 0 !important;
  transition: opacity 0.5s ease !important;
  pointer-events: none !important;
  z-index: -1 !important;
}

#report-sidebar a:hover::before,
#report-sidebar button:hover::before {
  opacity: 1 !important;
}

/* Download section styles */
#report-sidebar .neo-card {
  background: linear-gradient(
    135deg,
    rgba(249, 250, 251, 0.9),
    rgba(236, 253, 255, 0.9)
  ) !important;
  border: 1px solid rgba(186, 230, 253, 0.4) !important;
  box-shadow: 0 4px 12px rgba(8, 145, 178, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
}

#report-sidebar .neo-card:hover {
  box-shadow: 0 8px 16px rgba(8, 145, 178, 0.15) !important;
  transform: translateY(-2px) !important;
}

#report-sidebar .bg-gradient-to-r {
  background-size: 200% auto !important;
  transition: all 0.5s ease !important;
}

#report-sidebar .bg-gradient-to-r:hover {
  background-position: right center !important;
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.3) !important;
}

/* Active item glow effect */
#report-sidebar a[data-active="true"],
#report-sidebar button[data-active="true"] {
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.2) !important;
  transform: translateZ(0) !important;
}

/* Modern tooltip styles for collapsed sidebar */
.sidebar-tooltip {
  position: absolute !important;
  left: 120% !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.95),
    rgba(79, 70, 229, 0.95)
  ) !important;
  color: white !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 0.5rem !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  white-space: nowrap !important;
  z-index: 1010 !important; /* Higher than sidebar to ensure visibility */
  opacity: 0 !important;
  pointer-events: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.25) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-50%) translateX(-10px) !important;
}

.sidebar-tooltip::before {
  content: "" !important;
  position: absolute !important;
  left: -6px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 0 !important;
  height: 0 !important;
  border-top: 6px solid transparent !important;
  border-bottom: 6px solid transparent !important;
  border-right: 6px solid rgba(37, 99, 235, 0.95) !important;
}

#report-sidebar.w-16 a:hover .sidebar-tooltip,
#report-sidebar.w-16 button:hover .sidebar-tooltip {
  opacity: 1 !important;
  transform: translateY(-50%) translateX(0) !important;
}

/* Enhanced header and footer styles */
#report-sidebar > div > div:first-child,
#report-sidebar > div > div:last-child {
  background-size: 200% 200% !important;
  animation: gradientAnimation 15s ease infinite !important;
}

@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
