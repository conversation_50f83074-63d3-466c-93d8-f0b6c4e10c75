import React from 'react';
import { StatusType } from '../types/report';

interface StatusPillProps {
  status: StatusType;
  id?: string; // Optional unique identifier for the pill
}

const statusConfig = {
  success: {
    color: 'bg-green-100 text-green-800',
    label: 'Success'
  },
  warning: {
    color: 'bg-yellow-100 text-yellow-800',
    label: 'Warning'
  },
  error: {
    color: 'bg-red-100 text-red-800',
    label: 'Error'
  },
  unknown: {
    color: 'bg-gray-100 text-gray-800',
    label: 'Unknown'
  }
} as const;

// Type guard to check if a string is a valid status
const isValidStatus = (status: string | undefined): status is keyof typeof statusConfig => {
  return status !== undefined && status in statusConfig;
};

export const StatusPill: React.FC<StatusPillProps> = ({ status, id }) => {
  // Validate status and fallback to 'unknown' if invalid
  const validStatus = isValidStatus(status) ? status : 'unknown';
  const config = statusConfig[validStatus];
  
  return (
    <span 
      key={id || validStatus} // Use provided id or status as key
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
    >
      {config.label}
    </span>
  );
}; 