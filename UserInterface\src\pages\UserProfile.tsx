import { useState, useEffect } from "react";
import { useNavigate, useLocation, Link } from "react-router-dom";
import { Toaster } from "sonner";
import { toast } from "sonner";
import {
  User,
  KeyRound,
  CreditCard,
  Settings,
  Save,
  AlertCircle,
  CheckCircle,
  Loader2,
} from "lucide-react";
import Card from "../components/ui-custom/Card";
import Button from "../components/ui-custom/Button";
import Input from "../components/ui-custom/Input";
import Layout from "../components/Layout";
import { apiService } from "@/services/apiService";
import { PasswordCriteriaList } from "../components/PasswordCriteriaList";

interface UserInfo {
  id: string;
  email: string;
  username?: string;
  role: string | number;
  billingCycle?: string;
  plan?: string;
  createdAt?: string;
}

export default function UserProfile() {
  const [activeTab, setActiveTab] = useState("profile");
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [passwordSaving, setPasswordSaving] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // Form states
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");

  // Password change states
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [passwordSuccess, setPasswordSuccess] = useState("");

  // Password validation states
  const [passwordValid, setPasswordValid] = useState({
    length: false,
    uppercase: false,
    lowercase: false,
    number: false,
    special: false,
    match: false,
  });

  useEffect(() => {
    const loadUserData = async () => {
      try {
        // Add a small delay to allow token refresh if needed
        await new Promise((resolve) => setTimeout(resolve, 100));

        const user = await apiService.getCurrentUser();
        if (!user) {
          // Try one more time after a short delay
          await new Promise((resolve) => setTimeout(resolve, 500));
          const retryUser = await apiService.getCurrentUser();

          if (!retryUser) {
            toast.error("Vous devez être connecté pour accéder à cette page");
            navigate("/login");
            return;
          }

          // Use retry result
          setUserInfo(retryUser);
          setEmail(retryUser.email || "");
          setUsername(retryUser.username || "");
        } else {
          // Redirect admin users to the admin dashboard
          const userRole = String(user.role).toLowerCase();
          if (userRole === "3" || userRole === "admin") {
            console.log(
              "Admin user detected in profile page. Redirecting to admin dashboard."
            );
            toast.info(
              "Les administrateurs doivent utiliser le tableau de bord administrateur"
            );
            navigate("/admin");
            return;
          }

          console.log("Regular user accessing profile page. Role:", userRole);

          setUserInfo(user);
          setEmail(user.email || "");
          setUsername(user.username || "");
        }

        // Vérifier si un onglet est spécifié dans l'URL
        const params = new URLSearchParams(location.search);
        const tabParam = params.get("tab");
        if (
          tabParam &&
          ["profile", "password", "subscription", "settings"].includes(tabParam)
        ) {
          setActiveTab(tabParam);
        }
      } catch (error) {
        console.error("Error loading user data:", error);
        // Don't immediately redirect on error, try to refresh token first
        try {
          await apiService.refreshToken();
          const user = await apiService.getCurrentUser();
          if (user) {
            setUserInfo(user);
            setEmail(user.email || "");
            setUsername(user.username || "");
          } else {
            toast.error("Session expirée. Veuillez vous reconnecter.");
            navigate("/login");
          }
        } catch (refreshError) {
          toast.error("Erreur lors du chargement des données utilisateur");
          navigate("/login");
        }
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, [navigate, location]);

  // Validate password as user types
  useEffect(() => {
    setPasswordValid({
      length: newPassword.length >= 8 && newPassword.length <= 15,
      uppercase: /[A-Z]/.test(newPassword),
      lowercase: /[a-z]/.test(newPassword),
      number: /[0-9]/.test(newPassword),
      special: /[!@#$%^&*]/.test(newPassword),
      match: newPassword === confirmPassword && newPassword !== "",
    });
  }, [newPassword, confirmPassword]);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Cette fonctionnalité sera implémentée dans apiService
      await apiService.updateUserProfile({
        username,
        email,
      });

      toast.success("Profil mis à jour avec succès");
    } catch (error: any) {
      toast.error(error.message || "Erreur lors de la mise à jour du profil");
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError("");
    setPasswordSuccess("");

    // Validate password
    const isValid = Object.values(passwordValid).every(Boolean);
    if (!isValid) {
      setPasswordError(
        "Veuillez corriger les erreurs de validation du mot de passe"
      );
      return;
    }

    setPasswordSaving(true);

    try {
      // Cette fonctionnalité sera implémentée dans apiService
      await apiService.changePassword({
        currentPassword,
        newPassword,
        confirmPassword,
      });

      setPasswordSuccess("Mot de passe modifié avec succès");
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error: any) {
      setPasswordError(
        error.message || "Erreur lors du changement de mot de passe"
      );
    } finally {
      setPasswordSaving(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 pt-28 pb-12 flex-grow">
          <div className="flex flex-col justify-center items-center min-h-[50vh] space-y-4">
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500"></div>
            <p className="text-gray-600">Chargement du profil...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNav={true} navType="second">
      <div className="container mx-auto px-4 pt-28 pb-12">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">
          Mon Profil
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Sidebar navigation */}
          <div className="md:col-span-1">
            <Card className="p-6 bg-white/95 rounded-xl shadow-lg h-fit">
              <nav className="space-y-3">
                <button
                  onClick={() => setActiveTab("profile")}
                  className={`w-full text-left px-4 py-4 rounded-lg flex items-center space-x-3 transition-all duration-200 ${
                    activeTab === "profile"
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg"
                      : "hover:bg-gray-100 hover:shadow-md"
                  }`}
                >
                  <User size={20} />
                  <span className="font-medium">Informations personnelles</span>
                </button>

                <button
                  onClick={() => setActiveTab("password")}
                  className={`w-full text-left px-4 py-4 rounded-lg flex items-center space-x-3 transition-all duration-200 ${
                    activeTab === "password"
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg"
                      : "hover:bg-gray-100 hover:shadow-md"
                  }`}
                >
                  <KeyRound size={20} />
                  <span className="font-medium">Changer le mot de passe</span>
                </button>

                <button
                  onClick={() => setActiveTab("subscription")}
                  className={`w-full text-left px-4 py-4 rounded-lg flex items-center space-x-3 transition-all duration-200 ${
                    activeTab === "subscription"
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg"
                      : "hover:bg-gray-100 hover:shadow-md"
                  }`}
                >
                  <CreditCard size={20} />
                  <span className="font-medium">Abonnement</span>
                </button>

                <button
                  onClick={() => setActiveTab("settings")}
                  className={`w-full text-left px-4 py-4 rounded-lg flex items-center space-x-3 transition-all duration-200 ${
                    activeTab === "settings"
                      ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg"
                      : "hover:bg-gray-100 hover:shadow-md"
                  }`}
                >
                  <Settings size={20} />
                  <span className="font-medium">Paramètres</span>
                </button>
              </nav>
            </Card>
          </div>

          {/* Main content area */}
          <div className="md:col-span-3">
            <Card className="p-8 bg-white/95 rounded-xl shadow-lg min-h-[600px]">
              {/* Profile Information Tab */}
              {activeTab === "profile" && (
                <div className="h-full flex flex-col">
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold mb-2 text-gray-800">
                      Informations personnelles
                    </h2>
                    <p className="text-gray-600">
                      Gérez vos informations de profil et vos préférences de
                      compte.
                    </p>
                  </div>

                  <div className="flex-1">
                    <form onSubmit={handleProfileUpdate} className="space-y-8">
                      <div className="grid grid-cols-1 gap-6">
                        <div className="space-y-2">
                          <Input
                            label="Nom d'utilisateur"
                            type="text"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            required
                            className="h-12"
                          />
                        </div>

                        <div className="space-y-2">
                          <Input
                            label="Email"
                            type="email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                            disabled
                            helpText="L'adresse email ne peut pas être modifiée"
                            className="h-12"
                          />
                        </div>
                      </div>

                      <div className="pt-6 border-t border-gray-200">
                        <Button
                          type="submit"
                          className="w-full h-12 text-lg"
                          loading={saving}
                        >
                          {saving ? (
                            <>
                              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                              Enregistrement...
                            </>
                          ) : (
                            <>
                              <Save className="w-5 h-5 mr-2" />
                              Enregistrer les modifications
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </div>
                </div>
              )}

              {/* Password Change Tab */}
              {activeTab === "password" && (
                <div className="h-full flex flex-col">
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold mb-2 text-gray-800">
                      Changer le mot de passe
                    </h2>
                    <p className="text-gray-600">
                      Assurez-vous que votre mot de passe est sécurisé et
                      unique.
                    </p>
                  </div>

                  {passwordError && (
                    <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start">
                      <AlertCircle className="text-red-500 w-5 h-5 mr-2 mt-0.5" />
                      <p className="text-red-700 text-sm">{passwordError}</p>
                    </div>
                  )}

                  {passwordSuccess && (
                    <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg flex items-start">
                      <CheckCircle className="text-green-500 w-5 h-5 mr-2 mt-0.5" />
                      <p className="text-green-700 text-sm">
                        {passwordSuccess}
                      </p>
                    </div>
                  )}

                  <div className="flex-1">
                    <form onSubmit={handlePasswordChange} className="space-y-8">
                      <div className="grid grid-cols-1 gap-6">
                        <div className="space-y-2">
                          <Input
                            label="Mot de passe actuel"
                            type="password"
                            value={currentPassword}
                            onChange={(e) => setCurrentPassword(e.target.value)}
                            required
                            className="h-12"
                          />
                        </div>

                        <div className="space-y-2">
                          <Input
                            label="Nouveau mot de passe"
                            type="password"
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            required
                            className="h-12"
                          />
                        </div>

                        <div className="space-y-2">
                          <Input
                            label="Confirmer le nouveau mot de passe"
                            type="password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            required
                            className="h-12"
                          />
                        </div>

                        <div className="bg-gray-50 p-4 rounded-lg">
                          <PasswordCriteriaList
                            password={newPassword}
                            confirmPassword={confirmPassword}
                          />
                        </div>
                      </div>

                      <div className="pt-6 border-t border-gray-200">
                        <Button
                          type="submit"
                          className="w-full h-12 text-lg"
                          loading={passwordSaving}
                          disabled={
                            !Object.values(passwordValid).every(Boolean)
                          }
                        >
                          {passwordSaving ? (
                            <>
                              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                              Modification en cours...
                            </>
                          ) : (
                            <>
                              <KeyRound className="w-5 h-5 mr-2" />
                              Changer le mot de passe
                            </>
                          )}
                        </Button>
                      </div>
                    </form>

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex flex-col items-start">
                        <div className="flex items-center">
                          <span className="text-sm text-gray-600">
                            Vous avez oublié votre mot de passe actuel ?
                          </span>
                          <Link
                            to="/forgot-password"
                            className="text-sm text-sitechecker-blue hover:underline ml-2"
                          >
                            Cliquez ici
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Subscription Tab */}
              {activeTab === "subscription" && (
                <div className="h-full flex flex-col">
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold mb-2 text-gray-800">
                      Détails de l'abonnement
                    </h2>
                    <p className="text-gray-600">
                      Consultez et gérez votre plan d'abonnement actuel.
                    </p>
                  </div>

                  <div className="flex-1">
                    <div className="bg-gradient-to-br from-blue-50 to-purple-50 p-6 rounded-xl border border-blue-100 mb-8">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center md:text-left">
                          <p className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">
                            Plan actuel
                          </p>
                          <p className="text-2xl font-bold text-gray-800">
                            {userInfo?.role === 1 || userInfo?.role === "1"
                              ? "Gratuit"
                              : userInfo?.role === 2 || userInfo?.role === "2"
                              ? "Premium"
                              : "Admin"}
                          </p>
                        </div>

                        <div className="text-center md:text-left">
                          <p className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">
                            Cycle de facturation
                          </p>
                          <p className="text-2xl font-bold text-gray-800">
                            {userInfo?.billingCycle === "monthly"
                              ? "Mensuel"
                              : userInfo?.billingCycle === "annual"
                              ? "Annuel"
                              : "N/A"}
                          </p>
                        </div>

                        <div className="text-center md:text-left">
                          <p className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">
                            Date d'inscription
                          </p>
                          <p className="text-2xl font-bold text-gray-800">
                            {userInfo?.createdAt
                              ? new Date(userInfo.createdAt).toLocaleDateString(
                                  "fr-FR"
                                )
                              : "N/A"}
                          </p>
                        </div>
                      </div>
                    </div>

                    {(userInfo?.role === 1 || userInfo?.role === "1") && (
                      <div className="pt-6 border-t border-gray-200">
                        <Button
                          onClick={() => navigate("/payment-plans")}
                          className="w-full h-12 text-lg"
                        >
                          <CreditCard className="w-5 h-5 mr-2" />
                          Passer au plan Premium
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === "settings" && (
                <div className="h-full flex flex-col">
                  <div className="mb-8">
                    <h2 className="text-2xl font-bold mb-2 text-gray-800">
                      Paramètres
                    </h2>
                    <p className="text-gray-600">
                      Configurez vos préférences et paramètres de compte.
                    </p>
                  </div>

                  <div className="flex-1">
                    <div className="space-y-6">
                      <div className="bg-gray-50 p-6 rounded-xl">
                        <h3 className="text-lg font-semibold mb-4 text-gray-800">
                          Notifications
                        </h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-700">
                                Notifications par email
                              </p>
                              <p className="text-sm text-gray-500">
                                Recevez des notifications sur vos analyses
                              </p>
                            </div>
                            <input
                              type="checkbox"
                              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                              defaultChecked
                            />
                          </div>
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-700">
                                Rapports hebdomadaires
                              </p>
                              <p className="text-sm text-gray-500">
                                Résumé de vos analyses de la semaine
                              </p>
                            </div>
                            <input
                              type="checkbox"
                              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-6 rounded-xl">
                        <h3 className="text-lg font-semibold mb-4 text-gray-800">
                          Confidentialité
                        </h3>
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-700">
                                Profil public
                              </p>
                              <p className="text-sm text-gray-500">
                                Permettre aux autres de voir votre profil
                              </p>
                            </div>
                            <input
                              type="checkbox"
                              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>

      <Toaster position="top-right" richColors />
    </Layout>
  );
}
