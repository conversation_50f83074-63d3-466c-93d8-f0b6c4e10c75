using System;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace SiteCheckerApp.Services
{
    public class EmailService : IEmailService
    {
        private readonly string _smtpServer = string.Empty;
        private readonly int _smtpPort;
        private readonly string _smtpUser = string.Empty;
        private readonly string _smtpPass = string.Empty;

        public EmailService(IConfiguration configuration)
        {
            // Essayer d'abord de récupérer les valeurs depuis les variables d'environnement
            string? envServer = Environment.GetEnvironmentVariable("SMTP_SERVER");
            if (!string.IsNullOrEmpty(envServer))
            {
                _smtpServer = envServer;
            }

            // Essayer de convertir le port SMTP en entier
            string? smtpPortStr = Environment.GetEnvironmentVariable("SMTP_PORT");
            if (!string.IsNullOrEmpty(smtpPortStr) && int.TryParse(smtpPortStr, out int portValue))
            {
                _smtpPort = portValue;
            }
            else
            {
                // Si la conversion échoue, utiliser la configuration
                var smtpConfig = configuration.GetSection("SmtpSettings");

                string? serverFromConfig = smtpConfig["Server"];
                if (string.IsNullOrEmpty(_smtpServer) && !string.IsNullOrEmpty(serverFromConfig))
                {
                    _smtpServer = serverFromConfig;
                }

                if (_smtpServer == string.Empty)
                    throw new InvalidOperationException("SMTP server is missing.");

                _smtpPort = int.TryParse(smtpConfig["Port"], out var port)
                    ? port
                    : throw new InvalidOperationException("SMTP port is missing or invalid.");
            }

            // Récupérer les autres valeurs
            string? envUser = Environment.GetEnvironmentVariable("SMTP_USER");
            string? configUser = configuration.GetSection("SmtpSettings")["User"];

            if (!string.IsNullOrEmpty(envUser))
            {
                _smtpUser = envUser;
            }
            else if (!string.IsNullOrEmpty(configUser))
            {
                _smtpUser = configUser;
            }
            else
            {
                throw new InvalidOperationException("SMTP user is missing.");
            }

            string? envPass = Environment.GetEnvironmentVariable("SMTP_PASSWORD");
            string? configPass = configuration.GetSection("SmtpSettings")["Password"];

            if (!string.IsNullOrEmpty(envPass))
            {
                _smtpPass = envPass;
            }
            else if (!string.IsNullOrEmpty(configPass))
            {
                _smtpPass = configPass;
            }
            else
            {
                throw new InvalidOperationException("SMTP password is missing.");
            }

            // Afficher les valeurs pour le débogage
            Console.WriteLine($"SMTP Server: {_smtpServer}");
            Console.WriteLine($"SMTP Port: {_smtpPort}");
            Console.WriteLine($"SMTP User: {_smtpUser}");
            Console.WriteLine($"SMTP Password: {(_smtpPass != null ? "***" : "null")}");
        }

        public async Task SendVerificationEmailAsync(string email, string verificationLink)
        {
            string subject = "Email Verification";
            string body = $@"
                <p>Hello,</p>
                <p>Please verify your email by clicking the link below:</p>
                <p><a href='{verificationLink}'>Verify Email</a></p>
                <p>Thank you!</p>";
            await SendEmailAsync(email, subject, body);
        }

        public async Task SendPasswordResetEmail(string email, string otp)
        {
            string subject = "Password Reset Request";
            string body = $@"
                <p>Your OTP for password reset is: <strong>{otp}</strong></p>
                <p>This code is valid for 10 minutes.</p>
                <p>If you did not request a password reset, please ignore this email.</p>";
            await SendEmailAsync(email, subject, body);
        }

        public async Task SendOtpEmail(string email, string otp)
        {
            string subject = "Your OTP Code";
            string body = $@"
                <p>Your OTP code is: <strong>{otp}</strong></p>
                <p>This code is valid for 10 minutes.</p>";
            await SendEmailAsync(email, subject, body);
        }

        public async Task SendAdminOtpEmail(string email, string otp)
        {
            string subject = "Admin Login OTP Code";
            string body = $@"
                <p>Your administrator OTP code is: <strong>{otp}</strong></p>
                <p>This code is valid for 10 minutes.</p>";
            await SendEmailAsync(email, subject, body);
        }


        private async Task SendEmailAsync(string to, string subject, string body)
        {
            using var client = new SmtpClient(_smtpServer, _smtpPort);
            client.Credentials = new NetworkCredential(_smtpUser, _smtpPass);
            client.EnableSsl = true;

            var mailMessage = new MailMessage
            {
                From = new MailAddress(_smtpUser),
                Subject = subject,
                Body = body,
                IsBodyHtml = true
            };

            mailMessage.To.Add(to);

            try
            {
                await client.SendMailAsync(mailMessage);
            }
            catch (SmtpException ex)
            {
                Console.WriteLine($"SMTP Error: {ex.Message}");
                throw new Exception("Failed to send email via SMTP.", ex);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Unexpected Error: {ex.Message}");
                throw new Exception("Unexpected error occurred while sending email.", ex);
            }
        }
    }
}