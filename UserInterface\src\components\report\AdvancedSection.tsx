import { ReactNode } from "react";
import { Lock } from "lucide-react";
import { useAuth } from "../../contexts/AuthContext";

interface AdvancedSectionProps {
  unlocked: boolean;
  onUnlock: () => void;
  children: ReactNode;
}

export default function AdvancedSection({
  unlocked,
  onUnlock,
  children,
}: AdvancedSectionProps) {
  const { user } = useAuth();

  // Determine if advanced section should be unlocked based on user role (2 or 3)
  const isPremiumOrAdmin =
    user && (Number(user.role) === 2 || Number(user.role) === 3);

  // If user is premium or admin, override unlocked prop to true
  const shouldUnlock = isPremiumOrAdmin || unlocked;

  return (
    <div className="relative rounded-xl overflow-hidden bg-white shadow-lg">
      <div
        className={
          shouldUnlock
            ? "p-6 space-y-6"
            : "p-6 filter blur-sm bg-white/70 pointer-events-none select-none"
        }
      >
        {shouldUnlock && (
          <div className="space-y-1">
            <h3 className="text-lg font-semibold text-gray-800">
              Analyse Technique
            </h3>
            <p className="text-sm text-gray-500">
              Détails techniques et recommandations avancées
            </p>
          </div>
        )}
        {children}
      </div>

      {!unlocked && (
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center px-4">
          <Lock className="w-6 h-6 text-purple-600 mb-2" />
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Analyse Avancée
          </h3>
          <p className="text-sm text-gray-600 max-w-sm mb-4">
            Débloquez l'analyse technique détaillée et les recommandations
            expertes avec Premium
          </p>
          <button
            onClick={onUnlock}
            className="bg-gradient-to-r from-[#D4AF37] via-[#EEDC82] to-[#FFFFFF] text-yellow-900 font-bold px-6 py-3 rounded-full shadow-md flex items-center gap-2 transition duration-200 hover:brightness-105 border border-yellow-300"
          >
            Passer à Premium <span className="text-yellow-700">⭐</span>
          </button>
        </div>
      )}
    </div>
  );
}
