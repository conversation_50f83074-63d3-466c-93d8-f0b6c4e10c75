import i18n from "i18next";
import { initReactI18next } from "react-i18next";

// Define the translations
const resources = {
  fr: {
    translation: {
      // Security scan
      "security.title": "Sécurité",
      "security.ssl": "SSL/TLS",
      "security.headers": "En-têtes HTTP",
      "security.vulnerabilities": "Vulnérabilités",
      "security.javascript": "Sécurité JavaScript",
      "security.api": "Sécurité API",
      "security.score": "Score de Sécurité",
      "security.analysis": "Analyse Technique de Sécurité",

      // SEO scan
      "seo.title": "SEO",
      "seo.meta": "Balises Meta",
      "seo.mobile": "Optimisation Mobile",
      "seo.images": "Optimisation Images",
      "seo.performance": "Performance",
      "seo.content": "Analyse du Contenu",
      "seo.url": "Structure URL",
      "seo.score": "Score SEO",
      "seo.analysis": "Analyse d'Optimisation SEO",

      // Common
      "common.loading": "Chargement...",
      "common.error": "Erreur",
      "common.success": "Succès",
      "common.warning": "Attention",
      "common.scan": "Analyse",
      "common.download": "Télécharger",
      "common.back": "Retour",
      "common.score": "/100",

      // Report
      "report.title": "Rapport d'analyse",
      "report.security": "Sécurité",
      "report.seo": "SEO",
      "report.actions": "Actions recommandées",
      "report.details": "Détails techniques",
      "report.action_plan": "Plan d'Action Recommandé",
      "report.critical_actions": "Actions Critiques",
      "report.important_actions": "Actions Importantes",
      "report.suggested_improvements": "Améliorations Suggérées",
      "report.no_actions": "Aucune action requise",
      "report.no_actions_desc":
        "Félicitations ! Votre site ne présente aucun problème nécessitant une action immédiate. Continuez comme ça ! ✨",
      "report.review_implement":
        "Examinez et mettez en œuvre ces recommandations pour améliorer votre site.",
      "report.show_less": "Afficher moins",
      "report.technical_details": "Analyses Techniques Détaillées",
      "report.no_info": "Aucune information disponible",
      "report.view": "Voir",

      // Priority levels
      "priority.high": "Élevée",
      "priority.medium": "Moyenne",
      "priority.low": "Basse",

      // Difficulty levels
      "difficulty.easy": "Facile",
      "difficulty.medium": "Moyen",
      "difficulty.hard": "Difficile",
    },
  },
  en: {
    translation: {
      // Security scan
      "security.title": "Security",
      "security.ssl": "SSL/TLS",
      "security.headers": "HTTP Headers",
      "security.vulnerabilities": "Vulnerabilities",
      "security.javascript": "JavaScript Security",
      "security.api": "API Security",
      "security.score": "Security Score",
      "security.analysis": "Technical Security Analysis",

      // SEO scan
      "seo.title": "SEO",
      "seo.meta": "Meta Tags",
      "seo.mobile": "Mobile Optimization",
      "seo.images": "Image Optimization",
      "seo.performance": "Performance",
      "seo.content": "Content Analysis",
      "seo.url": "URL Structure",
      "seo.score": "SEO Score",
      "seo.analysis": "SEO Optimization Analysis",

      // Common
      "common.loading": "Loading...",
      "common.error": "Error",
      "common.success": "Success",
      "common.warning": "Warning",
      "common.scan": "Scan",
      "common.download": "Download",
      "common.back": "Back",
      "common.score": "/100",

      // Report
      "report.title": "Analysis Report",
      "report.security": "Security",
      "report.seo": "SEO",
      "report.actions": "Recommended Actions",
      "report.details": "Technical Details",
      "report.action_plan": "Recommended Action Plan",
      "report.critical_actions": "Critical Actions",
      "report.important_actions": "Important Actions",
      "report.suggested_improvements": "Suggested Improvements",
      "report.no_actions": "No actions required",
      "report.no_actions_desc":
        "Congratulations! Your site doesn't have any issues requiring immediate action. Keep up the good work! ✨",
      "report.review_implement":
        "Review and implement these recommendations to improve your site.",
      "report.show_less": "Show Less",
      "report.technical_details": "Detailed Technical Analyses",
      "report.no_info": "No information available",
      "report.view": "View",

      // Priority levels
      "priority.high": "High",
      "priority.medium": "Medium",
      "priority.low": "Low",

      // Difficulty levels
      "difficulty.easy": "Easy",
      "difficulty.medium": "Medium",
      "difficulty.hard": "Hard",
    },
  },
};

// Initialize i18next
i18n.use(initReactI18next).init({
  resources,
  lng: "fr", // Default language
  fallbackLng: "en",
  interpolation: {
    escapeValue: false, // React already escapes values
  },
  react: {
    useSuspense: false, // Disable suspense to avoid issues
  },
});

export default i18n;
