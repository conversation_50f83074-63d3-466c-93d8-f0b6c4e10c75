namespace SiteCheckerApp.DTOs
{
    public enum OtpVerificationType
    {
        Registration,
        AdminLogin,
        PasswordReset
    }

    public class OtpVerificationResponse
    {
        public bool IsValid { get; set; }
        public bool IsExpired { get; set; }
        public string Message { get; set; } = string.Empty;
        public OtpVerificationType VerificationType { get; set; }
        public string? AccessToken { get; set; }
        public string? RefreshToken { get; set; }
        public Guid? UserId { get; set; }
        public string? Email { get; set; }
        public string? Username { get; set; }
        public int? Role { get; set; }
    }
}
