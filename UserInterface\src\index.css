/* Import print styles */
@import "./print-styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #ffffff; /* Valid color value */
    --foreground: #000000; /* Valid color value */

    --card: #ffffff;
    --card-foreground: #000000;

    --popover: #ffffff;
    --popover-foreground: #000000;

    --primary: 238 100% 64%;
    --primary-foreground: #ffffff;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: #000000;

    --muted: 210 40% 96.1%;
    --muted-foreground: #000000;
  }

  * {
    @apply border-gray-300 selection:bg-sitechecker-blue/20 selection:text-sitechecker-blue;
  }

  html,
  body {
    @apply antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02";
    /* Ensure smooth scrolling doesn't affect fixed elements */
    scroll-behavior: smooth;
    overflow-x: hidden;
  }

  body {
    @apply bg-background text-foreground min-h-screen;
    /* Prevent any layout shifts that could affect fixed navbar */
    position: relative;
    margin: 0;
    padding: 0;
  }
}

/* Custom styles */
@layer components {
  .glassmorphism {
    @apply bg-white/80 backdrop-blur-xl;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .navbar-blur {
    backdrop-filter: blur(40px) saturate(200%) brightness(110%);
    -webkit-backdrop-filter: blur(40px) saturate(200%) brightness(110%);
    -moz-backdrop-filter: blur(40px) saturate(200%) brightness(110%);
    -ms-backdrop-filter: blur(40px) saturate(200%) brightness(110%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  /* Specific styles for the second navbar to ensure it's absolutely fixed */
  .second-navbar-fixed {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    z-index: 9999 !important;
    will-change: transform !important;
    -webkit-backface-visibility: hidden !important;
    backface-visibility: hidden !important;
    /* Ensure it doesn't inherit any relative positioning */
    margin: 0 !important;
    padding: 0 !important;
    /* Lock it in place */
    contain: layout style paint !important;
    /* Additional properties to prevent movement */
    pointer-events: auto !important;
    isolation: isolate !important;
    /* Ensure it's not affected by parent transforms */
    transform-style: flat !important;
    /* Force hardware acceleration */
    -webkit-transform: translateZ(0) !important;
    -moz-transform: translateZ(0) !important;
    -ms-transform: translateZ(0) !important;
    -o-transform: translateZ(0) !important;
  }

  /* Ensure the navbar content doesn't move */
  .second-navbar-fixed > div {
    position: relative !important;
    z-index: 1 !important;
  }

  /* Media queries to ensure fixed positioning works on all devices */
  @media screen and (max-width: 768px) {
    .second-navbar-fixed {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      width: 100vw !important;
    }
  }

  @media screen and (min-width: 769px) {
    .second-navbar-fixed {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      width: 100% !important;
    }
  }

  .backdrop-blur-3xl {
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
  }

  /* Fallback for browsers that don't support backdrop-filter */
  @supports not (backdrop-filter: blur(20px)) {
    .navbar-blur {
      background-color: rgba(255, 255, 255, 0.9) !important;
    }

    .second-navbar-fixed .navbar-blur {
      background-color: rgba(255, 255, 255, 0.95) !important;
    }
  }

  .card-hover-effect {
    @apply transition-all duration-300 ease-in-out hover:shadow hover:-translate-y-1; /* Updated shadow class */
  }

  /* Background pattern for dashboards */
  .bg-grid-pattern {
    background-image: linear-gradient(
        to right,
        rgba(0, 0, 0, 0.05) 1px,
        transparent 1px
      ),
      linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .feature-icon {
    @apply flex items-center justify-center w-12 h-12 rounded-full bg-sitechecker-blue/10 text-sitechecker-blue;
  }

  .input-focus-ring {
    @apply focus-within:ring-2 focus-within:ring-sitechecker-blue/20 focus-within:border-sitechecker-blue;
  }

  .button-gradient {
    @apply bg-gradient-to-r from-blue-500 to-purple-500 hover:shadow transition-all duration-300; /* Updated gradient class */
  }

  /* Neo card styling for report components */
  .neo-card {
    @apply rounded-lg bg-white/10 border border-white/20 backdrop-blur-sm transition-all duration-300;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .neo-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.25);
  }

  /* Neon glow effects */
  .neo-glow {
    box-shadow: 0 0 15px rgba(155, 135, 245, 0.3),
      0 0 30px rgba(155, 135, 245, 0.1);
  }

  .neo-glow-magenta {
    box-shadow: 0 0 15px rgba(217, 70, 239, 0.3),
      0 0 30px rgba(217, 70, 239, 0.1);
  }

  .neo-glow-blue {
    box-shadow: 0 0 15px rgba(30, 174, 219, 0.3),
      0 0 30px rgba(30, 174, 219, 0.1);
  }

  /* Neon borders */
  .neon-border {
    border: 1px solid rgba(155, 135, 245, 0.3);
  }

  .neon-border-magenta {
    border: 1px solid rgba(217, 70, 239, 0.3);
  }

  .neon-border-blue {
    border: 1px solid rgba(30, 174, 219, 0.3);
  }

  /* Static gradient backgrounds to prevent layout shifts */
  .bg-gradient-static {
    background: linear-gradient(135deg, #6495ed 0%, #b99aef 100%);
    background-size: 100% 100%; /* Static size */
  }

  /* Custom gradient background based on reference image - static version */
  .custom-gradient-bg {
    background: linear-gradient(135deg, #2563eb 0%, #9333ea 100%);
    background-size: 100% 100%; /* Static size */
  }

  /* For backward compatibility - static version */
  .lighter-gradient-bg {
    background: linear-gradient(135deg, #6495ed 0%, #b99aef 100%);
    background-size: 100% 100%; /* Static size */
  }

  .animated-gradient-bg {
    background: linear-gradient(135deg, #2563eb 0%, #9333ea 100%);
    background-size: 100% 100%; /* Static size */
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Technical section styling */
  .content-section {
    @apply relative;
  }

  .content-section::before {
    content: "";
    @apply absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-gray-200 to-transparent;
  }
}

/* Removed gradient-shift animation to prevent layout shifts */

/* Page transition animations - further optimized to prevent layout shifts */
.page-transition-enter {
  opacity: 0;
  will-change: opacity;
  transform: translateZ(0); /* Force GPU acceleration */
}

.page-transition-enter-active {
  opacity: 1;
  transition: opacity 300ms;
  transform: translateZ(0); /* Force GPU acceleration */
}

.page-transition-exit {
  opacity: 1;
  will-change: opacity;
  transform: translateZ(0); /* Force GPU acceleration */
}

/* Content-visibility optimizations */
.offscreen-content {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px; /* Reserve space for content */
}

/* Prevent layout shifts for images */
img {
  aspect-ratio: attr(width) / attr(height);
}

.page-transition-exit-active {
  opacity: 0;
  transition: opacity 300ms;
  transform: translateZ(0); /* Force GPU acceleration */
}

/* Custom loader animation - optimized for layout stability */
@keyframes pulse-opacity {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 0.8;
  }
}

.pulse-ring {
  animation: pulse-opacity 1.5s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: opacity;
}

/* FadeIn animation for password feedback - optimized for layout stability */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: opacity;
}

/* Gradient animations for modern effects */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient {
  background: linear-gradient(-45deg, #2563eb, #9333ea, #3b82f6, #8b5cf6);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* Floating animation for shapes */
@keyframes float-gentle {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.animate-float {
  animation: float-gentle 6s ease-in-out infinite;
}

/* Glow effect for interactive elements */
@keyframes glow-pulse {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(37, 99, 235, 0.6),
      0 0 60px rgba(147, 51, 234, 0.3);
  }
}

.animate-glow {
  animation: glow-pulse 3s ease-in-out infinite;
}

/* Custom cursor for modern interaction */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: radial-gradient(
    circle,
    rgba(37, 99, 235, 0.8) 0%,
    transparent 70%
  );
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
  mix-blend-mode: difference;
}

.custom-cursor-hover {
  transform: scale(2);
  background: radial-gradient(
    circle,
    rgba(147, 51, 234, 0.8) 0%,
    transparent 70%
  );
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  html {
    scroll-behavior: auto;
  }
}

/* Pulse opacity animation for progress bar */
@keyframes pulse-opacity {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.2;
  }
}

.animate-pulse-opacity {
  animation: pulse-opacity 1.5s ease-in-out infinite;
}

/* Optimized pulse animations that only affect filter and opacity, not size */
@keyframes pulse-glow-red {
  0%,
  100% {
    filter: drop-shadow(0 0 4px rgba(220, 38, 38, 0.7))
      drop-shadow(0 0 6px rgba(220, 38, 38, 0.5));
    opacity: 1;
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(220, 38, 38, 0.9))
      drop-shadow(0 0 12px rgba(220, 38, 38, 0.7));
    opacity: 0.9;
  }
}

@keyframes pulse-glow-amber {
  0%,
  100% {
    filter: drop-shadow(0 0 4px rgba(202, 138, 4, 0.7))
      drop-shadow(0 0 6px rgba(202, 138, 4, 0.5));
    opacity: 1;
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(202, 138, 4, 0.9))
      drop-shadow(0 0 12px rgba(202, 138, 4, 0.7));
    opacity: 0.9;
  }
}

@keyframes pulse-glow-green {
  0%,
  100% {
    filter: drop-shadow(0 0 4px rgba(22, 163, 74, 0.7))
      drop-shadow(0 0 6px rgba(22, 163, 74, 0.5));
    opacity: 1;
  }
  50% {
    filter: drop-shadow(0 0 8px rgba(22, 163, 74, 0.9))
      drop-shadow(0 0 12px rgba(22, 163, 74, 0.7));
    opacity: 0.9;
  }
}

.animate-pulse-glow-red {
  animation: pulse-glow-red 2s infinite;
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: filter, opacity;
}

.animate-pulse-glow-amber {
  animation: pulse-glow-amber 2s infinite;
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: filter, opacity;
}

.animate-pulse-glow-green {
  animation: pulse-glow-green 2s infinite;
  transform: translateZ(0); /* Force GPU acceleration */
  will-change: filter, opacity;
}

/* Highlight pulse animation for recommended actions section */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.5);
  }
  70% {
    box-shadow: 0 0 0 15px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.highlight-pulse {
  animation: highlight-pulse 1.5s ease-out;
}
