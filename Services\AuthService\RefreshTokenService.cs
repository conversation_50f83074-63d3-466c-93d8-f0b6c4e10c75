using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SiteCheckerApp.Models;
using SiteCheckerApp.Data;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace SiteCheckerApp.Services
{
    public class RefreshTokenService : IRefreshTokenService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<RefreshTokenService> _logger;
        private readonly int _refreshTokenExpiryDays;

        public RefreshTokenService(AppDbContext context, ILogger<RefreshTokenService> logger, IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            // Make refresh token expiry configurable, default to 7 days
            if (!int.TryParse(configuration["Jwt:RefreshTokenExpiryDays"], out _refreshTokenExpiryDays))
            {
                _refreshTokenExpiryDays = 7;
            }
        }

        public async Task<string> GenerateRefreshTokenAsync(Guid userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
                throw new InvalidOperationException("User not found");

            // Generate a new refresh token as a secure random string
            var newToken = GenerateSecureToken();
            var hashedToken = HashToken(newToken);

            user.RefreshToken = hashedToken;
            user.RefreshTokenExpiry = DateTime.UtcNow.AddDays(_refreshTokenExpiryDays);

            await _context.SaveChangesAsync();

            return newToken; // Return the plain token to client
        }

        public async Task<string> RotateRefreshTokenAsync(Guid userId, string currentRefreshToken)
        {
            if (string.IsNullOrEmpty(currentRefreshToken))
            {
                throw new ArgumentException("Current refresh token cannot be null or empty");
            }

            await using var transaction = await _context.Database.BeginTransactionAsync(
                System.Data.IsolationLevel.Serializable);

            try
            {
                // Get user with explicit lock and holdlock to prevent concurrent updates
                var user = await _context.Users
                    .FromSqlInterpolated($@"
                        SELECT * FROM Users WITH (UPDLOCK, ROWLOCK, HOLDLOCK) 
                        WHERE Id = {userId}")
                    .FirstOrDefaultAsync();

                if (user == null)
                    throw new InvalidOperationException("User not found");

                if (string.IsNullOrEmpty(user.RefreshToken))
                {
                    _logger.LogWarning("No refresh token exists for user {UserId}", userId);
                    throw new InvalidOperationException("No existing refresh token");
                }

                // Validate hashed token
                if (!VerifyHashedToken(user.RefreshToken, currentRefreshToken))
                {
                    _logger.LogWarning("Token mismatch. DB: {DbToken}, Provided: {Provided}",
                        user.RefreshToken, currentRefreshToken);
                    throw new InvalidOperationException("Refresh token mismatch");
                }

                // Generate and update - ONLY PLACE WHERE REFRESH TOKEN IS UPDATED
                var newRefreshToken = GenerateSecureToken();
                var hashedNewToken = HashToken(newRefreshToken);
                user.RefreshToken = hashedNewToken;
                user.RefreshTokenExpiry = DateTime.UtcNow.AddDays(_refreshTokenExpiryDays);
                user.TokenVersion++;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("Rotating token. Current: {CurrentTokenFirst10}, New: {NewTokenFirst10}",
                    currentRefreshToken[..10], newRefreshToken[..10]);

                return newRefreshToken;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rotating refresh token for user {UserId}", userId);
                await transaction.RollbackAsync();
                throw;
            }
        }

        private string GenerateSecureToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var tokenBytes = new byte[32]; // 256 bits
            rng.GetBytes(tokenBytes);
            return Convert.ToBase64String(tokenBytes);
        }

        private string HashToken(string token)
        {
            using var sha256 = SHA256.Create();
            var bytes = Encoding.UTF8.GetBytes(token);
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToBase64String(hash);
        }

        private bool VerifyHashedToken(string hashedToken, string token)
        {
            var hashOfInput = HashToken(token);
            return hashOfInput == hashedToken;
        }

        public async Task<bool> ValidateRefreshTokenAsync(Guid userId, string refreshToken)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                _logger.LogWarning("ValidateRefreshTokenAsync: User not found for userId {UserId}", userId);
                return false;
            }
            if (string.IsNullOrEmpty(user.RefreshToken) || !VerifyHashedToken(user.RefreshToken, refreshToken))
            {
                _logger.LogWarning("ValidateRefreshTokenAsync: Refresh token mismatch or empty for userId {UserId}. Provided: {Provided}, Expected: {Expected}", userId, refreshToken, user.RefreshToken);
                return false;
            }
            if (user.RefreshTokenExpiry < DateTime.UtcNow)
            {
                _logger.LogWarning("ValidateRefreshTokenAsync: Refresh token expired for userId {UserId}. Expiry: {Expiry}, Now: {Now}", userId, user.RefreshTokenExpiry, DateTime.UtcNow);
                return false;
            }
            _logger.LogInformation("ValidateRefreshTokenAsync: Refresh token valid for userId {UserId}", userId);
            return true;
        }

        public async Task RevokeRefreshTokenAsync(Guid userId)
        {
            var user = await _context.Users.FindAsync(userId);
            if (user != null)
            {
                _logger.LogInformation("RevokeRefreshTokenAsync: Revoking refresh token for user {UserId}", userId);
                // Clear the refresh token and expiry to revoke
                user.RefreshToken = null;
                user.RefreshTokenExpiry = null;
                await _context.SaveChangesAsync();
                _logger.LogInformation("RevokeRefreshTokenAsync: Refresh token revoked for user {UserId}", userId);
            }
            else
            {
                _logger.LogWarning("RevokeRefreshTokenAsync: User not found for userId {UserId}", userId);
            }
        }
    }
}
