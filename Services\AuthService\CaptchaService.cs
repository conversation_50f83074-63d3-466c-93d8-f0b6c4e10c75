using Newtonsoft.Json;

namespace SiteCheckerApp.Services
{
    public class CaptchaService : ICaptchaService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _config;
        private readonly ILogger<CaptchaService> _logger;

        public CaptchaService(HttpClient httpClient, IConfiguration config, ILogger<CaptchaService> logger)
        {
            _httpClient = httpClient;
            _config = config;
            _logger = logger;
        }

        public async Task<bool> VerifyAsync(string token, bool isRegistration = false, bool isV2Fallback = false)
        {
            if (isV2Fallback)
            {
                // Verify using reCAPTCHA v2
                return await VerifyV2Async(token);
            }

            // Verify using reCAPTCHA v3
            return await VerifyV3Async(token, isRegistration);
        }

        public async Task<bool> VerifyV3Async(string token, bool isRegistration)
        {
            try
            {
                // For local development/testing
                if (_config["BypassCaptcha"] == "true")
                {
                    _logger.LogWarning("CAPTCHA verification bypassed");
                    return true;
                }

                // Get the secret key directly from environment variables
                var secret = Environment.GetEnvironmentVariable("RECAPTCHA_SECRET_KEY");
                if (string.IsNullOrEmpty(secret))
                {
                    // Fall back to configuration if environment variable is not set
                    secret = _config["Captcha:SecretKey"];
                }

                _logger.LogInformation($"Using reCAPTCHA secret key: {(secret?.Length > 5 ? secret.Substring(0, 5) + "..." : "not found")}");
                var url = $"https://www.google.com/recaptcha/api/siteverify?secret={secret}&response={token}";
                _logger.LogInformation($"Sending reCAPTCHA verification request to: {url.Substring(0, url.IndexOf("?") + 20)}...");

                var responseString = await _httpClient.GetStringAsync(url);
                _logger.LogInformation($"reCAPTCHA response: {responseString}");

                var result = JsonConvert.DeserializeObject<RecaptchaV3Response>(responseString);

                if (result == null)
                {
                    _logger.LogWarning("reCAPTCHA response was null");
                    return false;
                }

                // Optional: Validate action matches expected
                var expectedAction = isRegistration ? "register" : "login";

                // For now, let's make action validation optional since some reCAPTCHA responses might not include it
                if (!string.IsNullOrEmpty(result.Action) && result.Action != expectedAction)
                {
                    _logger.LogWarning($"reCAPTCHA action mismatch. Expected: {expectedAction}, Got: {result.Action}");
                    // For debugging purposes, we'll log this but not fail the verification
                    // return false;
                }
                else if (string.IsNullOrEmpty(result.Action))
                {
                    _logger.LogWarning($"reCAPTCHA action is missing. Expected: {expectedAction}");
                    // For debugging purposes, we'll log this but not fail the verification
                    // return false;
                }

                float minScore = isRegistration ? 0.7f : 0.5f;
                if (!result.Success || result.Score < minScore)
                {
                    _logger.LogWarning($"reCAPTCHA validation failed. Success: {result.Success}, Score: {result.Score}, Required: {minScore}");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CAPTCHA verification failed");
                return false; // Fail securely
            }
        }

        public async Task<bool> VerifyV2Async(string token)
        {
            try
            {
                if (_config["BypassCaptcha"] == "true")
                {
                    _logger.LogWarning("CAPTCHA verification bypassed");
                    return true;
                }

                // Get the secret key directly from environment variables
                var secret = Environment.GetEnvironmentVariable("RECAPTCHA_SECRET_KEY_V2");
                if (string.IsNullOrEmpty(secret))
                {
                    // Fall back to configuration if environment variable is not set
                    secret = _config["Captcha:SecretKeyV2"];
                }

                _logger.LogInformation($"Using reCAPTCHA v2 secret key: {(secret?.Length > 5 ? secret.Substring(0, 5) + "..." : "not found")}");
                var url = $"https://www.google.com/recaptcha/api/siteverify?secret={secret}&response={token}";
                _logger.LogInformation($"Sending reCAPTCHA v2 verification request to: {url.Substring(0, url.IndexOf("?") + 20)}...");

                var responseString = await _httpClient.GetStringAsync(url);
                _logger.LogInformation($"reCAPTCHA v2 response: {responseString}");

                var result = JsonConvert.DeserializeObject<RecaptchaV2Response>(responseString);

                if (result == null || !result.Success)
                {
                    _logger.LogWarning("reCAPTCHA v2 validation failed.");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "reCAPTCHA v2 verification failed");
                return false;
            }
        }

        private record RecaptchaV3Response(bool Success, float Score, string? Action);
        private record RecaptchaV2Response(bool Success);
    }
}
