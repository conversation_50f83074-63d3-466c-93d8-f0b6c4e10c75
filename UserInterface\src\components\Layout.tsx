import React, { PropsWithChildren } from "react";
import Navbar from "./Navbar"; // Default Navbar
import SecondNavbar from "./SecondNavbar"; // New Second Navbar
import Footer from "./Footer"; // Common Footer component

interface LayoutProps extends PropsWithChildren {
  showNav?: boolean;
  showFooter?: boolean;
  className?: string;
  navType?: "default" | "second"; // Add type for choosing the navbar
}

export default function Layout({
  children,
  showNav = true,
  showFooter = true,
  className = "",
  navType = "default", // Default navbar type
}: LayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-static overflow-hidden relative">
      {/* Static Background - No animations to prevent layout shifts */}
      <div
        className="absolute inset-0 opacity-30 pointer-events-none"
        aria-hidden="true"
        style={{ contain: "strict" }}
      >
        {/* Using fixed positioning and contain: strict to prevent layout shifts */}
        <div
          className="fixed top-1/4 left-1/4 w-1/2 h-1/2 rounded-full bg-blue-300 opacity-5"
          style={{
            filter: "blur(64px)",
            transform: "translateZ(0)",
            willChange: "auto",
          }}
        ></div>
        <div
          className="fixed top-0 right-1/4 w-1/3 h-1/3 rounded-full bg-blue-400 opacity-5"
          style={{
            filter: "blur(64px)",
            transform: "translateZ(0)",
            willChange: "auto",
          }}
        ></div>
        <div
          className="fixed bottom-0 left-0 w-1/3 h-1/3 rounded-full bg-blue-500 opacity-5"
          style={{
            filter: "blur(64px)",
            transform: "translateZ(0)",
            willChange: "auto",
          }}
        ></div>
      </div>

      {/* Navbar */}
      {showNav && (
        <>
          {navType === "default" && <Navbar />} {/* Render Default Navbar */}
          {navType === "second" && <SecondNavbar />}{" "}
          {/* Render Second Navbar */}
        </>
      )}

      {/* Main Content - No animations to prevent layout shifts */}
      <main
        className={`relative z-10 ${className}`}
        style={{ contain: "content" }}
      >
        {children}
      </main>

      {/* Footer */}
      {showFooter && <Footer />}
    </div>
  );
}
