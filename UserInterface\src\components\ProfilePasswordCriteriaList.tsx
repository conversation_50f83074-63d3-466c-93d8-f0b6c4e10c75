import { Check, X } from "lucide-react";

interface ProfilePasswordCriteriaListProps {
  password: string;
  confirmPassword: string;
}

export function ProfilePasswordCriteriaList({
  password,
  confirmPassword,
}: ProfilePasswordCriteriaListProps) {
  // Si le mot de passe est vide, ne rien afficher
  if (!password) return null;

  // Critères de validation
  const hasMinLength = password.length >= 8;
  const hasMaxLength = password.length <= 15;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSpecialChar = /[!@#$%^&*]/.test(password);
  const passwordsMatch = password === confirmPassword && password !== "";

  // Tous les critères sont-ils remplis?
  const allCriteriaMet = 
    hasMinLength && 
    hasMaxLength && 
    hasUppercase && 
    hasLowercase && 
    hasNumber && 
    hasSpecialChar && 
    passwordsMatch;

  return (
    <div className="space-y-2 mt-2 mb-4">
      <h3 className="text-sm font-medium text-gray-700 mb-2">Exigences du mot de passe:</h3>
      
      <div className="space-y-1">
        <CriteriaItem 
          isValid={hasMinLength && hasMaxLength} 
          text="Entre 8 et 15 caractères" 
        />
        
        <CriteriaItem 
          isValid={hasUppercase} 
          text="Au moins une lettre majuscule" 
        />
        
        <CriteriaItem 
          isValid={hasLowercase} 
          text="Au moins une lettre minuscule" 
        />
        
        <CriteriaItem 
          isValid={hasNumber} 
          text="Au moins un chiffre" 
        />
        
        <CriteriaItem 
          isValid={hasSpecialChar} 
          text="Au moins un caractère spécial (!@#$%^&*)" 
        />
        
        {confirmPassword && (
          <CriteriaItem 
            isValid={passwordsMatch} 
            text="Les mots de passe correspondent" 
          />
        )}
      </div>
      
      {allCriteriaMet && (
        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md text-green-700 text-sm">
          Votre mot de passe répond à toutes les exigences de sécurité.
        </div>
      )}
    </div>
  );
}

// Composant pour afficher un critère individuel
function CriteriaItem({ isValid, text }: { isValid: boolean; text: string }) {
  return (
    <div className="flex items-center gap-2">
      {isValid ? (
        <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center">
          <Check className="w-3 h-3 text-green-600" />
        </div>
      ) : (
        <div className="w-5 h-5 rounded-full bg-red-100 flex items-center justify-center">
          <X className="w-3 h-3 text-red-500" />
        </div>
      )}
      <span className={`text-sm ${isValid ? "text-green-600" : "text-red-500"}`}>
        {text}
      </span>
    </div>
  );
}
