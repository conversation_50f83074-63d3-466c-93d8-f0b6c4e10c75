
import { cn } from "@/lib/utils";
import Card from "../ui-custom/Card";
import { LucideIcon } from "lucide-react";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  className?: string;
  iconColor?: string;
}

export default function FeatureCard({
  icon: Icon,
  title,
  description,
  className,
  iconColor = "text-sitechecker-blue",
}: FeatureCardProps) {
  return (
    <Card 
      className={cn("flex flex-col items-start", className)} 
      hoverEffect
    >
      <div className={cn("feature-icon", iconColor)}>
        <Icon className="w-5 h-5" />
      </div>
      
      <h3 className="mt-4 text-lg font-semibold text-gray-800">
        {title}
      </h3>
      
      <p className="mt-2 text-sm text-gray-600">
        {description}
      </p>
    </Card>
  );
}
