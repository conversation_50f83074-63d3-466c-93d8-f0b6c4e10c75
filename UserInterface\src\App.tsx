import { Toaster as Sonner } from "./components/ui/sonner"; // Corrected import path
import { TooltipProvider } from "./components/ui/tooltip"; // Corrected import path
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import Register from "./pages/Register"; // Importing Register component
import { Routes, Route, Navigate } from "react-router-dom";
import Index from "./pages/Index";
import Login from "./pages/Login";
import ForgotPassword from "./pages/ForgotPassword";
import VerifyCode from "./pages/VerifyCode";
import ResetPassword from "./pages/ResetPassword";
import SuccessReset from "./pages/SuccessReset";
import Dashboard from "./pages/Dashboard";
import CheckEmail from "./pages/CheckEmail";
import EmailVerified from "./pages/EmailVerified";
import NotFound from "./pages/NotFound";
import EmailVerificationError from "./pages/EmailVerificationError.tsx";
import AdminEmailVerified from "./pages/AdminEmailVerified.tsx";
import AdminDashboard from "./pages/AdminDashboard";
import Report from "./pages/Report.tsx";
import PaymentPlans from "./pages/PaymentPlans.tsx";
import UserProfile from "./pages/UserProfile";
import ProfileResetPassword from "./pages/ProfileResetPassword";
import PremiumDashboard from "./pages/PremiumDashboard";
import ProtectedRoute from "./components/ProtectedRoute";

const queryClient = new QueryClient();

import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";

const App = () => {
  return (
    <GoogleReCaptchaProvider
      reCaptchaKey={
        import.meta.env.VITE_RECAPTCHA_SITE_KEY ||
        "6LeGTiYrAAAAAKSljNVOkXUfgoN85XcRe9-0P29z"
      }
      scriptProps={{
        async: true,
        defer: true,
        appendTo: "head",
      }}
    >
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Sonner position="bottom-center" />
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/verify-code" element={<VerifyCode />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/success-reset" element={<SuccessReset />} />
            <Route path="/register" element={<Register />} />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route path="/check-email" element={<CheckEmail />} />
            <Route
              path="/admin-email-verified"
              element={<AdminEmailVerified />}
            />
            <Route path="/email-verified" element={<EmailVerified />} />
            <Route path="*" element={<NotFound />} />
            <Route
              path="/email-verification-error"
              element={<EmailVerificationError />}
            />
            <Route
              path="/report"
              element={
                <ProtectedRoute>
                  <Report />
                </ProtectedRoute>
              }
            />
            <Route
              path="/payment-plans"
              element={
                <ProtectedRoute>
                  <PaymentPlans />
                </ProtectedRoute>
              }
            />
            <Route
              path="/profile"
              element={
                <ProtectedRoute>
                  <UserProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/profile/ResetPassword"
              element={
                <ProtectedRoute>
                  <ProfileResetPassword />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin"
              element={
                <ProtectedRoute requiredRole="3">
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/premium-dashboard"
              element={
                <ProtectedRoute requiredRole="2">
                  <PremiumDashboard />
                </ProtectedRoute>
              }
            />
          </Routes>
        </TooltipProvider>
      </QueryClientProvider>
    </GoogleReCaptchaProvider>
  );
};

export default App;
