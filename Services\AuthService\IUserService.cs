using SiteCheckerApp.DTOs;
using SiteCheckerApp.Models;
using System.Threading.Tasks;

namespace SiteCheckerApp.Services
{
    public interface IUserService
    {
        Task<RegisterResponse> RegisterAsync(RegisterRequest request);
        Task<VerificationResult> VerifyUserAsync(string email, string verificationToken);
        Task<VerificationResult> ResendVerificationEmailAsync(string email);
        Task<LoginResponse> LoginAsync(LoginRequest request);
        Task RequestPasswordResetAsync(string email);
        Task<OtpVerificationResult> VerifyOtpAsync(string email, string otp);
        Task<bool> ResetPasswordAsync(string email, string newPassword);
        Task<RegisterResponse> RegisterAdminAsync(AdminRegisterRequest request, string secretKey);
        Task<User?> GetUserByEmailAsync(string email);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<User?> GetUserByIdAsync(string id);
        Task<Guid?> GetUserIdFromRefreshTokenAsync(string refreshToken);
        Task<User?> GetUserByIdAsync(Guid userId);

        // Admin functionality
        Task<List<User>> GetAllUsersAsync();
        Task<bool> UpdateUserRoleAsync(string userId, int newRole);
        Task<bool> UpdateUserStatusAsync(string userId, bool isActive);
        Task<PlatformStatsResponse> GetPlatformStatsAsync();

        Task<bool> VerifyPasswordAsync(User user, string password);
        Task UpdateUserAsync(User user);
    }
}
