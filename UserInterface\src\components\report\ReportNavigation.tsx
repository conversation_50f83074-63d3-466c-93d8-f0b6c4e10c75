import React, { useState, useEffect } from "react";
import {
  ChevronLeft,
  ChevronRight,
  Shield,
  Search,
  AlertTriangle,
  Menu,
  BarChart2,
  Download,
  ArrowLeft,
  ExternalLink,
  FileText,
  Code,
} from "lucide-react";
// No need for useNavigate as we're using onBack prop
// import { useNavigate } from "react-router-dom";

interface ReportNavigationProps {
  securityScore: number;
  seoScore: number;
  url: string;
  onBack: () => void;
  onDownload?: (format: "pdf" | "html") => void;
  scanDuration?: number;
  isPremiumOrAdmin?: boolean;
  isExpanded?: boolean;
  onExpandToggle?: (expanded: boolean) => void;
}

const ReportNavigation: React.FC<ReportNavigationProps> = ({
  securityScore,
  seoScore,
  url,
  onBack,
  onDownload,
  scanDuration,
  isPremiumOrAdmin = false,
  isExpanded: propIsExpanded,
  onExpandToggle,
}) => {
  // We don't need navigate as we're using onBack prop
  // const navigate = useNavigate();

  // Initialize expanded state from props or localStorage if available
  const [isExpanded, setIsExpanded] = useState(() => {
    if (propIsExpanded !== undefined) return propIsExpanded;
    const savedState = localStorage.getItem("reportNavExpanded");
    return savedState !== null ? savedState === "true" : true;
  });
  const [activeSection, setActiveSection] = useState<string | null>("overview");
  const [selectedFormat, setSelectedFormat] = useState<"pdf" | "html">("pdf");
  const [isDownloadExpanded, setIsDownloadExpanded] = useState<boolean>(false);

  // Save expanded state to localStorage when it changes and notify parent component
  useEffect(() => {
    localStorage.setItem("reportNavExpanded", isExpanded.toString());
    if (onExpandToggle) {
      onExpandToggle(isExpanded);
    }
  }, [isExpanded, onExpandToggle]);

  // Function to handle download with selected format
  const handleDownload = () => {
    if (onDownload) {
      onDownload(selectedFormat);
    }
  };

  // Track scroll position to highlight the active section using Intersection Observer
  useEffect(() => {
    // Create an Intersection Observer to detect which sections are in view
    const observerOptions = {
      root: null, // Use the viewport as the root
      rootMargin: "-10% 0px -70% 0px", // Adjust these values to control when a section is considered "in view"
      threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5], // Multiple thresholds for better accuracy
    };

    // This callback will be called whenever an observed element's visibility changes
    const observerCallback = (entries: IntersectionObserverEntry[]) => {
      // Find the most visible section (highest intersection ratio)
      const visibleEntries = entries.filter((entry) => entry.isIntersecting);

      if (visibleEntries.length > 0) {
        // Sort by intersection ratio (highest first)
        visibleEntries.sort(
          (a, b) => b.intersectionRatio - a.intersectionRatio
        );
        const mostVisibleEntry = visibleEntries[0];

        // Get the section ID from the element
        const sectionId = mostVisibleEntry.target.id;

        // Map section IDs to our navigation sections
        if (sectionId === "overview") {
          setActiveSection("overview");
        } else if (sectionId === "recommended-actions") {
          setActiveSection("actions");
        } else if (sectionId === "security-details") {
          setActiveSection("security");
        } else if (sectionId === "seo-details") {
          setActiveSection("seo");
        }
      }
    };

    // Create the observer
    const observer = new IntersectionObserver(
      observerCallback,
      observerOptions
    );

    // Get all section elements and observe them
    const sections = [
      document.getElementById("overview"),
      document.getElementById("recommended-actions"),
      document.getElementById("security-details"),
      document.getElementById("seo-details"),
    ];

    // Observe all sections that exist
    sections.forEach((section) => {
      if (section) {
        observer.observe(section);
      }
    });

    // Fallback for initial load or if IntersectionObserver isn't supported
    const handleScroll = () => {
      // Get all section elements
      const overviewSection = document.getElementById("overview");
      const actionsSection = document.getElementById("recommended-actions");
      const securitySection = document.getElementById("security-details");
      const seoSection = document.getElementById("seo-details");

      // Calculate which section is most visible
      const sections = [
        { id: "overview", element: overviewSection },
        { id: "actions", element: actionsSection },
        { id: "security", element: securitySection },
        { id: "seo", element: seoSection },
      ];

      // Filter out non-existent sections
      const existingSections = sections.filter((section) => section.element);

      // Find the section that's currently in view
      for (let i = existingSections.length - 1; i >= 0; i--) {
        const section = existingSections[i];
        if (section.element) {
          const { top, bottom } = section.element.getBoundingClientRect();
          // If the section is in the viewport
          if (top < window.innerHeight / 2 && bottom > 0) {
            setActiveSection(section.id);
            break;
          }
        }
      }
    };

    // Add scroll event listener as a fallback
    window.addEventListener("scroll", handleScroll);

    // Initial check
    handleScroll();

    return () => {
      // Clean up
      sections.forEach((section) => {
        if (section) {
          observer.unobserve(section);
        }
      });
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // We're using section-specific styles instead of a generic score color function
  // const getScoreColor = (score: number) => {
  //   if (score >= 80) return "text-green-600";
  //   if (score >= 50) return "text-amber-500";
  //   return "text-red-500";
  // };

  // Get section-specific colors for navigation items
  const getSectionStyles = (section: string) => {
    switch (section) {
      case "security":
        return {
          activeBg: "bg-blue-100/80",
          activeText: "text-blue-600",
          iconColor:
            activeSection === "security"
              ? "text-blue-600 drop-shadow-[0_0_4px_rgba(37,99,235,0.6)]"
              : "text-gray-600",
          scoreClass: "bg-blue-100 text-blue-600",
          hoverBg: "hover:bg-blue-50/70",
          gradientBorder: "from-blue-400 to-blue-600",
          glowColor: "rgba(37,99,235,0.4)",
        };
      case "seo":
        return {
          activeBg: "bg-neon-magenta/10",
          activeText: "text-neon-magenta",
          iconColor:
            activeSection === "seo"
              ? "text-neon-magenta drop-shadow-[0_0_4px_rgba(217,70,239,0.6)]"
              : "text-gray-600",
          scoreClass: "bg-neon-magenta/20 text-neon-magenta",
          hoverBg: "hover:bg-neon-magenta/5",
          gradientBorder: "from-fuchsia-400 to-pink-500",
          glowColor: "rgba(217,70,239,0.4)",
        };
      case "overview":
        return {
          activeBg: "bg-indigo-50/80",
          activeText: "text-indigo-600",
          iconColor:
            activeSection === "overview"
              ? "text-indigo-600 drop-shadow-[0_0_4px_rgba(79,70,229,0.6)]"
              : "text-gray-600",
          hoverBg: "hover:bg-indigo-50/50",
          gradientBorder: "from-indigo-400 to-indigo-600",
          glowColor: "rgba(79,70,229,0.4)",
        };
      case "actions":
        return {
          activeBg: "bg-amber-50/80",
          activeText: "text-amber-600",
          iconColor:
            activeSection === "actions"
              ? "text-amber-600 drop-shadow-[0_0_4px_rgba(217,119,6,0.6)]"
              : "text-gray-600",
          hoverBg: "hover:bg-amber-50/50",
          gradientBorder: "from-amber-400 to-amber-600",
          glowColor: "rgba(217,119,6,0.4)",
        };
      case "download":
        return {
          activeBg: "bg-cyan-50/80",
          activeText: "text-cyan-600",
          iconColor:
            activeSection === "download"
              ? "text-cyan-600 drop-shadow-[0_0_4px_rgba(8,145,178,0.6)]"
              : "text-gray-600",
          hoverBg: "hover:bg-cyan-50/50",
          gradientBorder: "from-cyan-400 to-cyan-600",
          glowColor: "rgba(8,145,178,0.4)",
        };
      default:
        return {
          activeBg: "bg-blue-50/80",
          activeText: "text-blue-600",
          iconColor: "text-gray-600",
          hoverBg: "hover:bg-gray-50/70",
          gradientBorder: "from-gray-400 to-gray-600",
          glowColor: "rgba(107,114,128,0.4)",
        };
    }
  };

  return (
    <nav
      id="report-sidebar"
      className={`fixed top-0 left-0 h-screen z-[1000] bg-white shadow-lg border-r border-gray-200 transition-all duration-300 ${
        isExpanded ? "w-64" : "w-16"
      } print:hidden`}
      style={{
        backgroundImage:
          "linear-gradient(135deg, rgba(255,255,255,0.97), rgba(249,250,251,0.97))",
        backdropFilter: "blur(10px)",
        willChange: "transform",
        transform: "translateZ(0)",
        position: "fixed",
        overflowY: "hidden",
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        zIndex: 1000,
      }}
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="sticky top-0 p-2 border-b border-gray-200/50 flex items-center justify-between bg-gradient-to-r from-blue-600/10 via-indigo-500/10 to-purple-600/10 z-[50] backdrop-blur-sm">
          {isExpanded ? (
            <div className="flex items-center">
              <div className="w-1.5 h-6 bg-gradient-to-b from-blue-500 to-indigo-600 rounded-full mr-2 shadow-[0_0_8px_rgba(37,99,235,0.5)]"></div>
              <h3 className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 drop-shadow-sm">
                Navigation
              </h3>
            </div>
          ) : (
            <div className="mx-auto flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-br from-blue-500/20 to-indigo-500/20 shadow-inner">
              <Menu className="h-4 w-4 text-blue-600 drop-shadow-[0_0_3px_rgba(37,99,235,0.3)]" />
            </div>
          )}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1.5 rounded-full hover:bg-white/50 transition-all hover:shadow-sm bg-gradient-to-br from-blue-100/30 to-indigo-100/30"
            title={isExpanded ? "Réduire" : "Développer"}
          >
            {isExpanded ? (
              <ChevronLeft className="h-4 w-4 text-blue-600" />
            ) : (
              <ChevronRight className="h-4 w-4 text-blue-600" />
            )}
          </button>
        </div>

        {/* Report Info moved to the end of the sidebar */}

        {/* Navigation Links */}
        <div
          className="flex-1 overflow-y-auto py-2 px-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
          style={{
            maxHeight: "calc(100vh - 100px)",
            overflowY: "auto",
            overscrollBehavior: "contain",
          }}
        >
          <div className="space-y-1">
            <a
              href="#overview"
              className={`flex items-center text-sm p-2 rounded-lg transition-all duration-300 ${
                activeSection === "overview"
                  ? `${getSectionStyles("overview").activeBg} ${
                      getSectionStyles("overview").activeText
                    } font-medium shadow-md`
                  : `text-gray-700 ${getSectionStyles("overview").hoverBg}`
              }`}
              onClick={(e) => {
                e.preventDefault();
                setActiveSection("overview");
                const element = document.getElementById("overview");
                if (element) {
                  element.scrollIntoView({ behavior: "smooth" });
                }
              }}
              data-active={activeSection === "overview"}
              title="Aperçu"
              style={{
                borderLeft:
                  activeSection === "overview"
                    ? "3px solid"
                    : "3px solid transparent",
                borderImage:
                  activeSection === "overview"
                    ? `linear-gradient(to bottom, ${
                        getSectionStyles("overview").gradientBorder
                      }) 1`
                    : "none",
                boxShadow:
                  activeSection === "overview"
                    ? `0 0 10px ${getSectionStyles("overview").glowColor}`
                    : "none",
              }}
            >
              <div className={`${isExpanded ? "mr-3" : "mx-auto"} relative`}>
                <div
                  className={`p-1.5 rounded-md ${
                    activeSection === "overview" ? "bg-indigo-100" : ""
                  }`}
                >
                  <BarChart2
                    className={`h-4 w-4 ${
                      getSectionStyles("overview").iconColor
                    } ${
                      activeSection === "overview"
                        ? "scale-110 transition-transform"
                        : ""
                    }`}
                  />
                </div>
                {!isExpanded && <div className="sidebar-tooltip">Aperçu</div>}
              </div>
              {isExpanded && <span className="font-medium">Aperçu</span>}
            </a>

            <a
              href="#recommended-actions"
              className={`flex items-center text-sm p-2 rounded-lg transition-all duration-300 ${
                activeSection === "actions"
                  ? `${getSectionStyles("actions").activeBg} ${
                      getSectionStyles("actions").activeText
                    } font-medium shadow-md`
                  : `text-gray-700 ${getSectionStyles("actions").hoverBg}`
              }`}
              onClick={(e) => {
                e.preventDefault();
                setActiveSection("actions");
                const element = document.getElementById("recommended-actions");
                if (element) {
                  element.scrollIntoView({ behavior: "smooth" });
                }
              }}
              data-active={activeSection === "actions"}
              title="Actions Recommandées"
              style={{
                borderLeft:
                  activeSection === "actions"
                    ? "3px solid"
                    : "3px solid transparent",
                borderImage:
                  activeSection === "actions"
                    ? `linear-gradient(to bottom, ${
                        getSectionStyles("actions").gradientBorder
                      }) 1`
                    : "none",
                boxShadow:
                  activeSection === "actions"
                    ? `0 0 10px ${getSectionStyles("actions").glowColor}`
                    : "none",
              }}
            >
              <div className={`${isExpanded ? "mr-3" : "mx-auto"} relative`}>
                <div
                  className={`p-1.5 rounded-md ${
                    activeSection === "actions" ? "bg-amber-100" : ""
                  }`}
                >
                  <AlertTriangle
                    className={`h-4 w-4 ${
                      getSectionStyles("actions").iconColor
                    } ${
                      activeSection === "actions"
                        ? "scale-110 transition-transform"
                        : ""
                    }`}
                  />
                </div>
                {!isExpanded && (
                  <div className="sidebar-tooltip">Actions Recommandées</div>
                )}
              </div>
              {isExpanded && (
                <span className="font-medium">Actions Recommandées</span>
              )}
            </a>

            <a
              href="#security-details"
              className={`flex items-center text-sm p-2 rounded-lg transition-all duration-300 ${
                activeSection === "security"
                  ? `${getSectionStyles("security").activeBg} ${
                      getSectionStyles("security").activeText
                    } font-medium shadow-md`
                  : `text-gray-700 ${getSectionStyles("security").hoverBg}`
              }`}
              onClick={(e) => {
                e.preventDefault();
                setActiveSection("security");
                const element = document.getElementById("security-details");
                if (element) {
                  element.scrollIntoView({ behavior: "smooth" });
                }
              }}
              data-active={activeSection === "security"}
              title={`Sécurité (Score: ${securityScore})`}
              style={{
                borderLeft:
                  activeSection === "security"
                    ? "3px solid"
                    : "3px solid transparent",
                borderImage:
                  activeSection === "security"
                    ? `linear-gradient(to bottom, ${
                        getSectionStyles("security").gradientBorder
                      }) 1`
                    : "none",
                boxShadow:
                  activeSection === "security"
                    ? `0 0 10px ${getSectionStyles("security").glowColor}`
                    : "none",
              }}
            >
              <div className={`${isExpanded ? "mr-3" : "mx-auto"} relative`}>
                <div
                  className={`p-1.5 rounded-md ${
                    activeSection === "security" ? "bg-blue-100" : ""
                  }`}
                >
                  <Shield
                    className={`h-4 w-4 ${
                      getSectionStyles("security").iconColor
                    } ${
                      activeSection === "security"
                        ? "scale-110 transition-transform"
                        : ""
                    }`}
                  />
                </div>
                {!isExpanded && (
                  <div className="sidebar-tooltip">
                    Sécurité{" "}
                    <span className="ml-1 font-medium">{securityScore}</span>
                  </div>
                )}
              </div>
              {isExpanded && (
                <>
                  <span className="font-medium">Sécurité</span>
                  <div
                    className={`ml-auto px-2 py-0.5 rounded-md ${
                      getSectionStyles("security").scoreClass
                    } text-xs font-medium shadow-inner`}
                  >
                    {securityScore}
                  </div>
                </>
              )}
            </a>

            <a
              href="#seo-details"
              className={`flex items-center text-sm p-2 rounded-lg transition-all duration-300 ${
                activeSection === "seo"
                  ? `${getSectionStyles("seo").activeBg} ${
                      getSectionStyles("seo").activeText
                    } font-medium shadow-md`
                  : `text-gray-700 ${getSectionStyles("seo").hoverBg}`
              }`}
              onClick={(e) => {
                e.preventDefault();
                setActiveSection("seo");
                const element = document.getElementById("seo-details");
                if (element) {
                  element.scrollIntoView({ behavior: "smooth" });
                }
              }}
              data-active={activeSection === "seo"}
              title={`SEO (Score: ${seoScore})`}
              style={{
                borderLeft:
                  activeSection === "seo"
                    ? "3px solid"
                    : "3px solid transparent",
                borderImage:
                  activeSection === "seo"
                    ? `linear-gradient(to bottom, ${
                        getSectionStyles("seo").gradientBorder
                      }) 1`
                    : "none",
                boxShadow:
                  activeSection === "seo"
                    ? `0 0 10px ${getSectionStyles("seo").glowColor}`
                    : "none",
              }}
            >
              <div className={`${isExpanded ? "mr-3" : "mx-auto"} relative`}>
                <div
                  className={`p-1.5 rounded-md ${
                    activeSection === "seo" ? "bg-fuchsia-100" : ""
                  }`}
                >
                  <Search
                    className={`h-4 w-4 ${getSectionStyles("seo").iconColor} ${
                      activeSection === "seo"
                        ? "scale-110 transition-transform"
                        : ""
                    }`}
                  />
                </div>
                {!isExpanded && (
                  <div className="sidebar-tooltip">
                    SEO <span className="ml-1 font-medium">{seoScore}</span>
                  </div>
                )}
              </div>
              {isExpanded && (
                <>
                  <span className="font-medium">SEO</span>
                  <div
                    className={`ml-auto px-2 py-0.5 rounded-md ${
                      getSectionStyles("seo").scoreClass
                    } text-xs font-medium shadow-inner`}
                  >
                    {seoScore}
                  </div>
                </>
              )}
            </a>
          </div>
        </div>

        {/* Rapport d'Analyse and Télécharger le Rapport sections just above the footer */}
        <div className="px-2 py-1">
          {/* Rapport d'Analyse section */}
          {isExpanded ? (
            <div className="relative mb-2">
              <div className="neo-card p-3 bg-gradient-to-br from-gray-50/80 to-blue-50/80 rounded-xl shadow-md border border-blue-100/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300">
                <div className="flex items-center mb-3">
                  <div className="w-1 h-4 bg-gradient-to-b from-blue-400 to-indigo-500 rounded-full mr-2 shadow-[0_0_5px_rgba(37,99,235,0.4)]"></div>
                  <h3 className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 drop-shadow-sm">
                    Rapport d'Analyse
                  </h3>
                </div>

                <div className="mb-2 bg-white/80 p-2 rounded-lg shadow-sm border border-blue-100/50 backdrop-blur-sm hover:shadow-md transition-all duration-300">
                  <p className="text-xs text-gray-600 truncate flex items-center">
                    <span className="flex-1 truncate">{url}</span>
                    <a
                      href={url.startsWith("http") ? url : `https://${url}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center ml-1 text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-50 transition-colors"
                      title="Visiter le site"
                    >
                      <ExternalLink className="w-3 h-3" />
                    </a>
                  </p>
                  {scanDuration !== undefined && (
                    <div className="flex items-center mt-1 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 px-2 py-1 rounded-md text-xs text-blue-600 border border-blue-100/50 shadow-inner">
                      <span className="flex-1 font-medium">
                        Analyse effectuée en {scanDuration} secondes
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setIsExpanded(true)}
              className="w-full flex items-center text-sm p-2 rounded-lg transition-all duration-300 text-gray-700 hover:bg-indigo-50/50 mb-1"
              title="Rapport d'Analyse"
            >
              <div className="mx-auto relative">
                <div className="p-1.5 rounded-md">
                  <FileText className="h-4 w-4 text-gray-600" />
                </div>
                <div className="sidebar-tooltip">Rapport d'Analyse</div>
              </div>
            </button>
          )}

          {/* Télécharger le Rapport section */}
          {isExpanded ? (
            <div className="relative mb-2">
              <div className="neo-card p-3 bg-gradient-to-br from-gray-50/80 to-cyan-50/80 rounded-xl shadow-md border border-cyan-100/50 backdrop-blur-sm hover:shadow-lg transition-all duration-300">
                <div
                  className="flex items-center mb-3 cursor-pointer"
                  onClick={() => setIsDownloadExpanded(!isDownloadExpanded)}
                >
                  <div className="w-1 h-4 bg-gradient-to-b from-cyan-400 to-blue-500 rounded-full mr-2 shadow-[0_0_5px_rgba(8,145,178,0.4)]"></div>
                  <h3 className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-cyan-600 to-blue-600 drop-shadow-sm flex-grow">
                    Télécharger le Rapport
                  </h3>
                  <ChevronRight
                    className={`h-4 w-4 text-cyan-600 transition-transform duration-300 ${
                      isDownloadExpanded ? "rotate-90" : ""
                    }`}
                  />
                </div>

                {isDownloadExpanded && (
                  <>
                    <p className="text-xs text-gray-600 mb-2 bg-white/50 p-1.5 rounded-md border border-cyan-50 shadow-inner">
                      Téléchargez ce rapport pour le consulter hors ligne ou le
                      partager.
                    </p>

                    <div className="flex flex-col gap-2">
                      {isPremiumOrAdmin ? (
                        <>
                          <button
                            onClick={() => {
                              setSelectedFormat("pdf");
                              handleDownload();
                            }}
                            className="flex items-center px-2 py-1.5 rounded-md transition-all bg-gradient-to-r from-cyan-600 to-blue-600 text-white text-xs hover:opacity-90 shadow-md hover:shadow-lg hover:translate-y-[-1px] active:translate-y-[1px]"
                          >
                            <FileText className="w-3 h-3 mr-1.5" />
                            Télécharger en PDF
                          </button>

                          <button
                            onClick={() => {
                              setSelectedFormat("html");
                              handleDownload();
                            }}
                            className="flex items-center px-2 py-1.5 rounded-md transition-all bg-gradient-to-r from-cyan-600 to-blue-600 text-white text-xs hover:opacity-90 shadow-md hover:shadow-lg hover:translate-y-[-1px] active:translate-y-[1px]"
                          >
                            <Code className="w-3 h-3 mr-1.5" />
                            Télécharger en HTML
                          </button>
                        </>
                      ) : (
                        <div className="bg-amber-50/80 border border-amber-200 rounded-md p-2 text-xs text-amber-800 shadow-inner backdrop-blur-sm">
                          <p className="flex items-center">
                            <AlertTriangle className="w-3 h-3 mr-1.5 text-amber-500 flex-shrink-0" />
                            Fonctionnalité premium
                          </p>
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>
          ) : (
            <button
              onClick={() => {
                setIsExpanded(true);
                // When expanding the sidebar, also expand the download section
                setIsDownloadExpanded(true);
              }}
              className={`w-full flex items-center text-sm p-2 rounded-lg transition-all duration-300 ${
                activeSection === "download"
                  ? `${getSectionStyles("download").activeBg} ${
                      getSectionStyles("download").activeText
                    } font-medium shadow-md`
                  : `text-gray-700 ${getSectionStyles("download").hoverBg}`
              }`}
              data-active={activeSection === "download"}
              title="Télécharger le rapport"
              style={{
                borderLeft:
                  activeSection === "download"
                    ? "3px solid"
                    : "3px solid transparent",
                borderImage:
                  activeSection === "download"
                    ? `linear-gradient(to bottom, ${
                        getSectionStyles("download").gradientBorder
                      }) 1`
                    : "none",
                boxShadow:
                  activeSection === "download"
                    ? `0 0 10px ${getSectionStyles("download").glowColor}`
                    : "none",
              }}
            >
              <div className="mx-auto relative">
                <div
                  className={`p-1.5 rounded-md ${
                    activeSection === "download" ? "bg-cyan-100" : ""
                  }`}
                >
                  <Download
                    className={`h-4 w-4 ${
                      getSectionStyles("download").iconColor
                    } ${
                      activeSection === "download"
                        ? "scale-110 transition-transform"
                        : ""
                    }`}
                  />
                </div>
                <div className="sidebar-tooltip">Télécharger</div>
              </div>
            </button>
          )}
        </div>

        {/* Footer */}
        <div className="sticky bottom-0 p-2 border-t border-gray-200/50 bg-gradient-to-r from-blue-600/10 via-indigo-500/10 to-purple-600/10 shadow-[0_-2px_10px_rgba(0,0,0,0.05)] z-[50] backdrop-blur-sm">
          {isExpanded ? (
            <button
              onClick={onBack}
              className="w-full flex items-center justify-center p-2 text-xs text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:opacity-90 rounded-lg transition-all duration-300 shadow-md hover:shadow-lg hover:translate-y-[-1px] active:translate-y-[1px]"
              title="Retour au tableau de bord"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              <span className="font-medium">Retour au tableau de bord</span>
            </button>
          ) : (
            <button
              onClick={() => setIsExpanded(true)}
              className="w-full flex items-center justify-center p-2 text-blue-600 hover:bg-white/50 rounded-lg transition-all duration-300 hover:shadow-sm bg-gradient-to-br from-blue-100/30 to-indigo-100/30"
              title="Développer le menu"
            >
              <ChevronRight className="h-4 w-4 drop-shadow-[0_0_3px_rgba(37,99,235,0.4)]" />
            </button>
          )}
        </div>
      </div>
    </nav>
  );
};

export default ReportNavigation;
