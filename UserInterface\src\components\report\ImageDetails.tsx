import React from "react";
import { ImageAnalysis } from "@/types/report";
import { Check, AlertTriangle, X, Image, HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// InfoTooltip Component - Reusable tooltip for technical terms
const InfoTooltip: React.FC<{ term: string; explanation: string }> = ({
  term,
  explanation,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-flex items-center cursor-help">
          {term}
          <HelpCircle className="h-3.5 w-3.5 ml-1 text-gray-400" />
        </span>
      </TooltipTrigger>
      <TooltipContent className="max-w-xs bg-gray-800 text-white border-gray-700">
        <p className="text-xs">{explanation}</p>
      </TooltipContent>
    </Tooltip>
  );
};

// Section Introduction Component
const SectionIntro: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="mb-4 p-3 neo-card bg-gray-50/50 border-l-4 border-blue-500/50">
      <p className="text-sm text-gray-600">{children}</p>
    </div>
  );
};

// Security Impact Component
const SecurityImpact: React.FC<{
  severity: "high" | "medium" | "low";
  children: React.ReactNode;
}> = ({ severity, children }) => {
  const bgColor =
    severity === "high"
      ? "bg-red-500/10 border-red-500/30"
      : severity === "medium"
      ? "bg-yellow-500/10 border-yellow-500/30"
      : "bg-blue-500/10 border-blue-500/30";

  const icon =
    severity === "high" ? (
      <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
    ) : severity === "medium" ? (
      <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />
    ) : (
      <HelpCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
    );

  return (
    <div className={`p-2 rounded-lg flex items-start gap-2 ${bgColor}`}>
      {icon}
      <p className="text-xs text-gray-700">{children}</p>
    </div>
  );
};

interface ImageDetailsProps {
  details?: ImageAnalysis;
}

export const ImageDetails: React.FC<ImageDetailsProps> = ({ details }) => {
  if (!details) {
    return <p className="text-white/70">Aucune donnée d'image disponible</p>;
  }

  const getAltTextStatus = () => {
    if (details.totalImages === 0) return "success";
    const ratio = details.imagesWithAlt / details.totalImages;
    if (ratio >= 0.9) return "success";
    if (ratio >= 0.7) return "warning";
    return "error";
  };

  const getCompressionStatus = () => {
    if (details.totalImages === 0) return "success";
    const ratio = details.imagesCompressed / details.totalImages;
    if (ratio >= 0.9) return "success";
    if (ratio >= 0.7) return "warning";
    return "error";
  };

  const getLazyLoadingStatus = () => {
    if (details.totalImages === 0 || details.totalImages <= 3) return "success";
    const ratio = details.imagesWithLazyLoading / details.totalImages;
    if (ratio >= 0.7) return "success";
    if (ratio >= 0.5) return "warning";
    return "error";
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <Check className="h-5 w-5 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case "error":
        return <X className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success":
        return "bg-green-500";
      case "warning":
        return "bg-yellow-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-blue-500";
    }
  };

  const altTextStatus = getAltTextStatus();
  const compressionStatus = getCompressionStatus();
  const lazyLoadingStatus = getLazyLoadingStatus();
  const largeImagesStatus =
    details.largeImages.length > 0 ? "warning" : "success";

  // Image optimization explanations
  const imageExplanations = {
    altText: {
      term: "Texte alternatif",
      explanation:
        "Texte descriptif qui s'affiche si l'image ne peut pas être chargée et qui est lu par les lecteurs d'écran pour les utilisateurs malvoyants.",
      impact:
        "Les images sans texte alternatif nuisent à l'accessibilité et au référencement, car les moteurs de recherche ne peuvent pas comprendre leur contenu.",
    },
    compression: {
      term: "Compression d'images",
      explanation:
        "Processus de réduction de la taille des fichiers d'images sans perte significative de qualité visuelle.",
      impact:
        "Les images non compressées ralentissent considérablement le chargement de votre page, ce qui peut augmenter le taux de rebond et nuire au référencement.",
    },
    lazyLoading: {
      term: "Chargement différé",
      explanation:
        "Technique qui retarde le chargement des images jusqu'à ce qu'elles soient sur le point d'être visibles dans la fenêtre d'affichage.",
      impact:
        "Sans chargement différé, toutes les images sont chargées immédiatement, ce qui ralentit le temps de chargement initial de la page.",
    },
    largeImages: {
      term: "Images surdimensionnées",
      explanation:
        "Images dont les dimensions en pixels sont beaucoup plus grandes que nécessaire pour leur affichage sur la page.",
      impact:
        "Les images surdimensionnées consomment inutilement de la bande passante et ralentissent le chargement de la page, affectant l'expérience utilisateur et le référencement.",
    },
  };

  return (
    <div className="space-y-6">
      <SectionIntro>
        L'optimisation des images est essentielle pour les performances de votre
        site et son référencement. Des images bien optimisées améliorent la
        vitesse de chargement, l'expérience utilisateur et l'accessibilité, tout
        en réduisant la consommation de bande passante.
      </SectionIntro>

      <div className="neo-card p-4">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-gray-800 font-medium">Résumé des images</h4>
          <span className="text-gray-600 text-sm">
            {details.totalImages} image{details.totalImages !== 1 ? "s" : ""}{" "}
            trouvée
            {details.totalImages !== 1 ? "s" : ""}
          </span>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="neo-card p-3">
            <div className="flex items-center justify-between mb-2">
              <InfoTooltip
                term={imageExplanations.altText.term}
                explanation={imageExplanations.altText.explanation}
              />
              {getStatusIcon(altTextStatus)}
            </div>
            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full ${getStatusColor(altTextStatus)}`}
                style={{
                  width: `${
                    (details.imagesWithAlt / Math.max(details.totalImages, 1)) *
                    100
                  }%`,
                }}
              ></div>
            </div>
            <p className="text-xs text-gray-600 mt-2">
              {details.imagesWithAlt} / {details.totalImages} avec texte alt
            </p>
          </div>

          <div className="neo-card p-3">
            <div className="flex items-center justify-between mb-2">
              <InfoTooltip
                term={imageExplanations.compression.term}
                explanation={imageExplanations.compression.explanation}
              />
              {getStatusIcon(compressionStatus)}
            </div>
            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full ${getStatusColor(compressionStatus)}`}
                style={{
                  width: `${
                    (details.imagesCompressed /
                      Math.max(details.totalImages, 1)) *
                    100
                  }%`,
                }}
              ></div>
            </div>
            <p className="text-xs text-gray-600 mt-2">
              {details.imagesCompressed} / {details.totalImages} compressées
            </p>
          </div>

          <div className="neo-card p-3">
            <div className="flex items-center justify-between mb-2">
              <InfoTooltip
                term={imageExplanations.lazyLoading.term}
                explanation={imageExplanations.lazyLoading.explanation}
              />
              {getStatusIcon(lazyLoadingStatus)}
            </div>
            <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className={`h-full ${getStatusColor(lazyLoadingStatus)}`}
                style={{
                  width: `${
                    (details.imagesWithLazyLoading /
                      Math.max(details.totalImages, 1)) *
                    100
                  }%`,
                }}
              ></div>
            </div>
            <p className="text-xs text-gray-600 mt-2">
              {details.imagesWithLazyLoading} / {details.totalImages} avec lazy
              loading
            </p>
          </div>

          <div className="neo-card p-3">
            <div className="flex items-center justify-between mb-2">
              <InfoTooltip
                term={imageExplanations.largeImages.term}
                explanation={imageExplanations.largeImages.explanation}
              />
              {getStatusIcon(largeImagesStatus)}
            </div>
            <div className="flex items-center">
              <span className="text-gray-600 text-sm">
                {details.largeImages.length} image
                {details.largeImages.length !== 1 ? "s" : ""} surdimensionnée
                {details.largeImages.length !== 1 ? "s" : ""}
              </span>
            </div>
          </div>
        </div>
      </div>

      {details.missingAltImages.length > 0 && (
        <div className="neo-card p-4">
          <h4 className="text-gray-800 font-medium mb-3">
            Images sans texte alternatif
          </h4>
          <div className="space-y-2">
            {details.missingAltImages.map((src, index) => (
              <div
                key={index}
                className="flex items-center gap-2 text-sm text-gray-600 overflow-hidden neo-card p-2"
              >
                <Image className="w-4 h-4 flex-shrink-0 text-neon-magenta" />
                <span className="truncate">{src}</span>
              </div>
            ))}
            {details.imagesWithoutAlt > details.missingAltImages.length && (
              <p className="text-xs text-gray-500 italic mt-2">
                Et {details.imagesWithoutAlt - details.missingAltImages.length}{" "}
                autres...
              </p>
            )}
          </div>
        </div>
      )}

      {details.largeImages.length > 0 && (
        <div className="neo-card p-4">
          <h4 className="text-gray-800 font-medium mb-3">
            Images surdimensionnées
          </h4>
          <div className="space-y-2">
            {details.largeImages.map((src, index) => (
              <div
                key={index}
                className="flex items-center gap-2 text-sm text-gray-600 overflow-hidden neo-card p-2"
              >
                <Image className="w-4 h-4 flex-shrink-0 text-neon-magenta" />
                <span className="truncate">{src}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-4">
        <h4 className="text-sm font-medium mb-2 text-gray-700">
          Impact sur les performances et le SEO
        </h4>

        {altTextStatus !== "success" && (
          <SecurityImpact severity="high">
            {imageExplanations.altText.impact}
          </SecurityImpact>
        )}

        {compressionStatus !== "success" && (
          <SecurityImpact severity="high">
            {imageExplanations.compression.impact}
          </SecurityImpact>
        )}

        {lazyLoadingStatus !== "success" && (
          <SecurityImpact severity="medium">
            {imageExplanations.lazyLoading.impact}
          </SecurityImpact>
        )}

        {details.largeImages.length > 0 && (
          <SecurityImpact severity="medium">
            {imageExplanations.largeImages.impact}
          </SecurityImpact>
        )}
      </div>

      <div className="neo-card p-4">
        <h4 className="text-gray-800 font-medium mb-3">Recommandations</h4>
        <ul className="space-y-3">
          {altTextStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Ajoutez des textes alternatifs descriptifs à toutes les images
                pour améliorer l'accessibilité et le référencement.
              </span>
            </li>
          )}
          {compressionStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Optimisez les images non compressées en utilisant des formats
                comme WebP, AVIF ou JPEG optimisé.
              </span>
            </li>
          )}
          {lazyLoadingStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Implémentez le chargement différé (lazy loading) pour les images
                situées en bas de page afin d'améliorer les performances.
              </span>
            </li>
          )}
          {details.largeImages.length > 0 && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Redimensionnez les images surdimensionnées aux dimensions
                appropriées pour l'affichage web.
              </span>
            </li>
          )}
          {altTextStatus === "success" &&
            compressionStatus === "success" &&
            lazyLoadingStatus === "success" &&
            details.largeImages.length === 0 && (
              <li className="flex items-start gap-2 neo-card p-3 border border-green-500/30">
                <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 text-sm">
                  Toutes les images sont bien optimisées. Excellent travail !
                </span>
              </li>
            )}
        </ul>
      </div>
    </div>
  );
};

export default ImageDetails;
