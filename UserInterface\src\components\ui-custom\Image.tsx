import React from 'react';
import { cn } from '@/lib/utils';

interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

/**
 * Custom Image component that ensures proper dimensions and lazy loading
 * to prevent layout shifts
 */
const Image: React.FC<ImageProps> = ({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  ...props
}) => {
  // Calculate aspect ratio if both width and height are provided
  const aspectRatio = width && height ? width / height : undefined;
  
  return (
    <div 
      className={cn("overflow-hidden", className)}
      style={{
        width: width ? `${width}px` : '100%',
        height: height ? `${height}px` : 'auto',
        aspectRatio: aspectRatio,
        position: 'relative',
      }}
    >
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
        style={{
          objectFit: 'cover',
          width: '100%',
          height: '100%',
        }}
        {...props}
      />
    </div>
  );
};

export default Image;
