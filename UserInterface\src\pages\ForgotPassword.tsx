import { useState, useEffect } from "react";
import { safeStorage } from "../utils/storage";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { ArrowRight, Mail } from "lucide-react";
import Layout from "../components/Layout";
import Card from "../components/ui-custom/Card";
import Input from "../components/ui-custom/Input";
import Button from "../components/ui-custom/Button";
import { toast } from "sonner";
import axios from "axios";

// Base API URL
const API_BASE_URL = "http://localhost:5012"; // Adjust this to match your API server URL

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const [submitted, setSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isAdminVerification, setIsAdminVerification] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // State to track toast messages
  const [toastMessage, setToastMessage] = useState<{
    type: "success" | "error" | "info";
    message: string;
  } | null>(null);

  // Effect to show toast messages
  useEffect(() => {
    if (toastMessage) {
      if (toastMessage.type === "success") {
        toast.success(toastMessage.message);
      } else if (toastMessage.type === "info") {
        toast.info(toastMessage.message);
      } else {
        toast.error(toastMessage.message);
      }
      setToastMessage(null);
    }
  }, [toastMessage]);

  useEffect(() => {
    const initializeComponent = async () => {
      // Check if this page is loaded for admin verification
      const searchParams = new URLSearchParams(location.search);
      const isAdmin = searchParams.get("isAdmin") === "true";
      setIsAdminVerification(isAdmin);

      // Pre-fill email for admin
      if (isAdmin) {
        try {
          const adminEmail = await safeStorage.getItem("adminEmail");
          if (adminEmail) {
            setEmail(adminEmail);
            setSubmitted(true); // Show the confirmation screen right away
          } else {
            // If admin flag is set but no email is found, return to login
            setToastMessage({
              type: "error",
              message: "Session expirée Veuillez vous reconnecter",
            });
            navigate("/login");
          }
        } catch (error) {
          console.error("Error loading admin email:", error);
          setToastMessage({
            type: "error",
            message: "Une erreur est survenue",
          });
          navigate("/login");
        }
      }
    };

    initializeComponent();
  }, [location.search, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      setToastMessage({
        type: "error",
        message: "Veuillez entrer votre adresse email",
      });
      return;
    }

    setLoading(true);
    try {
      await axios.post(`${API_BASE_URL}/api/auth/request-password-reset`, {
        email,
      });
      await safeStorage.setItem("resetEmail", email);
      setSubmitted(true);
      setToastMessage({
        type: "success",
        message: "Un code de vérification a été envoyé à votre adresse email.",
      });
    } catch (error: any) {
      setToastMessage({
        type: "error",
        message:
          error.response?.data?.message || "Erreur lors de l'envoi du code.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = async () => {
    if (isAdminVerification) {
      navigate("/verify-code?isAdmin=true");
    } else {
      await safeStorage.setItem("resetEmail", email);
      navigate("/verify-code");
    }
  };

  if (submitted) {
    return (
      <Layout showNav={true} navType="second">
        <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
          <Card className="w-full max-w-md animate-scale-in text-center">
            <Mail className="w-12 h-12 text-sitechecker-blue mx-auto mb-4" />

            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              {isAdminVerification
                ? "Vérification Administrateur"
                : "Vérifiez votre boîte mail"}
            </h1>

            <p className="text-gray-600 mb-6">
              Nous avons envoyé un code de vérification à{" "}
              <strong>{email}</strong>. Veuillez vérifier votre boîte de
              réception.
            </p>

            <Button className="w-full" onClick={handleContinue}>
              Continuer
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>

            {isAdminVerification && (
              <div className="mt-4 text-center">
                <button
                  className="text-sm text-sitechecker-blue hover:underline"
                  onClick={async () => {
                    await safeStorage.removeItem("adminEmail");
                    await safeStorage.removeItem("adminPassword");
                    await safeStorage.removeItem("isAdminLogin");
                    navigate("/login");
                  }}
                >
                  Retour à la connexion
                </button>
              </div>
            )}
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showNav={true} navType="second">
      <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
        <Card className="w-full max-w-md animate-scale-in">
          <h1 className="text-2xl font-bold text-center text-gray-800 mb-6">
            Réinitialisation du mot de passe
          </h1>

          <form onSubmit={handleSubmit} className="space-y-4">
            <p className="text-gray-600 text-sm mb-4">
              Entrez l'adresse email associée à votre compte et nous vous
              enverrons un code de vérification pour réinitialiser votre mot de
              passe.
            </p>

            <Input
              type="email"
              placeholder="Adresse email"
              icon="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />

            <Button
              type="submit"
              className="w-full"
              loading={loading}
              withArrow
            >
              Envoyer le code
            </Button>
          </form>

          <div className="mt-6 text-center">
            <Link
              to="/login"
              className="text-sm text-sitechecker-blue hover:underline"
            >
              Retour à la connexion
            </Link>
          </div>
        </Card>
      </div>
    </Layout>
  );
}
