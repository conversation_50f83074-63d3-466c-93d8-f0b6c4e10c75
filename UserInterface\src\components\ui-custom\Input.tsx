import { cn } from "../../lib/utils"; // Corrected import path
import { Mail, Lock, Eye, EyeOff } from "lucide-react";
import { InputHTMLAttributes, forwardRef, useState } from "react";

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  icon?: "email" | "password" | "none" | React.ReactNode;
  label?: string;
  error?: string;
  helpText?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type = "text",
      icon = "none",
      label,
      error,
      helpText,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const inputType = type === "password" && showPassword ? "text" : type;

    const iconMap = {
      email: <Mail className="w-5 h-5 text-gray-400" />,
      password: <Lock className="w-5 h-5 text-gray-400" />,
      none: null,
    };

    const togglePasswordVisibility = () => {
      setShowPassword((prev) => !prev);
    };

    return (
      <div className="w-full space-y-1.5">
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {label}
          </label>
        )}
        <div
          className={cn(
            "relative flex items-center w-full rounded-lg border border-gray-200 bg-white input-focus-ring transition-all duration-200 focus-within:shadow-sm",
            error && "border-red-500",
            className
          )}
        >
          {icon !== "none" && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2">
              {typeof icon === "string"
                ? iconMap[icon as keyof typeof iconMap]
                : icon}
            </div>
          )}

          <input
            ref={ref}
            type={inputType}
            className={cn(
              "w-full px-4 py-3 bg-transparent outline-none rounded-lg text-gray-800 placeholder:text-gray-400",
              icon !== "none" ? "pl-10" : "",
              type === "password" && "pr-10"
            )}
            {...props}
          />

          {type === "password" && (
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
              onClick={togglePasswordVisibility}
            >
              {showPassword ? (
                <EyeOff className="w-5 h-5" />
              ) : (
                <Eye className="w-5 h-5" />
              )}
            </button>
          )}
        </div>
        {error && (
          <p className="text-sm text-red-500 mt-1 animate-fade-in">{error}</p>
        )}
        {helpText && !error && (
          <p className="text-sm text-gray-500 mt-1">{helpText}</p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export default Input;
