import React, { ReactNode, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  Shield,
  Search,
  Lock,
  Globe,
  FileCode,
  Server,
  Database,
  Image,
  Smartphone,
  Gauge,
  FileText,
} from "lucide-react";

// Import interfaces from interfaces.ts
import { TechnicalSectionItem } from "./interfaces";

// Import UnifiedTechnicalSectionCollapsible component
import UnifiedTechnicalSectionCollapsible from "./UnifiedTechnicalSectionCollapsible";

interface TechnicalSectionProps {
  title: string;
  icon: ReactNode;
  type: "security" | "seo";
  items: TechnicalSectionItem[];
}

const TechnicalSection: React.FC<TechnicalSectionProps> = ({
  title,
  icon,
  type,
  items,
}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState(items[0]?.id || "");
  const contentRef = useRef<HTMLDivElement>(null);

  // Auto-assign icons if not provided
  const itemsWithIcons = items.map((item) => {
    if (item.icon) return item;

    // Default icons based on common section titles
    let defaultIcon: ReactNode;
    const lowerTitle = item.title.toLowerCase();

    if (lowerTitle.includes("ssl") || lowerTitle.includes("tls")) {
      defaultIcon = <Lock className="w-4 h-4" />;
    } else if (lowerTitle.includes("header")) {
      defaultIcon = <Globe className="w-4 h-4" />;
    } else if (lowerTitle.includes("vulnerab")) {
      defaultIcon = <Shield className="w-4 h-4" />;
    } else if (lowerTitle.includes("js") || lowerTitle.includes("javascript")) {
      defaultIcon = <FileCode className="w-4 h-4" />;
    } else if (lowerTitle.includes("api")) {
      defaultIcon = <Server className="w-4 h-4" />;
    } else if (lowerTitle.includes("meta")) {
      defaultIcon = <Database className="w-4 h-4" />;
    } else if (lowerTitle.includes("mobile")) {
      defaultIcon = <Smartphone className="w-4 h-4" />;
    } else if (lowerTitle.includes("performance")) {
      defaultIcon = <Gauge className="w-4 h-4" />;
    } else if (lowerTitle.includes("content")) {
      defaultIcon = <FileText className="w-4 h-4" />;
    } else if (lowerTitle.includes("image")) {
      defaultIcon = <Image className="w-4 h-4" />;
    } else {
      defaultIcon =
        type === "security" ? (
          <Shield className="w-4 h-4" />
        ) : (
          <Search className="w-4 h-4" />
        );
    }

    return { ...item, icon: defaultIcon };
  });

  // Determine colors based on type
  const getGradient = () => {
    return type === "security"
      ? "from-neon-purple to-neon-blue"
      : "from-neon-magenta to-neon-blue";
  };

  // Get background pattern based on type
  const getBackgroundPattern = () => {
    return type === "security"
      ? "bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] bg-[length:20px_20px]"
      : "bg-[linear-gradient(45deg,#f3f4f6_25%,transparent_25%,transparent_50%,#f3f4f6_50%,#f3f4f6_75%,transparent_75%,transparent)] bg-[length:20px_20px]";
  };

  // Get border color based on type
  const getBorderColor = () => {
    return type === "security" ? "border-neon-purple" : "border-neon-magenta";
  };

  // Get score color for progress indicators
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <div className={`relative ${getBackgroundPattern()} bg-opacity-5`}>
      {/* Section Header - Only show if title is not hidden */}
      {title !== "hidden-title" && (
        <div
          className={`flex items-center mb-2 neo-card p-2 bg-gradient-to-r from-gray-50 to-gray-100 border-l-4 border-r-4 ${getBorderColor()} border-r-neon-blue shadow-md`}
        >
          <div
            className={`rounded-full p-1 ${
              type === "security" ? "bg-neon-purple/20" : "bg-neon-magenta/20"
            } mr-1.5`}
          >
            {icon}
          </div>
          <h3 className="text-base font-bold">
            <span
              className={`bg-gradient-to-r ${getGradient()} text-transparent bg-clip-text`}
            >
              {title}
            </span>
          </h3>
        </div>
      )}

      {/* Horizontal Layout */}
      <div className="flex flex-col gap-2">
        {/* Horizontal Navigation Tabs */}
        <div className="neo-card bg-white p-2 overflow-x-auto">
          <ul className="flex space-x-1 min-w-max">
            {itemsWithIcons.map((item) => (
              <li key={item.id} className="flex-shrink-0">
                <button
                  onClick={() => setActiveTab(item.id)}
                  className={`px-2 py-1 rounded-lg transition-all flex items-center text-xs ${
                    activeTab === item.id
                      ? `bg-gradient-to-r ${getGradient()} text-white shadow-sm`
                      : "text-gray-700 hover:bg-gray-100 border border-gray-200"
                  }`}
                >
                  <span className="mr-1">{item.icon}</span>
                  <span>
                    {t(
                      item.title.toLowerCase().includes("ssl")
                        ? "security.ssl"
                        : item.title.toLowerCase().includes("header")
                        ? "security.headers"
                        : item.title.toLowerCase().includes("vulnerab")
                        ? "security.vulnerabilities"
                        : item.title.toLowerCase().includes("javascript") ||
                          item.title.toLowerCase().includes("js")
                        ? "security.javascript"
                        : item.title.toLowerCase().includes("api")
                        ? "security.api"
                        : item.title.toLowerCase().includes("meta")
                        ? "seo.meta"
                        : item.title.toLowerCase().includes("mobile")
                        ? "seo.mobile"
                        : item.title.toLowerCase().includes("image")
                        ? "seo.images"
                        : item.title.toLowerCase().includes("performance")
                        ? "seo.performance"
                        : item.title.toLowerCase().includes("content")
                        ? "seo.content"
                        : item.title.toLowerCase().includes("url")
                        ? "seo.url"
                        : item.title
                    )}
                  </span>
                  {item.score !== undefined && (
                    <span
                      className={`ml-2 px-1.5 py-0.5 rounded-full text-xs ${
                        activeTab === item.id
                          ? "bg-white/20 text-white"
                          : item.score >= 80
                          ? "bg-green-100 text-green-700"
                          : item.score >= 60
                          ? "bg-yellow-100 text-yellow-700"
                          : "bg-red-100 text-red-700"
                      }`}
                    >
                      {item.score}
                    </span>
                  )}
                </button>
              </li>
            ))}
          </ul>
        </div>

        {/* Content Area - Full width */}
        <div className="w-full" ref={contentRef}>
          <div className="neo-card p-2 bg-white shadow-md">
            {itemsWithIcons.map((item) => (
              <div
                key={`content-${item.id}`}
                className={activeTab === item.id ? "block" : "hidden"}
              >
                {item.useUnifiedModel ? (
                  // Use the new UnifiedTechnicalSectionCollapsible component
                  <UnifiedTechnicalSectionCollapsible
                    type={type}
                    icon={item.icon}
                    title={item.title}
                    score={item.score || 0}
                    impactStatement={
                      item.impactStatement || {
                        text: t("report.no_info"),
                      }
                    }
                    findings={item.findings || []}
                    technicalDetails={item.technicalDetails || []}
                    actionReference={
                      item.actionReference || {
                        count: 0,
                        sectionName: item.title,
                        actionWord: t("report.view"),
                        targetId: "actions",
                      }
                    }
                    hideTitle={true}
                  />
                ) : (
                  // Legacy Layout
                  <>
                    <div className="flex items-center mb-1 pb-1 border-b border-gray-200">
                      <span
                        className={`p-0.5 rounded mr-1 ${
                          type === "security"
                            ? "bg-neon-purple/20"
                            : "bg-neon-magenta/20"
                        }`}
                      >
                        {item.icon}
                      </span>
                      <h4 className="text-sm font-medium text-gray-800">
                        {item.title}
                      </h4>

                      {/* Score inline with title for better space usage */}
                      {item.score !== undefined && (
                        <div className="ml-auto flex items-center">
                          <span className="text-xs font-medium mr-2">
                            Score: {Math.round(item.score)}%
                          </span>
                          <div className="w-20 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                            <div
                              className={`h-full ${getScoreColor(item.score)}`}
                              style={{ width: `${Math.round(item.score)}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="content-section">{item.content}</div>
                  </>
                )}
              </div>
            ))}

            {!itemsWithIcons.find((item) => item.id === activeTab) && (
              <div className="text-center py-2 text-gray-600 text-xs">
                <p>Sélectionnez une section pour voir les détails techniques</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TechnicalSection;
