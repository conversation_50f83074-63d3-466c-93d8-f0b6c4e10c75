using System.ComponentModel.DataAnnotations;

namespace SiteCheckerApp.DTOs
{
    public class AdminRegisterRequest
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [Required]
        [StringLength(100, MinimumLength = 8)]
        public string Password { get; set; } = string.Empty;

        // Secret key to authorize admin registration
        [Required]
        public string SecretKey { get; set; } = string.Empty;
    }
}