using System;
using System.ComponentModel.DataAnnotations;

namespace SiteCheckerApp.Models
{
    public class ScanHistory
    {
        [Key]
        public Guid Id { get; set; }

        public Guid UserId { get; set; }

        [Required]
        [MaxLength(500)]
        public required string Url { get; set; }

        [Required]
        [MaxLength(50)]
        public required string ScanType { get; set; } // "security" or "seo"

        [Required]
        [MaxLength(50)]
        public required string ScanDepth { get; set; } // "basic" or "comprehensive"

        public int? Score { get; set; }

        [Required]
        public DateTime ScanDate { get; set; } = DateTime.UtcNow;

        public bool IsSuccessful { get; set; } = true;

        [MaxLength(500)]
        public string? ErrorMessage { get; set; }
    }
}
