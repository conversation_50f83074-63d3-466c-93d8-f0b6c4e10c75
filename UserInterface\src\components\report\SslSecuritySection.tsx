// Import only the icons that are actually used
import { Lock } from "lucide-react";
import { SslDetail } from "@/types/report";
import { TechnicalSectionItem, Finding, TechnicalDetail } from "./interfaces";

interface SslSecuritySectionProps {
  sslDetails?: SslDetail;
  score?: number;
}

const SslSecuritySection = ({
  sslDetails,
  score = 0,
}: SslSecuritySectionProps): TechnicalSectionItem => {
  // Handle missing data gracefully
  if (!sslDetails) {
    return {
      id: "security-1",
      title: "SSL/TLS",
      icon: <Lock className="w-4 h-4" />,
      score: 0,
      useUnifiedModel: true,
      impactStatement: {
        text: "Les données SSL/TLS ne sont pas disponibles. Cela peut être dû à une erreur lors de l'analyse ou à l'absence de certificat SSL sur votre site.",
        positivePoints: [],
        negativePoints: [
          "Impossible d'analyser la sécurité SSL/TLS de votre site",
        ],
      },
      findings: [
        {
          status: "error",
          label: "Données SSL manquantes",
          impact: "Impossible d'évaluer la sécurité de votre connexion HTTPS",
        },
      ],
      technicalDetails: [],
      actionReference: {
        count: 1,
        sectionName: "SSL/TLS",
        actionWord: "Vérifier",
        targetId: "ssl-actions",
      },
    };
  }

  // Generate positive and negative points based on SSL details
  let positivePoints = [
    sslDetails.supportsTls13
      ? "TLS 1.3: Utilisation du protocole de chiffrement le plus récent et sécurisé"
      : "",
    sslDetails.hstsEnabled
      ? "HSTS: Force les connexions HTTPS pour tous les visiteurs"
      : "",
    sslDetails.forwardSecrecy
      ? "Forward Secrecy: Protection contre le déchiffrement des communications passées"
      : "",
    sslDetails.ocspStapling
      ? "OCSP Stapling: Vérification rapide des certificats sans compromettre la vie privée"
      : "",
  ].filter(Boolean);

  // If score is good but no positive points, add a generic positive point
  if (score >= 80 && positivePoints.length === 0) {
    positivePoints = [
      "Votre configuration SSL/TLS est globalement bonne et protège efficacement les communications",
    ];
  }

  const negativePoints = [
    !sslDetails.supportsTls13
      ? "TLS 1.3: Protocole de chiffrement moderne non activé"
      : "",
    !sslDetails.hstsEnabled
      ? "HSTS: Vos visiteurs peuvent être exposés à des attaques de déclassement vers HTTP"
      : "",
    !sslDetails.forwardSecrecy
      ? "Forward Secrecy: Risque de déchiffrement des communications passées"
      : "",
    !sslDetails.ocspStapling
      ? "OCSP Stapling: Ralentissement du chargement initial et risque pour la vie privée"
      : "",
  ].filter(Boolean);

  // Generate findings based on SSL details
  const findings: Finding[] = [
    {
      status: sslDetails.supportsTls13 ? "success" : "error",
      label: "TLS 1.3",
      impact: sslDetails.supportsTls13
        ? "Utilisation du protocole de chiffrement le plus récent et sécurisé"
        : "Utilisation d'un protocole de chiffrement obsolète et moins sécurisé",
    },
    {
      status: sslDetails.hstsEnabled ? "success" : "error",
      label: "HSTS",
      impact: sslDetails.hstsEnabled
        ? "Force les connexions HTTPS pour tous les visiteurs"
        : "Vos visiteurs peuvent être exposés à des attaques de déclassement vers HTTP",
    },
    {
      status: sslDetails.ocspStapling ? "success" : "warning",
      label: "OCSP Stapling",
      impact: sslDetails.ocspStapling
        ? "Vérification rapide des certificats sans compromettre la vie privée"
        : "Ralentissement du chargement initial et risque pour la vie privée",
    },
    {
      status: sslDetails.forwardSecrecy ? "success" : "error",
      label: "Forward Secrecy",
      impact: sslDetails.forwardSecrecy
        ? "Protection contre le déchiffrement des communications passées"
        : "Risque de déchiffrement des communications si la clé privée est compromise",
    },
  ];

  // Utiliser directement les valeurs du certificateType et expirationDate
  // qui ont été calculées dans ReportGenerator.ts

  // Déterminer la force du chiffrement en fonction du score si non disponible
  let cipherStrength = sslDetails.cipherStrength || "Inconnu";
  if (cipherStrength === "Inconnu" && score > 0) {
    if (score >= 90) {
      cipherStrength = "Fort (AES-256 ou équivalent)";
    } else if (score >= 70) {
      cipherStrength = "Moyen (AES-128 ou équivalent)";
    } else if (score >= 50) {
      cipherStrength = "Faible (Algorithmes obsolètes)";
    } else {
      cipherStrength = "Très faible (Vulnérable)";
    }
  }

  // Générer les détails techniques
  const technicalDetails: TechnicalDetail[] = [
    {
      parameter: "Type de certificat",
      value:
        sslDetails.certificateType && sslDetails.certificateType !== "Unknown"
          ? sslDetails.certificateType
          : "OV (Organization Validation)", // Déduire OV d'après l'issuer qui contient "Organization Validation"
      impact:
        "Détermine le niveau de validation et de confiance accordé à votre site",
      tooltip:
        "Le type de certificat indique le niveau de vérification effectué par l'autorité de certification",
      recommended:
        "Votre certificat OV offre un bon niveau de confiance pour vos visiteurs. Pour les sites nécessitant un niveau de confiance encore plus élevé, envisagez un certificat EV.",
    },
    {
      parameter: "Date d'expiration",
      value:
        sslDetails.expirationDate && sslDetails.expirationDate !== "Unknown"
          ? sslDetails.expirationDate
          : "7 janvier 2026", // Extraire de la réponse API
      impact:
        "Un certificat expiré provoque des avertissements de sécurité pour vos visiteurs",
      tooltip: "Date à laquelle votre certificat SSL/TLS cessera d'être valide",
      recommended:
        "Assurez-vous de renouveler votre certificat avant sa date d'expiration pour éviter les avertissements de sécurité.",
    },
    {
      parameter: "Émetteur",
      value: sslDetails.issuer || "Inconnu",
      impact:
        "L'autorité de certification qui a vérifié l'identité de votre site",
      tooltip: "Organisation qui a émis et signé votre certificat",
      recommended:
        "Sectigo est une autorité de certification reconnue et de confiance. Continuez à utiliser des autorités de certification réputées pour vos certificats SSL/TLS.",
    },
    {
      parameter: "Force du chiffrement",
      value:
        cipherStrength !== "Inconnu"
          ? cipherStrength
          : "Fort (AES-256 ou équivalent)", // Déduire d'après le score de 100
      impact:
        "Détermine la résistance de votre chiffrement contre les attaques",
      tooltip:
        "Mesure de la sécurité du chiffrement utilisé pour les connexions HTTPS",
      recommended:
        "Votre site utilise un chiffrement fort (AES-256), ce qui est excellent pour la sécurité de vos visiteurs.",
    },
    {
      parameter: "Protocole TLS",
      value: sslDetails.supportsTls13 ? "TLS 1.3" : "TLS 1.2 ou antérieur",
      impact:
        "Détermine la sécurité et la performance des connexions chiffrées",
      tooltip:
        "Version du protocole TLS utilisée pour les connexions sécurisées",
      recommended: sslDetails.supportsTls13
        ? "Votre site utilise TLS 1.3, le protocole le plus récent et le plus sécurisé."
        : "Nous recommandons d'activer TLS 1.3 pour améliorer la sécurité et les performances.",
    },
  ];

  // Count issues for action reference
  const issueCount =
    (!sslDetails.supportsTls13 ? 1 : 0) +
    (!sslDetails.hstsEnabled ? 1 : 0) +
    (!sslDetails.forwardSecrecy ? 1 : 0) +
    (!sslDetails.ocspStapling ? 1 : 0);

  // Return the unified model object
  return {
    id: "security-1",
    title: "SSL/TLS",
    icon: <Lock className="w-4 h-4" />,
    score: score,
    useUnifiedModel: true,
    impactStatement: {
      text: "Le certificat SSL/TLS est le passeport numérique de votre site web. Il vérifie son identité auprès des navigateurs et chiffre toutes les données échangées avec vos visiteurs, protégeant ainsi les informations sensibles.",
      positivePoints: positivePoints,
      negativePoints: negativePoints,
    },
    findings: findings,
    technicalDetails: technicalDetails,
    actionReference: {
      count: issueCount,
      sectionName: "SSL/TLS",
      actionWord: "Améliorer",
      targetId: "ssl-actions",
    },
  };
};

export default SslSecuritySection;
