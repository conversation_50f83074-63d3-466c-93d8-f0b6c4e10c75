/* Print styles for reports */

@media print {
  /* Hide elements that shouldn't be printed */
  #report-sidebar,
  .print-hidden,
  button,
  .no-print {
    display: none !important;
  }

  /* Ensure the report content takes full width */
  .report-content {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 0 !important;
  }

  /* Ensure proper page breaks */
  .page-break-before {
    page-break-before: always;
  }

  .page-break-after {
    page-break-after: always;
  }

  .avoid-break {
    page-break-inside: avoid;
  }

  /* Ensure proper font sizes and colors */
  body {
    font-size: 12pt;
    color: #000;
    background-color: #fff;
  }

  /* Ensure links are properly displayed */
  a {
    color: #000;
    text-decoration: underline;
  }

  /* Ensure proper margins */
  @page {
    margin: 1.5cm;
  }

  /* Ensure proper header and footer */
  .report-header {
    position: running(header);
  }

  .report-footer {
    position: running(footer);
  }

  @page {
    @top-center {
      content: element(header);
    }
    @bottom-center {
      content: element(footer);
    }
  }

  /* Ensure proper image handling */
  img {
    max-width: 100% !important;
  }

  /* Ensure proper table handling */
  table {
    border-collapse: collapse;
    width: 100%;
  }

  th, td {
    border: 1px solid #ddd;
    padding: 8px;
  }

  /* Ensure proper background colors */
  * {
    -webkit-print-color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
}
