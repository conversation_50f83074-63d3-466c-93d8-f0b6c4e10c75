import React from "react";
import { ReportAction } from "@/types/report";
import { useTranslation } from "react-i18next";
import { Shield, Search } from "lucide-react";

interface ActionItemProps {
  action: ReportAction;
  isSecurityAction: boolean;
  getSectionName: (title: string, type: string) => string;
  getDifficultyIcon: (difficulty: string) => React.ReactNode;
}

const ActionItem: React.FC<ActionItemProps> = ({
  action,
  isSecurityAction,
  getSectionName,
  getDifficultyIcon,
}) => {
  const { t } = useTranslation();

  return (
    <div
      className="rounded-lg p-2.5 transition-all duration-300 hover:scale-[1.01] border border-gray-200 hover:shadow-md backdrop-blur-sm neo-card bg-[#FDF6E3] overflow-hidden"
      style={{
        borderLeft: `3px solid ${isSecurityAction ? "#1eaedb" : "#d946ef"}`,
        boxShadow: isSecurityAction
          ? "0 2px 10px rgba(30,174,219,0.1)"
          : "0 2px 10px rgba(217,70,239,0.1)",
      }}
    >
      <div className="flex items-start gap-2">
        <div className="flex-shrink-0">
          {isSecurityAction ? (
            <Shield className="w-3.5 h-3.5 mt-0.5 text-[#1eaedb]" />
          ) : (
            <Search className="w-3.5 h-3.5 mt-0.5 text-[#d946ef]" />
          )}
        </div>
        <div className="flex-1">
          <h4 className="font-semibold text-xs mb-1 text-gray-800">
            {action.title}
          </h4>
          <p className="text-xs leading-tight text-gray-600 mb-3 line-clamp-2">
            {action.description}
          </p>

          {/* Tags at bottom right */}
          <div className="flex justify-end gap-1.5 mt-1">
            {/* Section tag - gray color scheme */}
            <div className="text-[8px] px-1.5 py-0.5 rounded-sm font-medium bg-gray-100 text-gray-600 border border-gray-200">
              {isSecurityAction ? (
                <span className="flex items-center">
                  <Shield className="w-2 h-2 mr-0.5 text-gray-500" />
                  {getSectionName(action.title, action.type)}
                </span>
              ) : (
                <span className="flex items-center">
                  <Search className="w-2 h-2 mr-0.5 text-gray-500" />
                  {getSectionName(action.title, action.type)}
                </span>
              )}
            </div>

            {/* Difficulty indicator - gray color scheme */}
            {action.implementationDifficulty && (
              <span className="text-[8px] px-1.5 py-0.5 rounded-sm font-medium bg-gray-100 text-gray-600 border border-gray-200">
                {getDifficultyIcon(action.implementationDifficulty)}
                {t(`difficulty.${action.implementationDifficulty}`)}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActionItem;
