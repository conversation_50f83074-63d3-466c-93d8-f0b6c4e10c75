/* Enhanced Report Styles */

/* Modern Card Design */
.neo-card-enhanced {
  @apply rounded-xl border border-white/20 backdrop-blur-lg p-6;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05),
    0 4px 6px -2px rgba(0, 0, 0, 0.025),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.neo-card-enhanced:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.05),
    0 10px 10px -5px rgba(0, 0, 0, 0.025),
    inset 0 0 0 1px rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Enhanced Score Cards */
.score-card-enhanced {
  @apply rounded-xl overflow-hidden relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.8)
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05),
    0 4px 6px -2px rgba(0, 0, 0, 0.025);
}

.score-card-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle at top left,
    rgba(155, 135, 245, 0.15) 0%,
    transparent 50%
  );
  z-index: 0;
}

.score-card-enhanced.seo::before {
  background: radial-gradient(
    circle at top left,
    rgba(217, 70, 239, 0.15) 0%,
    transparent 50%
  );
}

/* Enhanced Technical Sections */
.technical-section-enhanced {
  @apply rounded-xl overflow-hidden relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.8)
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05),
    0 4px 6px -2px rgba(0, 0, 0, 0.025);
}

.technical-section-enhanced.security {
  border-left: 4px solid;
  border-image: linear-gradient(to bottom, #3b82f6, #a855f7) 1;
}

.technical-section-enhanced.seo {
  border-left: 4px solid;
  border-image: linear-gradient(to bottom, #d946ef, #22d3ee) 1;
}

/* Enhanced Section Headers */
.section-header-enhanced {
  @apply flex items-center p-4 border-b border-gray-100;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.6)
  );
}

.section-header-enhanced.security {
  background: linear-gradient(
    to right,
    rgba(59, 130, 246, 0.05),
    rgba(168, 85, 247, 0.05)
  );
}

.section-header-enhanced.seo {
  background: linear-gradient(
    to right,
    rgba(217, 70, 239, 0.05),
    rgba(34, 211, 238, 0.05)
  );
}

/* Enhanced Section Content */
.section-content-enhanced {
  @apply p-4;
}

/* Enhanced Findings List */
.findings-list-enhanced {
  @apply space-y-3 mt-4;
}

.finding-item-enhanced {
  @apply flex items-start p-3 rounded-lg transition-all;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(229, 231, 235, 0.5);
}

.finding-item-enhanced:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(229, 231, 235, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.finding-item-enhanced.success {
  border-left: 3px solid #10b981;
}

.finding-item-enhanced.warning {
  border-left: 3px solid #f59e0b;
}

.finding-item-enhanced.error {
  border-left: 3px solid #ef4444;
}

/* Enhanced Priority Actions */
.priority-actions-enhanced {
  @apply rounded-xl overflow-hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.9),
    rgba(255, 255, 255, 0.8)
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05),
    0 4px 6px -2px rgba(0, 0, 0, 0.025);
}

/* Enhanced Action Groups */
.action-group-enhanced {
  @apply rounded-lg overflow-hidden mb-4;
  border: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.3s ease;
}

.action-group-enhanced:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.action-group-header-enhanced {
  @apply p-4 cursor-pointer;
}

.action-group-header-enhanced.critical {
  background: linear-gradient(
    to right,
    rgba(239, 68, 68, 0.1),
    rgba(248, 113, 113, 0.05)
  );
  border-bottom: 1px solid rgba(239, 68, 68, 0.1);
}

.action-group-header-enhanced.important {
  background: linear-gradient(
    to right,
    rgba(245, 158, 11, 0.1),
    rgba(251, 191, 36, 0.05)
  );
  border-bottom: 1px solid rgba(245, 158, 11, 0.1);
}

.action-group-header-enhanced.suggested {
  background: linear-gradient(
    to right,
    rgba(16, 185, 129, 0.1),
    rgba(52, 211, 153, 0.05)
  );
  border-bottom: 1px solid rgba(16, 185, 129, 0.1);
}

/* Enhanced Report Navigation */
.report-nav-enhanced {
  @apply fixed left-0 top-0 h-full bg-white/90 backdrop-blur-md z-50 transition-all duration-300 shadow-lg;
  border-right: 1px solid rgba(229, 231, 235, 0.5);
}

/* Enhanced Report Content */
.report-content-enhanced {
  @apply transition-all duration-300 overflow-y-auto;
  background: linear-gradient(135deg, #f1f4ff 0%, #f9f5ff 100%);
  min-height: 100vh;
  max-height: none; /* Ensure content can expand beyond viewport height */
  overflow-x: hidden;
}
