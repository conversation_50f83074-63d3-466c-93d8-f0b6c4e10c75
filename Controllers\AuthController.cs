using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using SiteCheckerApp.DTOs;
using SiteCheckerApp.Services;
using SiteCheckerApp.Models;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.RateLimiting;
using System.Security.Claims;



namespace SiteCheckerApp.Controllers
{
    using Microsoft.AspNetCore.Authorization;

    [EnableCors("AllowFrontend")]
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Require authentication for all actions by default
    public class AuthController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly IEmailService _emailService;
        private readonly ILogger<AuthController> _logger;
        private readonly IJwtService _jwtService;
        private readonly IRefreshTokenService _refreshTokenService;
        private readonly ICaptchaService _captchaService;
        private readonly IConfiguration _configuration;

        public AuthController(IUserService userService, IEmailService emailService, ILogger<AuthController> logger, IJwtService jwtService, IRefreshTokenService refreshTokenService, ICaptchaService captchaService, IConfiguration configuration)
        {
            _userService = userService;
            _emailService = emailService;
            _logger = logger;
            _jwtService = jwtService;
            _refreshTokenService = refreshTokenService;
            _captchaService = captchaService;
            _configuration = configuration;
        }



        [AllowAnonymous]
        [EnableRateLimiting("registration")]
        [HttpPost("register")]
        public async Task<ActionResult<RegisterResponse>> Register([FromBody] RegisterRequest request)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                _logger.LogWarning("Register: ModelState invalid with errors: {@Errors}", errors);
                return BadRequest(new { message = "Invalid registration data.", errors });
            }

            if (!await _captchaService.VerifyAsync(request.CaptchaToken, isRegistration: true, isV2Fallback: request.IsV2Fallback == "true"))
            {
                return BadRequest(new { error = "CAPTCHA_REQUIRED", message = "Invalid CAPTCHA. Please try again." });
            }

            try
            {
                var result = await _userService.RegisterAsync(request);

                if (!result.Success)
                {
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration");
                return StatusCode(500, new { message = "An unexpected error occurred during registration." });
            }
        }

        [AllowAnonymous]
        [HttpGet("check-username")]
        public async Task<IActionResult> CheckUsername(string username)
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                return BadRequest(new { message = "Username is required." });
            }

            var user = await _userService.GetUserByUsernameAsync(username);
            bool exists = user != null;

            return Ok(new { exists });
        }

        [AllowAnonymous]
        [HttpGet("check-email")]
        public async Task<IActionResult> CheckEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return BadRequest(new { message = "Email is required." });
            }

            var user = await _userService.GetUserByEmailAsync(email);
            bool exists = user != null;

            return Ok(new { exists });
        }


        [HttpPost("logout")]
        public IActionResult Logout()
        {
            // Clear cookies
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = false, // Disable Secure for local development (HTTP)
                SameSite = SameSiteMode.Lax, // Use Lax for local dev
                Expires = DateTime.UtcNow.AddDays(-1) // Expire immediately
            };

            Response.Cookies.Append("access_token", "", cookieOptions);
            Response.Cookies.Append("refresh_token", "", cookieOptions);

            return Ok(new { message = "Logged out successfully" });
        }

        [ApiExplorerSettings(IgnoreApi = true)]
        [AllowAnonymous]
        [HttpGet("verify-email")]
        public async Task<IActionResult> VerifyEmail(string email, string token)
        {
            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(token))
            {
                return BadRequest("Email and token are required.");
            }

            var verificationResult = await _userService.VerifyUserAsync(email, token);

            // Get frontend URL from environment variables or use default
            string? frontendBaseUrl = Environment.GetEnvironmentVariable("FRONTEND_BASE_URL");
            if (string.IsNullOrEmpty(frontendBaseUrl))
            {
                frontendBaseUrl = _configuration["AppSettings:FrontendBaseUrl"];
                if (string.IsNullOrEmpty(frontendBaseUrl) || frontendBaseUrl?.Contains("${") == true)
                {
                    frontendBaseUrl = "http://localhost:8080";
                }
            }

            _logger.LogInformation($"Using frontend URL for redirects: {frontendBaseUrl}");

            if (verificationResult.Success)
            {
                if (verificationResult.IsAdmin)
                {
                    return Redirect($"{frontendBaseUrl}/admin-email-verified");
                }

                return Redirect($"{frontendBaseUrl}/email-verified");
            }
            else
            {
                return Redirect($"{frontendBaseUrl}/email-verification-error?email={Uri.EscapeDataString(email)}");
            }
        }



        [AllowAnonymous]
        [EnableRateLimiting("login")]
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                var loginResponse = await _userService.LoginAsync(request);

                // Only set cookies if 2FA is not required
                if (!loginResponse.RequiresTwoFactor)
                {
                    var cookieOptions = new CookieOptions
                    {
                        HttpOnly = true,
                        Secure = false, // Disable Secure for local development (HTTP)
                        SameSite = SameSiteMode.Lax, // Use Lax for local dev
                        Path = "/", // Ensure cookie is sent on all paths
                        Expires = DateTime.UtcNow.AddDays(7)
                    };

                    if (!string.IsNullOrEmpty(loginResponse.RefreshToken))
                    {
                        Response.Cookies.Append("refresh_token", loginResponse.RefreshToken, cookieOptions);
                    }

                    // Only set access token if 2FA is not required
                    Response.Cookies.Append("access_token", loginResponse.Token, cookieOptions);
                }

                // Return response with 2FA information
                return Ok(new
                {
                    message = loginResponse.RequiresTwoFactor ?
                        "OTP sent to your email. Please verify to complete login." :
                        "Login successful",
                    token = loginResponse.RequiresTwoFactor ? null : loginResponse.Token,
                    refreshToken = loginResponse.RequiresTwoFactor ? null : loginResponse.RefreshToken,
                    userId = loginResponse.UserId,
                    email = loginResponse.Email,
                    username = loginResponse.Username,
                    role = loginResponse.Role,
                    requiresTwoFactor = loginResponse.RequiresTwoFactor
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogWarning(ex, "Unauthorized login attempt");
                return Unauthorized(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login");
                return StatusCode(500, new { message = "An unexpected error occurred during login." });
            }
        }

        [AllowAnonymous]
        [HttpPost("request-password-reset")]
        public async Task<IActionResult> RequestPasswordReset([FromBody] RequestPasswordResetRequest request)
        {
            if (string.IsNullOrEmpty(request.Email))
            {
                return BadRequest("Email is required.");
            }

            await _userService.RequestPasswordResetAsync(request.Email);
            return Ok(new { message = "If the email is registered, a password reset code has been sent." });
        }

        [AllowAnonymous]
        [EnableRateLimiting("registration")]
        [HttpPost("resend-verification-email")]
        public async Task<IActionResult> ResendVerificationEmail([FromBody] ResendVerificationEmailRequest request)
        {
            if (string.IsNullOrEmpty(request.Email))
            {
                return BadRequest(new { message = "Email is required." });
            }

            var result = await _userService.ResendVerificationEmailAsync(request.Email);

            if (result.Success)
            {
                return Ok(new { message = "If the email is registered and not verified, a new verification email has been sent." });
            }
            else
            {
                return BadRequest(new { message = result.Message });
            }
        }

        [AllowAnonymous]
        [HttpPost("verify-otp")]
        public async Task<ActionResult<OtpVerificationResponse>> VerifyOtp([FromBody] VerifyOtpRequest request)
        {
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("VerifyOtp: Invalid model state for request: {@Request}", request);
                return BadRequest("Invalid request data.");
            }

            var result = await _userService.VerifyOtpAsync(request.Email, request.VerifyCode);

            if (!result.IsValid)
            {
                _logger.LogWarning("VerifyOtp: Invalid or expired OTP for email {Email}. IsExpired: {IsExpired}", request.Email, result.IsExpired);
                return BadRequest(new { message = "Invalid or expired OTP." });
            }

            // Check if the OTP verification is for admin login
            var user = await _userService.GetUserByEmailAsync(request.Email);
            if (user != null && user.IdRole == 3)
            {
                // Generate tokens for admin
                var (accessToken, refreshToken) = await _jwtService.GenerateTokensAsync(user);

                // Save refresh token to user in database and get the saved token
                var savedRefreshToken = await _refreshTokenService.GenerateRefreshTokenAsync(user.Id);

                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = false, // Disable Secure for local development (HTTP)
                    SameSite = SameSiteMode.Lax, // Use Lax for local dev
                    // Domain = "localhost", // Removed explicit domain to fix cookie issues
                    Expires = DateTime.UtcNow.AddDays(7)
                };

                // Set only refresh_token cookie with saved token
                Response.Cookies.Append("refresh_token", savedRefreshToken, cookieOptions);

                // Return access token in response body
                return Ok(new OtpVerificationResponse
                {
                    IsValid = true,
                    IsExpired = false,
                    Message = "Login successful.",
                    VerificationType = OtpVerificationType.AdminLogin,
                    AccessToken = accessToken,
                    RefreshToken = savedRefreshToken,
                    UserId = user.Id,
                    Email = user.Email,
                    Username = user.Username,
                    Role = user.IdRole
                });
            }

            // For non-admin or other OTP verifications, return basic result
            return Ok(new OtpVerificationResponse
            {
                IsValid = result.IsValid,
                IsExpired = result.IsExpired,
                Message = "OTP verified successfully.",
                VerificationType = OtpVerificationType.Registration
            });
        }

        [AllowAnonymous]
        [HttpPost("reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPasswordRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest("Invalid request data.");
            }

            var result = await _userService.ResetPasswordAsync(request.Email, request.NewPassword);

            if (!result)
            {
                return BadRequest(new { message = "Invalid or expired reset token." });
            }

            return Ok(new { message = "Password reset successfully." });
        }

        [Authorize(Roles = "Admin")]
        [HttpPost("register-admin")]
        public async Task<ActionResult<RegisterResponse>> RegisterAdmin([FromBody] AdminRegisterRequest request)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { message = "Invalid admin registration data." });

            try
            {
                var result = await _userService.RegisterAdminAsync(request, request.SecretKey);

                if (!result.Success)
                {
                    _logger.LogWarning("Admin registration failed: {Message}", result.Message);
                    return BadRequest(new { message = result.Message });
                }

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during admin registration");
                return StatusCode(500, new { message = "An unexpected error occurred during admin registration." });
            }
        }

        [AllowAnonymous]
        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken()
        {
            try
            {
                // Read refresh token from HttpOnly cookie
                if (!Request.Cookies.TryGetValue("refresh_token", out var refreshToken) || string.IsNullOrEmpty(refreshToken))
                {
                    _logger.LogWarning("Refresh token cookie missing or empty");
                    return BadRequest(new { message = "Refresh token is required." });
                }

                _logger.LogInformation("Received refresh token from cookie: '{RefreshToken}'", refreshToken?.Trim());

                // Get the user ID from the refresh token
                if (refreshToken == null)
                {
                    _logger.LogWarning("Refresh token is null");
                    return Unauthorized(new { message = "Invalid refresh token." });
                }
                var userId = await _userService.GetUserIdFromRefreshTokenAsync(refreshToken);
                if (!userId.HasValue)
                {
                    _logger.LogWarning("Invalid refresh token provided: '{Token}'", refreshToken?.Trim());
                    Response.Cookies.Delete("refresh_token");
                    return Unauthorized(new
                    {
                        message = "Invalid refresh token.",
                        details = "Token may have been rotated already",
                        shouldLogout = true
                    });
                }

                // Get fresh user data
                var user = await _userService.GetUserByIdAsync(userId.Value);
                if (user == null)
                {
                    _logger.LogWarning("User not found for refresh token: '{Token}'", refreshToken?.Trim());
                    Response.Cookies.Delete("refresh_token");
                    return Unauthorized(new
                    {
                        message = "User not found.",
                        shouldLogout = true
                    });
                }

                _logger.LogInformation("Stored refresh token for user {UserId}: '{StoredRefreshToken}'", userId.Value, user.RefreshToken?.Trim());

                // Validate refresh token expiry and match
                var isValid = await _refreshTokenService.ValidateRefreshTokenAsync(userId.Value, refreshToken);
                if (!isValid)
                {
                    _logger.LogWarning("Refresh token expired or does not match for user {UserId}", userId.Value);
                    Response.Cookies.Delete("refresh_token");
                    return Unauthorized(new
                    {
                        message = "Refresh token expired or invalid.",
                        shouldLogout = true
                    });
                }

                // Rotate refresh token securely
                string newRefreshToken;
                try
                {
                    newRefreshToken = await _refreshTokenService.RotateRefreshTokenAsync(userId.Value, refreshToken);
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("concurrency conflict") || ex.Message.Contains("mismatch"))
                {
                    _logger.LogWarning("Concurrency or token mismatch error during token rotation for user {UserId}: {Message}", userId.Value, ex.Message);
                    Response.Cookies.Delete("refresh_token");
                    return Unauthorized(new
                    {
                        message = "Token rotation failed due to concurrency or token mismatch. Please login again.",
                        shouldLogout = true
                    });
                }

                // Generate new access token only (do not generate new refresh token here)
                var accessToken = _jwtService.GenerateAccessToken(user);

                _logger.LogInformation("Successfully rotated refresh token and generated new access token for user {UserId}", userId.Value);

                // Set tokens as HttpOnly cookies
                var cookieOptions = new CookieOptions
                {
                    HttpOnly = true,
                    Secure = false, // Disable Secure for local development (HTTP)
                    SameSite = SameSiteMode.Lax, // Use Lax for local dev
                    Path = "/", // Ensure cookie is sent on all paths
                    // Domain = "localhost", // Removed explicit domain to fix cookie issues
                    Expires = DateTime.UtcNow.AddDays(7)
                };

                // Set both access_token and refresh_token cookies
                Response.Cookies.Append("access_token", accessToken, cookieOptions);
                Response.Cookies.Append("refresh_token", newRefreshToken, cookieOptions);

                // Return new access token and refresh token in response body
                var response = new
                {
                    message = "Tokens refreshed successfully",
                    AccessToken = accessToken,
                    RefreshToken = newRefreshToken
                };

                // Log the response for debugging
                _logger.LogInformation("Refresh token response: {@Response}", response);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return StatusCode(500, new { message = "An unexpected error occurred during token refresh." });
            }
        }

        [HttpGet("recent-scans")]
        public async Task<IActionResult> GetRecentScans()
        {
            try
            {
                var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out Guid userGuid))
                {
                    return Unauthorized(new { message = "Identifiant utilisateur non trouvé dans le jeton." });
                }

                // Get the scan history service from DI
                var scanHistoryService = HttpContext.RequestServices.GetRequiredService<IScanHistoryService>();
                var scanHistory = await scanHistoryService.GetUserScanHistoryAsync(userGuid);

                // Group by URL and take the most recent scan of each type
                var groupedScans = scanHistory
                    .GroupBy(s => s.Url)
                    .Select(g => g.OrderByDescending(s => s.ScanDate).First())
                    .OrderByDescending(s => s.ScanDate)
                    .Take(5)
                    .Select(s => new
                    {
                        Url = s.Url,
                        Timestamp = s.ScanDate.ToString("o"),
                        ScanDepth = s.ScanDepth
                    })
                    .ToList();

                return Ok(groupedScans);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération de l'historique des analyses récentes");
                return StatusCode(500, new { message = "Une erreur s'est produite lors de la récupération de l'historique des analyses récentes." });
            }
        }

        [HttpGet("me")]
        public async Task<ActionResult<UserInfoResponse>> GetCurrentUser()
        {
            _logger.LogInformation("GetCurrentUser called. Claims in token:");
            foreach (var claim in User.Claims)
            {
                _logger.LogInformation("Claim Type: {Type}, Value: {Value}", claim.Type, claim.Value);
            }

            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null)
            {
                _logger.LogWarning("User ID claim not found in token.");
                return Unauthorized(new { message = "User ID claim not found." });
            }

            if (!Guid.TryParse(userIdClaim.Value, out var userId))
            {
                _logger.LogWarning("Invalid user ID claim value: {ClaimValue}", userIdClaim.Value);
                return Unauthorized(new { message = "Invalid user ID claim." });
            }

            _logger.LogInformation("GetCurrentUser called for user ID: {UserId}", userId);

            var user = await _userService.GetUserByIdAsync(userId);
            if (user == null)
            {
                _logger.LogWarning("User not found for ID: {UserId}", userId);
                return NotFound(new { message = "User not found." });
            }

            var response = new UserInfoResponse
            {
                Id = user.Id,
                Email = user.Email,
                Username = user.Username,
                Role = user.IdRole
            };

            _logger.LogInformation("Returning user info for user ID: {UserId}", userId);

            return Ok(response);
        }
        [HttpPut("update-profile")]
        public async Task<IActionResult> UpdateProfile([FromBody] UpdateProfileRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { message = "Invalid profile data." });
            }

            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized(new { message = "User ID claim not found or invalid." });
            }

            var user = await _userService.GetUserByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "User not found." });
            }

            // Vérifier si le nom d'utilisateur est déjà pris par un autre utilisateur
            if (request.Username != user.Username)
            {
                var existingUser = await _userService.GetUserByUsernameAsync(request.Username);
                if (existingUser != null && existingUser.Id != userId)
                {
                    return BadRequest(new { message = "Ce nom d'utilisateur est déjà utilisé." });
                }
            }

            // Mettre à jour les informations du profil
            user.Username = request.Username;
            user.UpdatedAt = DateTime.UtcNow;

            await _userService.UpdateUserAsync(user);

            return Ok(new { message = "Profil mis à jour avec succès." });
        }

        [HttpPost("change-password")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { message = "Invalid password data." });
            }

            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized(new { message = "User ID claim not found or invalid." });
            }

            var user = await _userService.GetUserByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "User not found." });
            }

            // Vérifier le mot de passe actuel
            if (!await _userService.VerifyPasswordAsync(user, request.CurrentPassword))
            {
                return BadRequest(new { message = "Le mot de passe actuel est incorrect." });
            }

            // Vérifier que le nouveau mot de passe est différent de l'ancien
            if (request.CurrentPassword == request.NewPassword)
            {
                return BadRequest(new { message = "Le nouveau mot de passe doit être différent de l'ancien." });
            }

            // Vérifier que les deux nouveaux mots de passe correspondent
            if (request.NewPassword != request.ConfirmNewPassword)
            {
                return BadRequest(new { message = "Les nouveaux mots de passe ne correspondent pas." });
            }

            // Mettre à jour le mot de passe
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userService.UpdateUserAsync(user);

            return Ok(new { message = "Mot de passe modifié avec succès." });
        }

        [HttpPost("reset-password-from-profile")]
        public async Task<IActionResult> ResetPasswordFromProfile([FromBody] ResetPasswordFromProfileRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new { message = "Invalid password data." });
            }

            var userIdClaim = User.Claims.FirstOrDefault(c => c.Type == System.Security.Claims.ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                return Unauthorized(new { message = "User ID claim not found or invalid." });
            }

            var user = await _userService.GetUserByIdAsync(userId);
            if (user == null)
            {
                return NotFound(new { message = "User not found." });
            }

            // Vérifier que les deux nouveaux mots de passe correspondent
            if (request.NewPassword != request.ConfirmNewPassword)
            {
                return BadRequest(new { message = "Les mots de passe ne correspondent pas." });
            }

            // Mettre à jour le mot de passe
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(request.NewPassword);
            user.UpdatedAt = DateTime.UtcNow;

            await _userService.UpdateUserAsync(user);

            return Ok(new { message = "Mot de passe réinitialisé avec succès." });
        }

        [HttpGet("debug-token")]
        public async Task<IActionResult> DebugToken()
        {
            try
            {
                // Read refresh token from HttpOnly cookie
                if (!Request.Cookies.TryGetValue("refresh_token", out var cookieToken) || string.IsNullOrEmpty(cookieToken))
                {
                    _logger.LogWarning("DebugToken: Refresh token cookie missing or empty");
                    return BadRequest(new { message = "Refresh token cookie is required." });
                }

                // Get the user ID from the refresh token
                var userId = await _userService.GetUserIdFromRefreshTokenAsync(cookieToken);
                if (!userId.HasValue)
                {
                    _logger.LogWarning("DebugToken: Invalid refresh token provided: '{Token}'", cookieToken?.Trim());
                    return Unauthorized(new
                    {
                        message = "Invalid refresh token.",
                        shouldLogout = true
                    });
                }

                // Get user from DB
                var user = await _userService.GetUserByIdAsync(userId.Value);
                if (user == null)
                {
                    _logger.LogWarning("DebugToken: User not found for refresh token: '{Token}'", cookieToken?.Trim());
                    return Unauthorized(new
                    {
                        message = "User not found.",
                        shouldLogout = true
                    });
                }

                var isValid = await _refreshTokenService.ValidateRefreshTokenAsync(userId.Value, cookieToken);

                var response = new
                {
                    IsValid = isValid
                };

                _logger.LogInformation("DebugToken response: {@Response}", response);

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during debug-token");
                return StatusCode(500, new { message = "An unexpected error occurred during debug-token." });
            }
        }
    }
}
