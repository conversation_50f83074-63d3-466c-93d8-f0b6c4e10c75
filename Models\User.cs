using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace SiteCheckerApp.Models
{
    public class User
    {
        public Guid Id { get; set; }

        public string Username { get; set; } = string.Empty;

        public string Email { get; set; } = string.Empty;

        public string PasswordHash { get; set; } = string.Empty;

        // Email verification properties
        public string? VerificationToken { get; set; } = string.Empty;
        public DateTime? VerificationTokenExpiry { get; set; }

        // OTP properties (used for admin 2FA and for password resets)
        public string? Otp { get; set; } = string.Empty;
        public DateTime? OtpExpiry { get; set; }  // Now the column in the database is OtpExpiry

        // Password reset properties
        public string? PasswordResetToken { get; set; } = string.Empty;
        public DateTime? PasswordResetTokenExpiry { get; set; }

        public bool IsVerified { get; set; }

        // Only used for admin two-factor authentication
        public bool IsTwoFactorEnabled { get; set; }

        // Role details: 1 = Free User, 2 = Premium User, 3 = Admin.
        public int IdRole { get; set; }

        public string BillingCycle { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Refresh token properties
        public string? RefreshToken { get; set; }
        public DateTime? RefreshTokenExpiry { get; set; }

        // Token version for concurrency control
        public int TokenVersion { get; set; } = 1;

        // New properties for account lockout
        public int FailedLoginAttempts { get; set; }
        public DateTime? LockoutEnd { get; set; }

        [NotMapped]
        public bool IsLockedOut => LockoutEnd.HasValue && LockoutEnd > DateTime.UtcNow;
    }
}
