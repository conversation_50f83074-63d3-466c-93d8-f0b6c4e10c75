import React from "react";
import { <PERSON>, Check, AlertTriangle } from "lucide-react";
import { HttpHeader } from "@/types/report";

// Import the TechnicalSectionItem interface
interface TechnicalSectionItem {
  id: string;
  title: string;
  content?: React.ReactNode;
  score?: number;
  icon?: React.ReactNode;
  impactStatement?: {
    text: string;
    positivePoints?: string[];
    negativePoints?: string[];
  };
  findings?: {
    status: "success" | "warning" | "error";
    label: string;
    impact: string;
  }[];
  technicalDetails?: {
    parameter: string;
    value: string;
    impact: string;
    recommended?: string;
    tooltip?: string;
  }[];
  actionReference?: {
    count: number;
    sectionName: string;
    actionWord: string;
    targetId: string;
  };
  useUnifiedModel?: boolean;
}

interface HttpHeadersSectionProps {
  headers?: HttpHeader[];
  score?: number;
}

const HttpHeadersSection = ({
  headers,
  score = 0,
}: HttpHeadersSectionProps): TechnicalSectionItem => {
  // <PERSON>le missing data gracefully
  if (!headers || headers.length === 0) {
    return {
      id: "security-2",
      title: "En-têtes HTTP",
      icon: <Globe className="w-4 h-4" />,
      score: 0,
      useUnifiedModel: true,
      impactStatement: {
        text: "Les en-têtes HTTP de sécurité n'ont pas pu être analysés. Ces en-têtes sont essentiels pour protéger votre site contre diverses attaques web.",
        positivePoints: [],
        negativePoints: ["Impossible d'analyser les en-têtes HTTP de sécurité"],
      },
      findings: [
        {
          status: "error",
          label: "Données d'en-têtes manquantes",
          impact: "Impossible d'évaluer la sécurité de vos en-têtes HTTP",
        },
      ],
      technicalDetails: [],
      actionReference: {
        count: 1,
        sectionName: "En-têtes HTTP",
        actionWord: "Vérifier",
        targetId: "headers-actions",
      },
    };
  }

  // Common security headers to check for
  const criticalHeaders = [
    "Content-Security-Policy",
    "Strict-Transport-Security",
    "X-Content-Type-Options",
    "X-Frame-Options",
    "Referrer-Policy",
    "Permissions-Policy",
  ];

  // Header explanations
  const headerExplanations: Record<string, string> = {
    "Content-Security-Policy":
      "Bloque les scripts malveillants et prévient les attaques XSS",
    "Strict-Transport-Security":
      "Force les connexions HTTPS et prévient les attaques de déclassement",
    "X-Content-Type-Options":
      "Empêche le MIME-sniffing et les attaques basées sur le type de contenu",
    "X-Frame-Options":
      "Protège contre le clickjacking en contrôlant l'affichage dans des iframes",
    "Referrer-Policy":
      "Contrôle les informations de référence envoyées lors de la navigation",
    "Permissions-Policy":
      "Limite l'accès aux fonctionnalités sensibles du navigateur",
  };

  // Traductions françaises des impacts des en-têtes HTTP
  const headerImpacts: Record<string, string> = {
    "Content-Security-Policy":
      "Empêche les attaques XSS en définissant les sources de contenu autorisées",
    "Strict-Transport-Security":
      "Garantit que le navigateur utilise toujours HTTPS pour ce domaine",
    "X-Content-Type-Options": "Empêche la détection automatique du type MIME",
    "X-Frame-Options": "Empêche les attaques de type clickjacking",
    "Referrer-Policy":
      "Contrôle les informations de référence dans les requêtes",
    "Permissions-Policy":
      "Limite l'accès aux fonctionnalités sensibles du navigateur",
    "X-XSS-Protection":
      "Fournit une protection supplémentaire contre les attaques XSS",
    "X-Permitted-Cross-Domain-Policies":
      "Contrôle quelles politiques cross-domain peuvent être appliquées",
    "Cross-Origin-Embedder-Policy":
      "Empêche le chargement de ressources provenant de sources non fiables",
    "Cross-Origin-Opener-Policy":
      "Isole le contexte de navigation pour améliorer la sécurité",
    "Cross-Origin-Resource-Policy":
      "Empêche d'autres sites d'accéder à vos ressources",
    "Cache-Control":
      "Contrôle la mise en cache des ressources par le navigateur",
    "X-Powered-By":
      "Révèle des informations sur la technologie utilisée par le serveur",
    Server: "Révèle des informations sur le serveur web utilisé",
    "Feature-Policy":
      "Contrôle quelles fonctionnalités du navigateur peuvent être utilisées",
  };

  // Generate positive and negative points based on headers
  const positivePoints = headers
    .filter((h) => {
      const status = h.status?.toString().toLowerCase();
      return status === "success" || status === "good";
    })
    .slice(0, 3)
    .map(
      (h) =>
        `${h.name}: ${
          headerImpacts[h.name] || h.securityImpact || "Protection active"
        }`
    );

  const negativePoints = headers
    .filter((h) => {
      const status = h.status?.toString().toLowerCase();
      return status === "error" || status === "bad" || status === "warning";
    })
    .slice(0, 3)
    .map(
      (h) =>
        `${h.name}: ${
          headerImpacts[h.name] || h.securityImpact || "Protection manquante"
        }`
    );

  // Generate findings for critical headers
  const findings = criticalHeaders.map((headerName) => {
    const header = headers.find(
      (h) => h.name.toLowerCase() === headerName.toLowerCase()
    );
    const status = header?.status?.toString().toLowerCase();

    // Check if header is present - consider 'present' status explicitly
    const isPresent =
      status === "success" || status === "good" || status === "present";
    // Check if header is explicitly marked as missing
    const isExplicitlyMissing = status === "missing";

    return {
      status: isPresent ? ("success" as const) : ("error" as const),
      label: headerName,
      impact: isPresent
        ? headerImpacts[headerName] ||
          "En-tête de sécurité correctement configuré"
        : `En-tête manquant: ${
            headerImpacts[headerName] || "Protection de sécurité manquante"
          }`,
    };
  });

  // Générer les détails techniques pour tous les en-têtes critiques, même s'ils ne sont pas présents
  const technicalDetails = criticalHeaders.map((headerName) => {
    const header = headers.find(
      (h) => h.name.toLowerCase() === headerName.toLowerCase()
    );
    const status = header?.status?.toString().toLowerCase();

    // Check if header is present - consider 'present' status explicitly
    const isPresent =
      status === "success" || status === "good" || status === "present";
    // Check if header is explicitly marked as missing
    const isExplicitlyMissing = status === "missing";

    return {
      parameter: headerName,
      value: isPresent ? header?.value || "Présent" : "Non défini",
      impact:
        headerImpacts[headerName] || "Affecte la sécurité de votre site web",
      tooltip: `En-tête HTTP qui ${
        headerImpacts[headerName] || "affecte la sécurité de votre site"
      }`,
      recommended: isPresent
        ? "Cet en-tête est correctement configuré. Continuez à le maintenir à jour."
        : `Ajoutez l'en-tête ${headerName} pour améliorer la sécurité de votre site. ${
            headerImpacts[headerName] || ""
          }`,
    };
  });

  // Ajouter des détails techniques pour d'autres en-têtes de sécurité importants
  const additionalHeaders = [
    "X-XSS-Protection",
    "X-Permitted-Cross-Domain-Policies",
    "Cross-Origin-Embedder-Policy",
    "Cross-Origin-Opener-Policy",
    "Cross-Origin-Resource-Policy",
    "Feature-Policy",
    "Cache-Control",
  ];

  // Ajouter ces en-têtes aux détails techniques, qu'ils soient présents ou non
  additionalHeaders.forEach((headerName) => {
    const header = headers.find(
      (h) => h.name.toLowerCase() === headerName.toLowerCase()
    );
    const status = header?.status?.toString().toLowerCase();

    // Check if header is present - consider 'present' status explicitly
    const isPresent =
      status === "success" || status === "good" || status === "present";

    technicalDetails.push({
      parameter: headerName,
      value: isPresent ? header?.value || "Présent" : "Non défini",
      impact:
        headerImpacts[headerName] ||
        "Fournit une couche de protection supplémentaire",
      tooltip: `En-tête HTTP qui renforce la sécurité de votre site`,
      recommended: isPresent
        ? "Cet en-tête est correctement configuré. Continuez à le maintenir à jour."
        : `Envisagez d'ajouter l'en-tête ${headerName} pour une sécurité renforcée.`,
    });
  });

  // Ajouter des informations sur les en-têtes Server et X-Powered-By s'ils sont présents
  const infoHeaders = ["Server", "X-Powered-By"];

  infoHeaders.forEach((headerName) => {
    const header = headers.find(
      (h) => h.name.toLowerCase() === headerName.toLowerCase()
    );
    const status = header?.status?.toString().toLowerCase();

    // Check if header is present - consider 'present' status explicitly
    const isPresent =
      status === "success" ||
      status === "good" ||
      status === "present" ||
      (header && header.value);

    if (isPresent) {
      technicalDetails.push({
        parameter: header?.name || headerName,
        value: header?.value || "Présent",
        impact: "Peut révéler des informations sensibles sur votre serveur",
        tooltip:
          "Il est recommandé de masquer ces informations pour réduire les risques",
        recommended:
          "Envisagez de masquer ou de supprimer cet en-tête pour réduire les informations divulguées sur votre infrastructure.",
      });
    }
  });

  // Trier les détails techniques par ordre alphabétique pour une meilleure lisibilité
  technicalDetails.sort((a, b) => a.parameter.localeCompare(b.parameter));

  // Count missing critical headers for action reference
  const missingCriticalHeaders = criticalHeaders.filter((name) => {
    return !headers.some((h) => {
      const status = h.status?.toString().toLowerCase();
      // Consider 'present' status explicitly
      return (
        h.name.toLowerCase() === name.toLowerCase() &&
        (status === "success" || status === "good" || status === "present")
      );
    });
  });

  // We've removed the console logs to keep the browser console clean

  // Ensure we have positive points if score is high, and no positive points if score is very low
  let finalPositivePoints = positivePoints;
  let finalNegativePoints = negativePoints;

  if (score >= 80 && positivePoints.length === 0) {
    finalPositivePoints = [
      "Vos en-têtes HTTP sont globalement bien configurés et offrent une bonne protection contre les attaques web courantes",
    ];
  } else if (score <= 20) {
    // Clear positive points when score is very low (20 or below)
    finalPositivePoints = [];

    // If score is 0, enhance negative points to be more specific about critical missing headers
    if (score === 0 && missingCriticalHeaders.length > 0) {
      finalNegativePoints = [
        `Strict-Transport-Security: Manquant - ${
          headerImpacts["Strict-Transport-Security"] ||
          "Votre site est vulnérable aux attaques de déclassement HTTPS"
        }`,
        `Content-Security-Policy: Manquant - ${
          headerImpacts["Content-Security-Policy"] ||
          "Votre site est vulnérable aux attaques XSS"
        }`,
        `X-Content-Type-Options: Manquant - ${
          headerImpacts["X-Content-Type-Options"] ||
          "Votre site est vulnérable au MIME-sniffing"
        }`,
      ];
    }
  }

  // Return the unified model object
  return {
    id: "security-2",
    title: "En-têtes HTTP",
    icon: <Globe className="w-4 h-4" />,
    score: score,
    useUnifiedModel: true,
    impactStatement: {
      text:
        score === 0
          ? "CRITIQUE: Aucun des en-têtes HTTP de sécurité essentiels n'est présent sur votre site. Votre site est extrêmement vulnérable à de nombreuses attaques web."
          : score <= 20
          ? "Les en-têtes HTTP de sécurité essentiels sont absents de votre site. Cela expose votre site à de nombreuses attaques web courantes et réduit la confiance des utilisateurs."
          : "Les en-têtes HTTP de sécurité protègent votre site contre les attaques web courantes et renforcent la confiance des utilisateurs. Certains en-têtes importants sont manquants, ce qui expose votre site à des risques potentiels.",
      positivePoints: finalPositivePoints,
      negativePoints: finalNegativePoints,
    },
    findings: findings,
    technicalDetails: technicalDetails,
    actionReference: {
      count: missingCriticalHeaders.length,
      sectionName: "En-têtes HTTP",
      actionWord: "Ajouter",
      targetId: "headers-actions",
    },
  };
};

export default HttpHeadersSection;
