import { jsPDF } from "jspdf";
import { SecurityReport, SeoReport, ReportAction } from "@/types/report";
import { toast } from "@/hooks/use-toast";

/**
 * Utility class for generating and downloading reports in different formats
 */
export class ReportDownloader {
  /**
   * Generate and download a PDF report
   * @param url The URL that was scanned
   * @param securityReport The security report data
   * @param seoReport The SEO report data
   * @param scanDuration The duration of the scan in seconds
   * @param recommendedActions The combined list of recommended actions
   * @returns Promise that resolves when the PDF is generated and downloaded
   */
  static generatePdfReport(
    url: string,
    securityReport: SecurityReport | null,
    seoReport: SeoReport | null,
    scanDuration: number | null,
    recommendedActions: ReportAction[]
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new jsPDF();
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();

        // Add logo and header
        doc.setFillColor(65, 105, 225); // Royal blue
        doc.rect(0, 0, pageWidth, 25, "F");
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(18);
        doc.setFont("helvetica", "bold");
        doc.text("SiteChecker", 10, 15);
        doc.setFontSize(12);
        doc.setFont("helvetica", "normal");
        doc.text("Rapport d'Analyse de Sécurité et SEO", pageWidth - 10, 15, {
          align: "right",
        });

        // Reset text color
        doc.setTextColor(0, 0, 0);

        // Report title
        doc.setFontSize(22);
        doc.setFont("helvetica", "bold");
        doc.text("Rapport d'Analyse Détaillé", pageWidth / 2, 40, {
          align: "center",
        });

        // URL and date info
        doc.setFontSize(12);
        doc.setFont("helvetica", "normal");
        doc.text(`URL: ${url}`, 10, 55);
        doc.text(`Date: ${new Date().toLocaleDateString("fr-FR")}`, 10, 62);
        doc.text(`Durée du scan: ${scanDuration || 0} secondes`, 10, 69);

        // Score section with colored boxes
        doc.setFillColor(240, 240, 240);
        doc.roundedRect(10, 80, pageWidth - 20, 40, 3, 3, "F");

        doc.setFontSize(14);
        doc.setFont("helvetica", "bold");
        doc.text("Scores:", 15, 90);

        // Security score with colored indicator
        if (securityReport) {
          const securityScore = securityReport.overall_score;
          let scoreColor: number[];

          if (securityScore >= 80) {
            scoreColor = [46, 204, 113]; // Green
          } else if (securityScore >= 50) {
            scoreColor = [241, 196, 15]; // Yellow
          } else {
            scoreColor = [231, 76, 60]; // Red
          }

          doc.setFillColor(scoreColor[0], scoreColor[1], scoreColor[2]);
          doc.circle(25, 100, 5, "F");
          doc.setFont("helvetica", "normal");
          doc.text(`Sécurité: ${securityScore}/100`, 35, 102);
        }

        // SEO score with colored indicator
        if (seoReport) {
          const seoScore = seoReport.overall_score;
          let scoreColor: number[];

          if (seoScore >= 80) {
            scoreColor = [46, 204, 113]; // Green
          } else if (seoScore >= 50) {
            scoreColor = [241, 196, 15]; // Yellow
          } else {
            scoreColor = [231, 76, 60]; // Red
          }

          doc.setFillColor(scoreColor[0], scoreColor[1], scoreColor[2]);
          doc.circle(25, 110, 5, "F");
          doc.setFont("helvetica", "normal");
          doc.text(`SEO: ${seoScore}/100`, 35, 112);
        }

        let y = 130;

        // Recommended actions section
        if (recommendedActions.length > 0) {
          doc.setFillColor(240, 240, 240);
          doc.roundedRect(10, y, pageWidth - 20, 10, 3, 3, "F");

          doc.setFontSize(14);
          doc.setFont("helvetica", "bold");
          doc.text("Actions Recommandées", 15, y + 7);
          y += 15;

          recommendedActions.forEach((action, i) => {
            if (y > pageHeight - 20) {
              doc.addPage();
              y = 20;
            }

            // Priority color
            let priorityColor: number[];
            const priorityLabel =
              action.priority === 3
                ? "Haute"
                : action.priority === 2
                ? "Moyenne"
                : "Basse";

            if (action.priority === 3) {
              priorityColor = [231, 76, 60]; // Red for high
            } else if (action.priority === 2) {
              priorityColor = [241, 196, 15]; // Yellow for medium
            } else {
              priorityColor = [46, 204, 113]; // Green for low
            }

            doc.setFillColor(
              priorityColor[0],
              priorityColor[1],
              priorityColor[2]
            );
            doc.roundedRect(15, y, 3, 3, 1, 1, "F");

            doc.setFontSize(12);
            doc.setFont("helvetica", "bold");
            doc.text(`${i + 1}. ${action.title}`, 25, y + 3);
            y += 7;

            doc.setFontSize(10);
            doc.setFont("helvetica", "italic");
            doc.text(
              `Priorité: ${priorityLabel} | Type: ${action.type} | Difficulté: ${action.implementationDifficulty}`,
              25,
              y
            );
            y += 7;

            doc.setFont("helvetica", "normal");
            const description = action.description;
            const words = description.split(" ");
            let line = "";

            for (let j = 0; j < words.length; j++) {
              if ((line + " " + words[j]).length < 80) {
                line += (line ? " " : "") + words[j];
              } else {
                doc.text(line, 25, y);
                line = words[j];
                y += 5;

                if (y > pageHeight - 20) {
                  doc.addPage();
                  y = 20;
                }
              }
            }

            if (line) {
              doc.text(line, 25, y);
              y += 10;
            }
          });
        }

        // Vulnerabilities section
        if (
          securityReport?.vulnerabilities &&
          securityReport.vulnerabilities.length > 0
        ) {
          if (y > pageHeight - 40) {
            doc.addPage();
            y = 20;
          }

          doc.setFillColor(240, 240, 240);
          doc.roundedRect(10, y, pageWidth - 20, 10, 3, 3, "F");

          doc.setFontSize(14);
          doc.setFont("helvetica", "bold");
          doc.text("Vulnérabilités Détectées", 15, y + 7);
          y += 15;

          securityReport.vulnerabilities.forEach((vuln, i) => {
            if (y > pageHeight - 20) {
              doc.addPage();
              y = 20;
            }

            // Severity color
            let severityColor: number[];
            if (
              vuln.severity.toLowerCase() === "high" ||
              vuln.severity.toLowerCase() === "critical"
            ) {
              severityColor = [231, 76, 60]; // Red
            } else if (vuln.severity.toLowerCase() === "medium") {
              severityColor = [241, 196, 15]; // Yellow
            } else {
              severityColor = [46, 204, 113]; // Green
            }

            doc.setFillColor(
              severityColor[0],
              severityColor[1],
              severityColor[2]
            );
            doc.roundedRect(15, y, 3, 3, 1, 1, "F");

            doc.setFontSize(12);
            doc.setFont("helvetica", "bold");
            doc.text(`${i + 1}. ${vuln.name}`, 25, y + 3);
            y += 7;

            doc.setFontSize(10);
            doc.setFont("helvetica", "italic");
            doc.text(`Sévérité: ${vuln.severity}`, 25, y);
            y += 7;

            doc.setFont("helvetica", "normal");
            const description =
              vuln.description || "Aucune description disponible";
            const words = description.split(" ");
            let line = "";

            for (let j = 0; j < words.length; j++) {
              if ((line + " " + words[j]).length < 80) {
                line += (line ? " " : "") + words[j];
              } else {
                doc.text(line, 25, y);
                line = words[j];
                y += 5;

                if (y > pageHeight - 20) {
                  doc.addPage();
                  y = 20;
                }
              }
            }

            if (line) {
              doc.text(line, 25, y);
              y += 10;
            }
          });
        }

        // Footer on each page
        const totalPages = doc.internal.pages.length - 1;
        for (let i = 1; i <= totalPages; i++) {
          doc.setPage(i);
          doc.setFontSize(10);
          doc.setTextColor(150, 150, 150);
          doc.text(
            `Page ${i} sur ${totalPages} | SiteChecker - Rapport généré le ${new Date().toLocaleDateString(
              "fr-FR"
            )}`,
            pageWidth / 2,
            pageHeight - 10,
            { align: "center" }
          );
        }

        doc.save(`rapport-${url.replace(/[^a-zA-Z0-9]/g, "-")}.pdf`);
        resolve();
      } catch (error) {
        console.error("Error generating PDF:", error);
        reject(error);
      }
    });
  }

  static generateHtmlReport(
    url: string,
    securityReport: SecurityReport | null,
    seoReport: SeoReport | null,
    scanDuration: number | null,
    recommendedActions: ReportAction[]
  ): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        // Create HTML content
        let htmlContent = `
      <!DOCTYPE html>
      <html lang="fr">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Rapport d'Analyse - ${url}</title>
        <style>
          :root {
            --primary-color: #4169E1;
            --secondary-color: #8A2BE2;
            --success-color: #2ecc71;
            --warning-color: #f1c40f;
            --danger-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
            --gradient-blue: linear-gradient(135deg, #4169E1, #8A2BE2);
            --gradient-green: linear-gradient(135deg, #2ecc71, #26c281);
            --gradient-yellow: linear-gradient(135deg, #f1c40f, #f39c12);
            --gradient-red: linear-gradient(135deg, #e74c3c, #c0392b);
            --gradient-purple: linear-gradient(135deg, #9b59b6, #8e44ad);
            --gradient-indigo: linear-gradient(135deg, #3498db, #2980b9);
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
          }

          header {
            background: var(--gradient-blue);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
          }

          h1, h2, h3 {
            margin-top: 0;
          }

          .report-title {
            font-size: 28px;
            margin-bottom: 10px;
          }

          .url-info {
            font-size: 18px;
            opacity: 0.9;
          }

          .date-info {
            font-size: 14px;
            opacity: 0.8;
          }

          .score-container {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
          }

          .score-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            width: 45%;
            min-width: 300px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          }

          .score-title {
            font-size: 20px;
            margin-bottom: 15px;
            color: var(--primary-color);
          }

          .score-value {
            font-size: 48px;
            font-weight: bold;
            margin: 10px 0;
          }

          .score-high {
            color: var(--success-color);
          }

          .score-medium {
            color: var(--warning-color);
          }

          .score-low {
            color: var(--danger-color);
          }

          .section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
          }

          .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--gradient-blue);
          }

          .section-title {
            font-size: 22px;
            background: var(--gradient-blue);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            font-weight: bold;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
            display: inline-block;
          }

          .action-item, .vulnerability-item {
            border-left: 4px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            border-radius: 0 5px 5px 0;
          }

          .priority-high {
            border-left-color: var(--danger-color);
          }

          .priority-medium {
            border-left-color: var(--warning-color);
          }

          .priority-low {
            border-left-color: var(--success-color);
          }

          .action-title, .vulnerability-title {
            font-size: 18px;
            margin-bottom: 10px;
            font-weight: bold;
          }

          .action-meta, .vulnerability-meta {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
          }

          .action-description, .vulnerability-description {
            font-size: 15px;
            line-height: 1.5;
          }

          footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
          }

          @media print {
            body {
              background-color: white;
              padding: 0;
            }

            .section, .score-card {
              box-shadow: none;
              border: 1px solid #eee;
            }

            header {
              background: white;
              color: black;
              box-shadow: none;
              border-bottom: 2px solid #eee;
            }

            .score-title {
              color: black;
            }

            .section-title {
              color: black;
            }
          }
        </style>
      </head>
      <body>
        <header>
          <h1 class="report-title">Rapport d'Analyse SiteChecker</h1>
          <div class="url-info">${url}</div>
          <div class="date-info">Généré le ${new Date().toLocaleDateString(
            "fr-FR"
          )} | Durée du scan: ${scanDuration} secondes</div>
        </header>

        <div class="score-container">`;

        // Add security score
        if (securityReport) {
          const scoreClass =
            securityReport.overall_score >= 80
              ? "score-high"
              : securityReport.overall_score >= 50
              ? "score-medium"
              : "score-low";

          htmlContent += `
          <div class="score-card">
            <h2 class="score-title">Score de Sécurité</h2>
            <div class="score-value ${scoreClass}">${securityReport.overall_score}/100</div>
          </div>`;
        }

        // Add SEO score
        if (seoReport) {
          const scoreClass =
            seoReport.overall_score >= 80
              ? "score-high"
              : seoReport.overall_score >= 50
              ? "score-medium"
              : "score-low";

          htmlContent += `
          <div class="score-card">
            <h2 class="score-title">Score SEO</h2>
            <div class="score-value ${scoreClass}">${seoReport.overall_score}/100</div>
          </div>`;
        }

        htmlContent += `
        </div>

        <!-- Recommended Actions Section -->
        <div class="section">
          <h2 class="section-title">Actions Recommandées</h2>`;

        if (recommendedActions.length > 0) {
          recommendedActions.forEach((action) => {
            const priorityClass =
              action.priority === 3
                ? "priority-high"
                : action.priority === 2
                ? "priority-medium"
                : "priority-low";

            const priorityLabel =
              action.priority === 3
                ? "Haute"
                : action.priority === 2
                ? "Moyenne"
                : "Basse";

            htmlContent += `
            <div class="action-item ${priorityClass}">
              <div class="action-title">${action.title}</div>
              <div class="action-meta">
                Priorité: <strong>${priorityLabel}</strong> |
                Type: <strong>${action.type}</strong> |
                Difficulté: <strong>${action.implementationDifficulty}</strong>
              </div>
              <div class="action-description">${action.description}</div>
            </div>`;
          });
        } else {
          htmlContent += `<p>Aucune action recommandée.</p>`;
        }

        htmlContent += `
        </div>

        <!-- Vulnerabilities Section -->
        <div class="section">
          <h2 class="section-title">Vulnérabilités Détectées</h2>`;

        if (
          securityReport?.vulnerabilities &&
          securityReport.vulnerabilities.length > 0
        ) {
          securityReport.vulnerabilities.forEach((vuln) => {
            const severityClass =
              vuln.severity.toLowerCase() === "high" ||
              vuln.severity.toLowerCase() === "critical"
                ? "priority-high"
                : vuln.severity.toLowerCase() === "medium"
                ? "priority-medium"
                : "priority-low";

            htmlContent += `
            <div class="vulnerability-item ${severityClass}">
              <div class="vulnerability-title">${vuln.name}</div>
              <div class="vulnerability-meta">
                Sévérité: <strong>${vuln.severity}</strong>
              </div>
              <div class="vulnerability-description">${
                vuln.description || "Aucune description disponible"
              }</div>
            </div>`;
          });
        } else {
          htmlContent += `<p>Aucune vulnérabilité détectée.</p>`;
        }

        // Add HTTP headers section if available
        if (
          securityReport?.http_headers &&
          securityReport.http_headers.length > 0
        ) {
          htmlContent += `
          </div>

          <!-- HTTP Headers Section -->
          <div class="section">
            <h2 class="section-title">En-têtes HTTP</h2>
            <table style="width:100%; border-collapse: collapse;">
              <thead>
                <tr style="background-color: #f2f2f2;">
                  <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">En-tête</th>
                  <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Valeur</th>
                  <th style="padding: 10px; text-align: left; border: 1px solid #ddd;">Statut</th>
                </tr>
              </thead>
              <tbody>`;

          securityReport.http_headers.forEach((header) => {
            const statusClass =
              header.status.toLowerCase() === "good" ||
              header.status.toLowerCase() === "secure"
                ? "color: var(--success-color);"
                : header.status.toLowerCase() === "warning" ||
                  header.status.toLowerCase() === "info"
                ? "color: var(--warning-color);"
                : "color: var(--danger-color);";

            htmlContent += `
            <tr>
              <td style="padding: 10px; border: 1px solid #ddd;">${
                header.name
              }</td>
              <td style="padding: 10px; border: 1px solid #ddd;">${
                header.value || "Non défini"
              }</td>
              <td style="padding: 10px; border: 1px solid #ddd; ${statusClass}">
                <strong>${header.status}</strong>
              </td>
            </tr>`;
          });

          htmlContent += `
              </tbody>
            </table>`;
        }

        // Add Technical Sections - using any type since the API structure might vary
        const technicalSections =
          (securityReport as any)?.technical_sections || [];
        if (technicalSections.length > 0) {
          htmlContent += `
          </div>

          <!-- Technical Sections -->
          <div class="section">
            <h2 class="section-title">Sections Techniques</h2>`;

          technicalSections.forEach((section: any) => {
            const sectionScoreClass =
              section.score >= 80
                ? "score-high"
                : section.score >= 60
                ? "score-medium"
                : "score-low";

            htmlContent += `
            <div class="technical-section" style="margin-bottom: 30px; border: 1px solid #eee; border-radius: 8px; overflow: hidden;">
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background-color: #f9f9f9; border-bottom: 1px solid #eee;">
                <h3 style="margin: 0; font-size: 18px;">${section.title}</h3>
                <div class="score-badge ${sectionScoreClass}" style="font-weight: bold; padding: 5px 10px; border-radius: 20px; background-color: #f5f5f5;">
                  ${section.score}/100
                </div>
              </div>

              <div style="padding: 15px;">
                <div style="margin-bottom: 15px;">
                  <h4 style="margin-top: 0; font-size: 16px; color: var(--primary-color);">Description:</h4>
                  <p style="margin: 0; font-size: 14px;">${
                    section.description || "Aucune description disponible"
                  }</p>
                </div>

                <div style="margin-bottom: 15px;">
                  <h4 style="margin-top: 0; font-size: 16px; color: var(--primary-color);">Explication du Score:</h4>
                  <p style="margin: 0; font-size: 14px; padding: 10px; background-color: #f5f5f5; border-radius: 5px;">
                    ${
                      section.score >= 80
                        ? "Excellent! Cette section répond aux meilleures pratiques."
                        : section.score >= 60
                        ? "Acceptable, mais des améliorations sont possibles pour optimiser cette section."
                        : "Nécessite votre attention. Des problèmes importants ont été détectés dans cette section."
                    }
                  </p>
                </div>
              </div>
            </div>`;
          });
        }

        // Add SEO Sections if available
        if (seoReport?.categories && seoReport.categories.length > 0) {
          htmlContent += `
          </div>

          <!-- SEO Sections -->
          <div class="section" style="position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; right: 0; height: 5px; background: linear-gradient(to right, #d946ef, #9333ea);"></div>
            <h2 class="section-title" style="background: linear-gradient(to right, #d946ef, #9333ea); -webkit-background-clip: text; background-clip: text; color: transparent;">Sections SEO</h2>`;

          seoReport.categories.forEach((category: any) => {
            const categoryScoreClass =
              category.score >= 80
                ? "score-high"
                : category.score >= 60
                ? "score-medium"
                : "score-low";

            htmlContent += `
            <div class="technical-section" style="margin-bottom: 30px; border: 1px solid #eee; border-radius: 8px; overflow: hidden;">
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; background-color: #f9f9f9; border-bottom: 1px solid #eee;">
                <h3 style="margin: 0; font-size: 18px;">${category.name}</h3>
                <div class="score-badge ${categoryScoreClass}" style="font-weight: bold; padding: 5px 10px; border-radius: 20px; background-color: #f5f5f5;">
                  ${category.score}/100
                </div>
              </div>

              <div style="padding: 15px;">
                <div style="margin-bottom: 15px;">
                  <h4 style="margin-top: 0; font-size: 16px; color: #d946ef;">Description:</h4>
                  <p style="margin: 0; font-size: 14px;">${
                    category.description || "Aucune description disponible"
                  }</p>
                </div>
              </div>
            </div>`;
          });
        }

        // Add Performance section if available
        if (seoReport?.performance_metrics) {
          const performance = seoReport.performance_metrics;

          htmlContent += `
          </div>

          <!-- Performance Section -->
          <div class="section">
            <h2 class="section-title">Performance</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px;">
              <div style="flex: 1; min-width: 250px; background-color: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                <h3 style="font-size: 16px; margin-bottom: 10px;">Premier Affichage (FCP)</h3>
                <p style="font-size: 14px; color: #666;">${
                  performance.firstContentfulPaint || "Non disponible"
                } ms</p>
                <p style="font-size: 13px; margin-top: 10px;">Mesure le temps nécessaire pour afficher le premier contenu visible</p>
              </div>

              <div style="flex: 1; min-width: 250px; background-color: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                <h3 style="font-size: 16px; margin-bottom: 10px;">Affichage Principal (LCP)</h3>
                <p style="font-size: 14px; color: #666;">${
                  performance.largestContentfulPaint || "Non disponible"
                } ms</p>
                <p style="font-size: 13px; margin-top: 10px;">Mesure le temps nécessaire pour afficher le contenu principal</p>
              </div>

              <div style="flex: 1; min-width: 250px; background-color: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                <h3 style="font-size: 16px; margin-bottom: 10px;">Temps de Réponse Serveur</h3>
                <p style="font-size: 14px; color: #666;">${
                  performance.responseTime || "Non disponible"
                }</p>
                <p style="font-size: 13px; margin-top: 10px;">Mesure la réactivité de votre serveur web</p>
              </div>
            </div>

            <div style="background-color: #f0f7ff; padding: 15px; border-radius: 8px; margin-top: 20px;">
              <h3 style="font-size: 16px; margin-bottom: 10px; color: var(--primary-color);">Explication du Score</h3>
              <p style="font-size: 14px;">Le score de performance est principalement basé sur le temps de Premier Affichage Contentuel (FCP):</p>
              <ul style="margin-top: 10px; padding-left: 20px;">
                <li style="margin-bottom: 5px; font-size: 13px;">Score 80-100: Premier Affichage Contentuel (FCP) &lt; 2000ms - Excellent temps de chargement</li>
                <li style="margin-bottom: 5px; font-size: 13px;">Score 60-79: Premier Affichage Contentuel (FCP) entre 2000ms et 3000ms - Temps de chargement acceptable</li>
                <li style="margin-bottom: 5px; font-size: 13px;">Score &lt; 60: Premier Affichage Contentuel (FCP) &gt; 3000ms - Temps de chargement lent</li>
              </ul>
            </div>
          </div>`;
        }

        // Add Mobile Optimization section if available
        if (seoReport?.mobile_optimization) {
          const mobile = seoReport.mobile_optimization;

          htmlContent += `
          <!-- Mobile Optimization Section -->
          <div class="section">
            <h2 class="section-title">Optimisation Mobile</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px;">
              <div style="flex: 1; min-width: 250px; background-color: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                <h3 style="font-size: 16px; margin-bottom: 10px;">Viewport Configuré</h3>
                <p style="font-size: 14px; color: #666;">${
                  mobile.viewportConfiguration ? "Oui" : "Non"
                }</p>
                <p style="font-size: 13px; margin-top: 10px;">Contrôle l'affichage sur les appareils mobiles</p>
              </div>

              <div style="flex: 1; min-width: 250px; background-color: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                <h3 style="font-size: 16px; margin-bottom: 10px;">Taille des Polices</h3>
                <p style="font-size: 14px; color: #666;">${
                  mobile.fontSizes ? "Appropriée" : "À améliorer"
                }</p>
                <p style="font-size: 13px; margin-top: 10px;">Évalue si les polices sont lisibles sur mobile</p>
              </div>

              <div style="flex: 1; min-width: 250px; background-color: #f9f9f9; padding: 15px; border-radius: 8px; border-left: 4px solid var(--primary-color);">
                <h3 style="font-size: 16px; margin-bottom: 10px;">Éléments Tactiles</h3>
                <p style="font-size: 14px; color: #666;">${
                  mobile.touchTargets ? "Appropriés" : "Trop petits"
                }</p>
                <p style="font-size: 13px; margin-top: 10px;">Évalue si les boutons et liens sont facilement cliquables</p>
              </div>
            </div>
          </div>`;
        }

        // Close remaining tags
        htmlContent += `
        </div>

        <footer>
          <p>© ${new Date().getFullYear()} SiteChecker. Tous droits réservés.</p>
          <p>Ce rapport a été généré automatiquement et ne constitue pas un audit de sécurité complet.</p>
        </footer>
      </body>
      </html>`;

        // Create a Blob and download the HTML file
        const blob = new Blob([htmlContent], { type: "text/html" });
        const blobUrl = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = blobUrl;
        a.download = `rapport-${url.replace(/[^a-zA-Z0-9]/g, "-")}.html`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(blobUrl);

        // Success notification
        setTimeout(() => {
          toast({
            title: "Succès",
            description: "Rapport HTML téléchargé avec succès!",
            variant: "default",
          });
        }, 0);

        resolve();
      } catch (error) {
        console.error("Error generating HTML:", error);
        reject(error);
      }
    });
  }
}
