/**
 * VulnerabilitiesSection.tsx
 *
 * This component displays vulnerability information in the security report.
 */

import { Shield } from "lucide-react";
import {
  TechnicalSectionItem,
  VulnerabilitySectionProps,
  Finding,
  TechnicalDetail,
} from "./interfaces";

// Vulnerability explanations
const vulnerabilityTypes: Record<string, string> = {
  // Common web vulnerabilities
  xss: "Cross-Site Scripting (XSS) permet à un attaquant d'injecter du code malveillant qui s'exécute dans le navigateur des utilisateurs.",
  sqli: "SQL Injection permet à un attaquant d'injecter des commandes SQL malveillantes dans votre base de données.",
  csrf: "Cross-Site Request Forgery force un utilisateur authentifié à exécuter des actions non désirées sur votre site.",
  open_redirect:
    "Open Redirect permet à un attaquant de rediriger les utilisateurs vers des sites malveillants.",
  insecure_cookie:
    "Cookies non sécurisés peuvent être interceptés ou modifiés par des attaquants.",
  mixed_content:
    "Contenu mixte (HTTP sur HTTPS) peut être intercepté ou modifié par des attaquants.",
  outdated_library:
    "Bibliothèques obsolètes peuvent contenir des vulnérabilités connues et non corrigées.",

  // Security header related vulnerabilities
  csp: "Content Security Policy manquante ou mal configurée permet l'exécution de scripts malveillants et les attaques XSS.",
  hsts: "HTTP Strict Transport Security manquant permet des attaques de déclassement HTTPS vers HTTP et des attaques Man-in-the-Middle.",
  clickjacking:
    "Clickjacking Attack permet à un attaquant de tromper les utilisateurs en les incitant à cliquer sur des éléments cachés ou déguisés.",
  mime_sniffing:
    "MIME Sniffing Attack permet à un attaquant d'exploiter la détection automatique du type de contenu par le navigateur pour exécuter du code malveillant.",
  information_leakage:
    "Information Leakage expose des données sensibles via les en-têtes HTTP, permettant à un attaquant de recueillir des informations sur votre infrastructure.",
  unauthorized_feature_access:
    "Unauthorized Feature Access permet à un site web d'accéder à des fonctionnalités sensibles du navigateur comme la caméra ou le microphone sans autorisation explicite.",
  cross_origin_resource_attack:
    "Cross-Origin Resource Attack permet à un attaquant d'accéder à des ressources protégées sur votre site depuis un autre domaine.",
  cross_origin_window_opener_attack:
    "Cross-Origin Window Opener Attack permet à un attaquant d'accéder à la fenêtre parente lorsqu'un lien est ouvert dans un nouvel onglet.",
  man_in_the_middle:
    "Man-in-the-Middle Attack permet à un attaquant d'intercepter et potentiellement modifier les communications entre votre site et les utilisateurs.",
};

// Helper function to get explanation for a vulnerability type
const getVulnerabilityExplanation = (type: string): string => {
  // Normalize the type string for better matching
  const normalizedType = type.toLowerCase();

  // Direct lookup first
  if (vulnerabilityTypes[normalizedType]) {
    return vulnerabilityTypes[normalizedType];
  }

  // Check for partial matches in the vulnerability name
  if (
    normalizedType.includes("xss") ||
    normalizedType.includes("cross-site scripting")
  ) {
    return vulnerabilityTypes["xss"];
  }
  if (normalizedType.includes("clickjacking")) {
    return vulnerabilityTypes["clickjacking"];
  }
  if (normalizedType.includes("mime") || normalizedType.includes("sniffing")) {
    return vulnerabilityTypes["mime_sniffing"];
  }
  if (
    normalizedType.includes("information") ||
    normalizedType.includes("leakage")
  ) {
    return vulnerabilityTypes["information_leakage"];
  }
  if (
    normalizedType.includes("unauthorized") ||
    normalizedType.includes("feature access")
  ) {
    return vulnerabilityTypes["unauthorized_feature_access"];
  }
  if (
    normalizedType.includes("cross-origin resource") ||
    normalizedType.includes("cors")
  ) {
    return vulnerabilityTypes["cross_origin_resource_attack"];
  }
  if (
    normalizedType.includes("cross-origin window") ||
    normalizedType.includes("opener")
  ) {
    return vulnerabilityTypes["cross_origin_window_opener_attack"];
  }
  if (
    normalizedType.includes("man-in-the-middle") ||
    normalizedType.includes("mitm")
  ) {
    return vulnerabilityTypes["man_in_the_middle"];
  }
  if (
    normalizedType.includes("csp") ||
    normalizedType.includes("content-security-policy")
  ) {
    return vulnerabilityTypes["csp"];
  }
  if (
    normalizedType.includes("hsts") ||
    normalizedType.includes("strict-transport-security")
  ) {
    return vulnerabilityTypes["hsts"];
  }

  // Default fallback
  return "Vulnérabilité pouvant compromettre la sécurité de votre site web.";
};

/**
 * Consolidated VulnerabilitiesSection component that combines both the section configuration
 * and detailed display functionality
 */
const VulnerabilitiesSection = ({
  vulnerabilities,
  score = 0,
}: VulnerabilitySectionProps): TechnicalSectionItem => {
  // Handle missing data gracefully
  if (!vulnerabilities) {
    return {
      id: "security-3",
      title: "Vulnérabilités",
      icon: <Shield className="w-4 h-4" />,
      score: 0,
      useUnifiedModel: true,
      impactStatement: {
        text: "Les données de vulnérabilités ne sont pas disponibles. Cela peut être dû à une erreur lors de l'analyse.",
        positivePoints: [],
        negativePoints: [
          "Impossible d'analyser les vulnérabilités de votre site",
        ],
      },
      findings: [
        {
          status: "error",
          label: "Données manquantes",
          impact: "Impossible d'évaluer les vulnérabilités de votre site",
        },
      ],
      technicalDetails: [],
      actionReference: {
        count: 1,
        sectionName: "Vulnérabilités",
        actionWord: "Vérifier",
        targetId: "vulnerabilities-actions",
      },
    };
  }

  // Filter out vulnerabilities with 'present' status
  const filteredVulnerabilities = vulnerabilities.filter(
    (v) => v.status !== "present"
  );

  // Deduplicate vulnerabilities based on similar names
  const deduplicateVulnerabilities = (vulns: any[]) => {
    const deduplicated = [];
    const seenTypes = new Set<string>();

    for (const vuln of vulns) {
      // Normalize vulnerability name to identify core type
      const normalizedName = vuln.name
        .toLowerCase()
        .replace(/\s+/g, " ")
        .replace(
          /vulnerability|attack|\(due to missing security headers?\)/gi,
          ""
        )
        .trim();

      // Define core vulnerability types for better matching
      let coreType = normalizedName;
      if (normalizedName.includes("clickjacking")) {
        coreType = "clickjacking";
      } else if (
        normalizedName.includes("cross-site scripting") ||
        normalizedName.includes("xss")
      ) {
        coreType = "xss";
      } else if (
        normalizedName.includes("man-in-the-middle") ||
        normalizedName.includes("mitm")
      ) {
        coreType = "mitm";
      } else if (
        normalizedName.includes("mime") &&
        normalizedName.includes("sniffing")
      ) {
        coreType = "mime_sniffing";
      }

      // Only add if we haven't seen this core type before
      if (!seenTypes.has(coreType)) {
        seenTypes.add(coreType);
        deduplicated.push(vuln);
      }
    }

    return deduplicated;
  };

  const deduplicatedVulnerabilities = deduplicateVulnerabilities(
    filteredVulnerabilities
  );

  // If no vulnerabilities found after filtering, return a positive message
  if (deduplicatedVulnerabilities.length === 0) {
    return {
      id: "security-3",
      title: "Vulnérabilités",
      icon: <Shield className="w-4 h-4" />,
      score: 100,
      useUnifiedModel: true,
      impactStatement: {
        text: "Les vulnérabilités sont des faiblesses dans votre site web qui peuvent être exploitées par des attaquants. Aucune vulnérabilité n'a été détectée lors de l'analyse.",
        positivePoints: [
          "Aucune vulnérabilité détectée",
          "Votre site semble bien protégé contre les attaques courantes",
        ],
        negativePoints: [],
      },
      findings: [
        {
          status: "success",
          label: "Analyse de vulnérabilités",
          impact: "Aucune vulnérabilité détectée lors de l'analyse",
        },
      ],
      technicalDetails: [],
      actionReference: {
        count: 0,
        sectionName: "Vulnérabilités",
        actionWord: "Vérifier",
        targetId: "vulnerabilities-actions",
      },
    };
  }

  // Group vulnerabilities by severity
  const criticalVulns = deduplicatedVulnerabilities.filter(
    (v) => v.severity === "critical"
  );
  const highVulns = deduplicatedVulnerabilities.filter(
    (v) => v.severity === "high"
  );
  const mediumVulns = deduplicatedVulnerabilities.filter(
    (v) => v.severity === "medium"
  );
  const lowVulns = deduplicatedVulnerabilities.filter(
    (v) => v.severity === "low"
  );

  // Generate positive and negative points based on vulnerabilities
  const positivePoints =
    lowVulns.length === 0
      ? ["Aucune vulnérabilité de faible gravité détectée"]
      : [];

  const negativePoints = [];
  if (criticalVulns.length > 0) {
    negativePoints.push(
      `${criticalVulns.length} vulnérabilité${
        criticalVulns.length > 1 ? "s" : ""
      } critique${criticalVulns.length > 1 ? "s" : ""} détectée${
        criticalVulns.length > 1 ? "s" : ""
      }`
    );
  }
  if (highVulns.length > 0) {
    negativePoints.push(
      `${highVulns.length} vulnérabilité${
        highVulns.length > 1 ? "s" : ""
      } de gravité élevée détectée${highVulns.length > 1 ? "s" : ""}`
    );
  }
  if (mediumVulns.length > 0) {
    negativePoints.push(
      `${mediumVulns.length} vulnérabilité${
        mediumVulns.length > 1 ? "s" : ""
      } de gravité moyenne détectée${mediumVulns.length > 1 ? "s" : ""}`
    );
  }

  // Generate findings based on top vulnerabilities
  const findings: Finding[] = [];

  // Add critical vulnerabilities first
  for (const vuln of criticalVulns.slice(0, 2)) {
    let betterDescription = getVulnerabilityExplanation(vuln.name);

    // Ensure we have a description for common vulnerability types
    if (
      vuln.name.includes("Cross-Site Scripting") ||
      vuln.name.includes("XSS")
    ) {
      betterDescription =
        "Permet à un attaquant d'injecter du code malveillant qui s'exécute dans le navigateur des utilisateurs, compromettant potentiellement les données sensibles.";
    } else if (vuln.name.includes("Man-in-the-Middle")) {
      betterDescription =
        "Permet à un attaquant d'intercepter et potentiellement modifier les communications entre votre site et les utilisateurs, exposant des données sensibles.";
    }

    findings.push({
      status: "error",
      label: vuln.name,
      impact:
        betterDescription ||
        vuln.description ||
        "Vulnérabilité critique qui peut compromettre la sécurité de votre site",
    });
  }

  // Add high vulnerabilities next
  for (const vuln of highVulns.slice(0, 2)) {
    let betterDescription = getVulnerabilityExplanation(vuln.name);

    // Ensure we have a description for common vulnerability types
    if (
      vuln.name.includes("Cross-Site Scripting") ||
      vuln.name.includes("XSS")
    ) {
      betterDescription =
        "Permet à un attaquant d'injecter du code malveillant qui s'exécute dans le navigateur des utilisateurs, compromettant potentiellement les données sensibles.";
    } else if (vuln.name.includes("Man-in-the-Middle")) {
      betterDescription =
        "Permet à un attaquant d'intercepter et potentiellement modifier les communications entre votre site et les utilisateurs, exposant des données sensibles.";
    }

    findings.push({
      status: "error",
      label: vuln.name,
      impact:
        betterDescription ||
        vuln.description ||
        "Vulnérabilité importante qui peut affecter la sécurité de votre site",
    });
  }

  // Add medium vulnerabilities if we have space
  if (findings.length < 4) {
    for (const vuln of mediumVulns.slice(0, 4 - findings.length)) {
      let betterDescription = getVulnerabilityExplanation(vuln.name);

      // Ensure we have a description for common vulnerability types
      if (vuln.name.includes("Clickjacking")) {
        betterDescription =
          "Permet à un attaquant de tromper les utilisateurs en les incitant à cliquer sur des éléments cachés ou déguisés.";
      } else if (vuln.name.includes("MIME Sniffing")) {
        betterDescription =
          "Permet à un attaquant d'exploiter la détection automatique du type de contenu par le navigateur pour exécuter du code malveillant.";
      }

      findings.push({
        status: "warning",
        label: vuln.name,
        impact:
          betterDescription ||
          vuln.description ||
          "Vulnérabilité qui peut présenter un risque pour votre site",
      });
    }
  }

  // Generate technical details for the most severe vulnerabilities
  const technicalDetails: TechnicalDetail[] = [];

  // Add critical and high vulnerabilities to technical details
  const topVulns = [...criticalVulns, ...highVulns, ...mediumVulns].slice(0, 6);
  for (const vuln of topVulns) {
    const vulnType = vuln.type || "";
    const vulnName = vuln.name || "";

    // Get a better description based on the vulnerability name
    let betterDescription = getVulnerabilityExplanation(vulnName);

    // Ensure we have a description for common vulnerability types
    if (vulnName.includes("Cross-Site Scripting") || vulnName.includes("XSS")) {
      betterDescription =
        "Cross-Site Scripting (XSS) permet à un attaquant d'injecter du code malveillant qui s'exécute dans le navigateur des utilisateurs, compromettant potentiellement les données sensibles ou permettant le détournement de session.";
    } else if (vulnName.includes("Man-in-the-Middle")) {
      betterDescription =
        "Man-in-the-Middle Attack permet à un attaquant d'intercepter et potentiellement modifier les communications entre votre site et les utilisateurs, exposant des données sensibles ou permettant l'injection de contenu malveillant.";
    } else if (vulnName.includes("Clickjacking")) {
      betterDescription =
        "Clickjacking Attack permet à un attaquant de tromper les utilisateurs en les incitant à cliquer sur des éléments cachés ou déguisés, pouvant conduire à des actions non désirées ou au vol d'informations.";
    } else if (vulnName.includes("MIME Sniffing")) {
      betterDescription =
        "MIME Sniffing Attack permet à un attaquant d'exploiter la détection automatique du type de contenu par le navigateur pour exécuter du code malveillant, contournant les protections basées sur le type de contenu.";
    }

    technicalDetails.push({
      parameter: vuln.name,
      value: vuln.severity.toUpperCase(),
      impact:
        betterDescription ||
        vuln.description ||
        "Peut compromettre la sécurité de votre site",
      tooltip: vuln.cveId
        ? `CVE: ${vuln.cveId} - ${betterDescription}`
        : betterDescription,
    });
  }

  // Ensure we have positive points if score is high
  let finalPositivePoints = positivePoints;
  if (score >= 80 && positivePoints.length === 0) {
    finalPositivePoints = [
      "Votre site présente peu de vulnérabilités critiques ou de gravité élevée",
      "La sécurité globale de votre site est bonne",
    ];
  }

  // Return the unified model object
  return {
    id: "security-3",
    title: "Vulnérabilités",
    icon: <Shield className="w-4 h-4" />,
    score: score,
    useUnifiedModel: true,
    impactStatement: {
      text: "Les vulnérabilités sont des faiblesses dans votre site web qui peuvent être exploitées par des attaquants. Elles peuvent compromettre la sécurité de vos données, de vos utilisateurs ou de votre infrastructure.",
      positivePoints: finalPositivePoints,
      negativePoints: negativePoints,
    },
    findings: findings,
    technicalDetails: technicalDetails,
    actionReference: {
      count: criticalVulns.length + highVulns.length,
      sectionName: "Vulnérabilités",
      actionWord: "Corriger",
      targetId: "vulnerabilities-actions",
    },
  };
};

export default VulnerabilitiesSection;
