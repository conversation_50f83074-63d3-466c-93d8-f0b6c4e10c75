import React from "react";
import { PerformanceMetrics } from "@/types/report";
import { Check, X, AlertTriangle, HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// InfoTooltip Component - Reusable tooltip for technical terms
const InfoTooltip: React.FC<{ term: string; explanation: string }> = ({
  term,
  explanation,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-flex items-center cursor-help">
          {term}
          <HelpCircle className="h-3.5 w-3.5 ml-1 text-gray-400" />
        </span>
      </TooltipTrigger>
      <TooltipContent className="max-w-xs bg-gray-800 text-white border-gray-700">
        <p className="text-xs">{explanation}</p>
      </TooltipContent>
    </Tooltip>
  );
};

// Section Introduction Component
const SectionIntro: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="mb-4 p-3 neo-card bg-gray-50/50 border-l-4 border-blue-500/50">
      <p className="text-sm text-gray-600">{children}</p>
    </div>
  );
};

// Security Impact Component
const SecurityImpact: React.FC<{
  severity: "high" | "medium" | "low";
  children: React.ReactNode;
}> = ({ severity, children }) => {
  const bgColor =
    severity === "high"
      ? "bg-red-500/10 border-red-500/30"
      : severity === "medium"
      ? "bg-yellow-500/10 border-yellow-500/30"
      : "bg-blue-500/10 border-blue-500/30";

  const icon =
    severity === "high" ? (
      <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
    ) : severity === "medium" ? (
      <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />
    ) : (
      <HelpCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
    );

  return (
    <div className={`p-2 rounded-lg flex items-start gap-2 ${bgColor}`}>
      {icon}
      <p className="text-xs text-gray-700">{children}</p>
    </div>
  );
};

interface PerformanceDetailsProps {
  metrics?: PerformanceMetrics;
}

const PerformanceDetails: React.FC<PerformanceDetailsProps> = ({ metrics }) => {
  if (!metrics) {
    return (
      <p className="text-gray-600">Aucune donnée de performance disponible.</p>
    );
  }

  // Performance metrics explanations
  const metricsExplanations = {
    responseTime: {
      term: "Temps de réponse",
      explanation:
        "Temps nécessaire au serveur pour répondre à une requête. Un temps de réponse rapide est essentiel pour une bonne expérience utilisateur.",
      impact:
        "Un temps de réponse lent peut augmenter le taux de rebond et nuire au référencement, car Google prend en compte la vitesse de chargement dans son classement.",
      threshold: 300, // ms
    },
    pageSize: {
      term: "Taille de la page",
      explanation:
        "Taille totale de la page web, incluant HTML, CSS, JavaScript, images et autres ressources.",
      impact:
        "Une page trop volumineuse ralentit le chargement, particulièrement sur les connexions mobiles, et peut consommer inutilement la bande passante des utilisateurs.",
      threshold: 2000, // KB
    },
    firstContentfulPaint: {
      term: "Premier affichage de contenu",
      explanation:
        "Moment où le premier contenu visuel (texte, image, etc.) s'affiche à l'écran après le début du chargement.",
      impact:
        "Un FCP lent donne l'impression que votre site est peu réactif, ce qui peut frustrer les utilisateurs et les inciter à quitter votre site.",
      threshold: 1800, // ms
    },
    largestContentfulPaint: {
      term: "Plus grand affichage de contenu",
      explanation:
        "Moment où le plus grand élément de contenu visible s'affiche à l'écran, souvent une image ou un bloc de texte principal.",
      impact:
        "Le LCP est une métrique Core Web Vital de Google. Un LCP lent peut directement affecter votre classement dans les résultats de recherche.",
      threshold: 2500, // ms
    },
    cumulativeLayoutShift: {
      term: "Décalage cumulatif de mise en page",
      explanation:
        "Mesure de la stabilité visuelle d'une page. Un CLS élevé indique que des éléments se déplacent de façon inattendue pendant le chargement.",
      impact:
        "Les décalages de mise en page sont frustrants pour les utilisateurs et peuvent causer des clics accidentels. Le CLS est une métrique Core Web Vital de Google.",
      threshold: 0.1,
    },
    timeToInteractive: {
      term: "Temps d'interactivité",
      explanation:
        "Temps nécessaire pour que la page devienne pleinement interactive et réponde aux interactions utilisateur.",
      impact:
        "Un TTI élevé signifie que les utilisateurs peuvent voir votre page mais ne peuvent pas interagir avec elle, ce qui crée une expérience utilisateur médiocre.",
      threshold: 3800, // ms
    },
  };

  // Helper function to determine status based on thresholds
  const getMetricStatus = (
    metric: string,
    value: number
  ): "success" | "warning" | "error" => {
    const thresholds: Record<string, { warning: number; error: number }> = {
      responseTime: { warning: 300, error: 1000 },
      pageSize: { warning: 2000, error: 5000 },
      firstContentfulPaint: { warning: 1800, error: 3000 },
      largestContentfulPaint: { warning: 2500, error: 4000 },
      cumulativeLayoutShift: { warning: 0.1, error: 0.25 },
      timeToInteractive: { warning: 3800, error: 7300 },
    };

    if (!thresholds[metric]) return "success";

    if (metric === "cumulativeLayoutShift") {
      if (value <= thresholds[metric].warning) return "success";
      if (value <= thresholds[metric].error) return "warning";
      return "error";
    } else {
      if (value <= thresholds[metric].warning) return "success";
      if (value <= thresholds[metric].error) return "warning";
      return "error";
    }
  };

  // Parse numeric values from metrics with null checks
  const responseTime = metrics?.responseTime
    ? parseInt(metrics.responseTime.replace(/[^\d]/g, "")) || 0
    : 0;
  const pageSize = metrics?.pageSize
    ? parseInt(metrics.pageSize.replace(/[^\d]/g, "")) || 0
    : 0;
  const firstContentfulPaint = metrics?.firstContentfulPaint
    ? parseInt(metrics.firstContentfulPaint.replace(/[^\d]/g, "")) || 0
    : 0;
  const largestContentfulPaint = metrics?.largestContentfulPaint
    ? parseInt(metrics.largestContentfulPaint.replace(/[^\d]/g, "")) || 0
    : 0;
  const cumulativeLayoutShift = metrics?.cumulativeLayoutShift
    ? parseFloat(metrics.cumulativeLayoutShift) || 0
    : 0;
  const timeToInteractive = metrics?.timeToInteractive
    ? parseInt(metrics.timeToInteractive.replace(/[^\d]/g, "")) || 0
    : 0;

  // Determine status for each metric
  const responseTimeStatus = getMetricStatus("responseTime", responseTime);
  const pageSizeStatus = getMetricStatus("pageSize", pageSize);
  const fcpStatus = getMetricStatus(
    "firstContentfulPaint",
    firstContentfulPaint
  );
  const lcpStatus = getMetricStatus(
    "largestContentfulPaint",
    largestContentfulPaint
  );
  const clsStatus = getMetricStatus(
    "cumulativeLayoutShift",
    cumulativeLayoutShift
  );
  const ttiStatus = getMetricStatus("timeToInteractive", timeToInteractive);

  // Helper function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <Check className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <SectionIntro>
        Les performances de votre site web sont cruciales pour l'expérience
        utilisateur et le référencement. Google utilise les Core Web Vitals
        (LCP, CLS, FID) comme facteurs de classement. Un site rapide améliore le
        taux de conversion et réduit le taux de rebond.
      </SectionIntro>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="neo-card p-4">
          <div className="flex items-center justify-between mb-2">
            <InfoTooltip
              term={metricsExplanations.responseTime.term}
              explanation={metricsExplanations.responseTime.explanation}
            />
            {getStatusIcon(responseTimeStatus)}
          </div>
          <p className="text-gray-800 font-medium">
            {metrics?.responseTime || "N/A"}
          </p>
        </div>

        <div className="neo-card p-4">
          <div className="flex items-center justify-between mb-2">
            <InfoTooltip
              term={metricsExplanations.pageSize.term}
              explanation={metricsExplanations.pageSize.explanation}
            />
            {getStatusIcon(pageSizeStatus)}
          </div>
          <p className="text-gray-800 font-medium">
            {metrics?.pageSize || "N/A"}
          </p>
        </div>

        <div className="neo-card p-4">
          <div className="flex items-center justify-between mb-2">
            <InfoTooltip
              term={metricsExplanations.firstContentfulPaint.term}
              explanation={metricsExplanations.firstContentfulPaint.explanation}
            />
            {getStatusIcon(fcpStatus)}
          </div>
          <p className="text-gray-800 font-medium">
            {metrics?.firstContentfulPaint || "N/A"}
          </p>
        </div>

        <div className="neo-card p-4">
          <div className="flex items-center justify-between mb-2">
            <InfoTooltip
              term={metricsExplanations.largestContentfulPaint.term}
              explanation={
                metricsExplanations.largestContentfulPaint.explanation
              }
            />
            {getStatusIcon(lcpStatus)}
          </div>
          <p className="text-gray-800 font-medium">
            {metrics?.largestContentfulPaint || "N/A"}
          </p>
        </div>

        <div className="neo-card p-4">
          <div className="flex items-center justify-between mb-2">
            <InfoTooltip
              term={metricsExplanations.cumulativeLayoutShift.term}
              explanation={
                metricsExplanations.cumulativeLayoutShift.explanation
              }
            />
            {getStatusIcon(clsStatus)}
          </div>
          <p className="text-gray-800 font-medium">
            {metrics?.cumulativeLayoutShift || "N/A"}
          </p>
        </div>

        <div className="neo-card p-4">
          <div className="flex items-center justify-between mb-2">
            <InfoTooltip
              term={metricsExplanations.timeToInteractive.term}
              explanation={metricsExplanations.timeToInteractive.explanation}
            />
            {getStatusIcon(ttiStatus)}
          </div>
          <p className="text-gray-800 font-medium">
            {metrics?.timeToInteractive || "N/A"}
          </p>
        </div>
      </div>

      {/* Impact section */}
      <div className="mt-4">
        <h4 className="text-sm font-medium mb-2 text-gray-700">
          Impact sur l'expérience utilisateur et le SEO
        </h4>

        {responseTimeStatus !== "success" && (
          <SecurityImpact
            severity={responseTimeStatus === "error" ? "high" : "medium"}
          >
            {metricsExplanations.responseTime.impact}
          </SecurityImpact>
        )}

        {pageSizeStatus !== "success" && (
          <SecurityImpact
            severity={pageSizeStatus === "error" ? "high" : "medium"}
          >
            {metricsExplanations.pageSize.impact}
          </SecurityImpact>
        )}

        {lcpStatus !== "success" && (
          <SecurityImpact severity={lcpStatus === "error" ? "high" : "medium"}>
            {metricsExplanations.largestContentfulPaint.impact}
          </SecurityImpact>
        )}

        {clsStatus !== "success" && (
          <SecurityImpact severity={clsStatus === "error" ? "high" : "medium"}>
            {metricsExplanations.cumulativeLayoutShift.impact}
          </SecurityImpact>
        )}

        {ttiStatus !== "success" && (
          <SecurityImpact severity={ttiStatus === "error" ? "high" : "medium"}>
            {metricsExplanations.timeToInteractive.impact}
          </SecurityImpact>
        )}
      </div>

      {/* Recommendations section */}
      <div className="neo-card p-4">
        <h4 className="text-gray-800 font-medium mb-3">Recommandations</h4>
        <ul className="space-y-3">
          {responseTimeStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Optimisez votre serveur et utilisez un CDN pour réduire le temps
                de réponse.
              </span>
            </li>
          )}

          {pageSizeStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Réduisez la taille de la page en compressant les images,
                minifiant les fichiers CSS/JS et en utilisant la compression
                GZIP.
              </span>
            </li>
          )}

          {lcpStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Améliorez le LCP en optimisant les images, en utilisant un CDN
                et en implémentant la mise en cache du navigateur.
              </span>
            </li>
          )}

          {clsStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Réduisez le CLS en spécifiant les dimensions des images et des
                éléments publicitaires, et en évitant d'insérer du contenu
                dynamique au-dessus du contenu existant.
              </span>
            </li>
          )}

          {ttiStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Améliorez le TTI en réduisant le JavaScript, en différant les
                scripts non critiques et en optimisant le chemin critique de
                rendu.
              </span>
            </li>
          )}

          {responseTimeStatus === "success" &&
            pageSizeStatus === "success" &&
            lcpStatus === "success" &&
            clsStatus === "success" &&
            ttiStatus === "success" && (
              <li className="flex items-start gap-2 neo-card p-3 border border-green-500/30">
                <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 text-sm">
                  Toutes les métriques de performance sont excellentes.
                  Continuez à surveiller régulièrement les performances de votre
                  site.
                </span>
              </li>
            )}
        </ul>
      </div>
    </div>
  );
};

export default PerformanceDetails;
