import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { CheckCircle } from "lucide-react";
import Layout from "@/components/Layout";
import Card from "@/components/ui-custom/Card";
import Button from "@/components/ui-custom/Button";
import { safeStorage } from "../utils/storage";

export default function AdminEmailVerified() {
  const navigate = useNavigate();

  useEffect(() => {
    const setupAdminSession = async () => {
      // Ensure admin verification status is set
      await safeStorage.setItem("isAdminVerified", "true");
      await safeStorage.setItem("hasLoggedIn", "true");

      // Clear temporary admin login data
      await safeStorage.removeItem("adminPassword");

      // Keep isAdminLogin flag until user navigates away
      // This helps with session persistence

      // Auto-redirect after 5 seconds (optional)
      const timer = setTimeout(async () => {
        // Clear admin login state before auto-redirecting
        await safeStorage.removeItem("isAdminLogin");
        await safeStorage.removeItem("adminEmail");

        // Navigate to dashboard
        navigate("/dashboard");
      }, 5000);

      return () => clearTimeout(timer);
    };

    setupAdminSession();
  }, [navigate]);

  const handleDashboardRedirect = async () => {
    // Clear admin login state when manually navigating to dashboard
    await safeStorage.removeItem("isAdminLogin");
    await safeStorage.removeItem("adminEmail");

    // Keep the admin verification status
    navigate("/dashboard");
  };

  return (
    <Layout showNav={true} navType="second">
      <div className="min-h-screen flex flex-col justify-center items-center p-4 pt-20">
        <Card className="w-full max-w-md backdrop-blur-sm rounded-xl shadow-lg p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-10 h-10 text-green-500" />
          </div>

          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Bienvenue, administrateur !
          </h1>

          <p className="text-gray-600 mb-6">
            Votre compte administrateur a été vérifié avec succès. Vous avez
            maintenant accès à toutes les fonctionnalités premium.
          </p>

          <Button onClick={handleDashboardRedirect} className="w-full">
            Accéder au tableau de bord
          </Button>
        </Card>
      </div>
    </Layout>
  );
}
