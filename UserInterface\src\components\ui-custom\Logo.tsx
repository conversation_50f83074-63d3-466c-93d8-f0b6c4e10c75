import { Link } from "react-router-dom";

interface LogoProps {
  to?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
  showIcon?: boolean;
  showText?: boolean;
}

export default function Logo({
  to = "/",
  className = "",
  size = "md",
  showIcon = true,
  showText = true,
}: LogoProps) {
  const sizeClasses = {
    sm: {
      container: "text-lg gap-2",
      icon: "w-6 h-6",
    },
    md: {
      container: "text-2xl gap-3",
      icon: "w-8 h-8",
    },
    lg: {
      container: "text-3xl gap-4",
      icon: "w-10 h-10",
    },
  };

  const content = (
    <>
      {showIcon && (
        <img
          src="/Iconlogo.png"
          alt="SiteChecker Logo"
          className={`${sizeClasses[size].icon} object-contain`}
        />
      )}
      {showText && (
        <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-bold">
          SiteChecker
        </span>
      )}
    </>
  );

  if (to) {
    return (
      <Link
        to={to}
        className={`font-bold flex items-center transition-all hover:opacity-90 ${sizeClasses[size].container} ${className}`}
      >
        {content}
      </Link>
    );
  }

  return (
    <div
      className={`font-bold flex items-center ${sizeClasses[size].container} ${className}`}
    >
      {content}
    </div>
  );
}
