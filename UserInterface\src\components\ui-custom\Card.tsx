import React, {
  HTMLAttributes,
  forwardRef,
  isValidElement,
  Children,
} from "react";
import { cn } from "../../lib/utils"; // Adjust path if necessary

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  hoverEffect?: boolean;
  glassmorphism?: boolean;
  children?: React.ReactNode;
}

/**
 * Utility function to omit unwanted props from an object.
 * @param obj The source object
 * @param keys The keys to omit
 * @returns A new object without the specified keys
 */
function omitProps<T extends Record<string, any>>(
  obj: T,
  keys: string[]
): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) => !keys.includes(key))
  ) as Partial<T>;
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className = "",
      hoverEffect = false,
      glassmorphism = true,
      children,
      ...props
    },
    ref
  ) => {
    // Filter and validate children: Only React Elements are processed
    const validChildren = Children.toArray(children).filter(
      (child): child is React.ReactElement => isValidElement(child)
    );

    return (
      <div
        ref={ref}
        className={cn(
          "rounded-2xl border border-white/20 p-6 shadow-card transition-all duration-300",
          glassmorphism && "bg-white/98 backdrop-blur-sm",
          hoverEffect && "card-hover-effect",
          className
        )}
        {...props}
      >
        {/* Map valid children, omitting unwanted props */}
        {validChildren.map(
          (child, index) =>
            isValidElement(child)
              ? React.cloneElement(child, {
                  ...omitProps(child.props, ["data-lov-id", "data-lov-name"]), // Remove invalid props
                })
              : child // Return non-element children unchanged
        )}
      </div>
    );
  }
);

Card.displayName = "Card";

export default Card;
