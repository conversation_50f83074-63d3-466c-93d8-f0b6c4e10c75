import axios from "axios";
import { createBrowserHistory } from "history";

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:5012";

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
});

// Add request interceptor to include credentials for cookies
apiClient.interceptors.request.use(
  async (config) => {
    // With cookie-based auth, we just need to include credentials
    config.withCredentials = true;
    return config;
  },
  (error) => Promise.reject(error)
);

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Skip refresh token logic if 401 is from login endpoint
    if (
      error.response?.status === 401 &&
      !originalRequest._retry &&
      !originalRequest.url.includes("/api/auth/login")
    ) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then(() => {
            return apiClient(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        // Make the refresh token request with credentials to include cookies
        const refreshResponse = await axios.post(
          `${API_BASE_URL}/api/auth/refresh-token`,
          {}, // Empty body
          {
            withCredentials: true,
            timeout: 5000, // Add timeout to prevent hanging
          }
        );

        // With cookie-based auth, we don't need to manually update headers
        // The cookies will be sent automatically with subsequent requests

        // Check if we got a token in the response
        if (refreshResponse.data && refreshResponse.data.accessToken) {
          // Store the token in a cookie to ensure it's available for API requests
          document.cookie = `access_token=${refreshResponse.data.accessToken}; path=/; secure; samesite=strict`;

          // If the original request had an Authorization header, update it
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${refreshResponse.data.accessToken}`;
          }
        }

        // Process queue to allow waiting requests to continue
        processQueue(null, refreshResponse.data?.accessToken || "");
        return apiClient(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError, null);

        // Don't redirect if we're already on login page or it's a refresh-token request
        if (
          !window.location.pathname.includes("/login") &&
          !originalRequest.url.includes("/refresh-token")
        ) {
          // Use setTimeout to avoid React state updates during render
          setTimeout(() => {
            const history = createBrowserHistory();
            history.push("/login");
          }, 0);
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

// Global 401 handler
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const requestUrl = error.config?.url || "";

      // Don't handle 401s for auth endpoints - let the components handle these
      if (
        requestUrl.includes("/api/auth/refresh-token") ||
        requestUrl.includes("/api/auth/me") ||
        requestUrl.includes("/api/auth/login")
      ) {
        return Promise.reject(error);
      }

      // Only redirect to login if we're not already there
      if (!window.location.pathname.includes("/login")) {
        // Use setTimeout to avoid React state updates during render
        setTimeout(() => {
          const history = createBrowserHistory();
          history.push("/login");
        }, 0);
      }
    }
    return Promise.reject(error);
  }
);

export default apiClient;
