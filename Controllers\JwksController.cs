using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace SiteCheckerApp.Controllers
{
    [ApiController]
    [Route(".well-known")]
    public class JwksController : ControllerBase
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<JwksController> _logger;

        public JwksController(IConfiguration configuration, ILogger<JwksController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        [HttpGet("jwks.json")]
        public IActionResult GetJwks()
        {
            try
            {
                var secretKey = _configuration["Jwt:SecretKey"];
                if (string.IsNullOrEmpty(secretKey))
                {
                    _logger.LogError("JWT SecretKey is not configured");
                    return StatusCode(500, new { error = "JWT configuration error" });
                }

                // Utiliser la clé de la configuration 
                var secretKeyValue = secretKey;
                var keyBytes = Encoding.UTF8.GetBytes(secretKeyValue);

                // Vérifier si la clé est assez longue pour HS256 (au moins 32 octets / 256 bits)
                if (keyBytes.Length < 32)
                {
                    int keyBitsLength = keyBytes.Length * 8;
                    _logger.LogWarning("La clé JWT est trop courte ({KeyBitsLength} bits). Extension à 256 bits.", keyBitsLength);

                    // Étendre la clé à 32 octets en répétant ou en ajoutant des données
                    var extendedKeyBytes = new byte[32];

                    // Copier les octets existants
                    Array.Copy(keyBytes, extendedKeyBytes, Math.Min(keyBytes.Length, 32));

                    // Remplir le reste avec des valeurs dérivées de la clé originale
                    for (int i = keyBytes.Length; i < 32; i++)
                    {
                        extendedKeyBytes[i] = (byte)(keyBytes[i % keyBytes.Length] ^ (i * 13));
                    }

                    keyBytes = extendedKeyBytes;
                    _logger.LogInformation("Clé JWT étendue à {KeyBitsLength} bits.", keyBytes.Length * 8);
                }

                var key = new SymmetricSecurityKey(keyBytes)
                {
                    KeyId = "sitechecker-key-1" // Key ID for key rotation
                };

                var jwk = JsonWebKeyConverter.ConvertFromSecurityKey(key);
                var jwkSet = new
                {
                    keys = new[] { jwk }
                };

                return Ok(jwkSet);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating JWKS");
                return StatusCode(500, new { error = "Internal server error" });
            }
        }
    }
}