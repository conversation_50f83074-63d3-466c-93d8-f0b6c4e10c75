﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using SiteCheckerApp.Models;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace SiteCheckerApp.Services
{
    public interface IJwtService
    {
        string GenerateAccessToken(User user);
        Task<string> GenerateRefreshTokenAsync(User user);
        Task<(string AccessToken, string RefreshToken)> GenerateTokensAsync(User user);
        SecurityKey GetSigningKey();
        ClaimsPrincipal ValidateToken(string token);
    }

    public class JwtService : IJwtService
    {
        private readonly string _secretKey;
        private readonly string _issuer;
        private readonly string _audience;
        private readonly SecurityKey _signingKey;
        private readonly IRefreshTokenService _refreshTokenService;
        private readonly int _accessTokenExpiryMinutes;

        public JwtService(
            IConfiguration configuration,
            IRefreshTokenService refreshTokenService)
        {
            // Get values directly from environment variables with fallback to configuration
            var jwtSettings = configuration.GetSection("Jwt");
            _secretKey = Environment.GetEnvironmentVariable("JWT_SECRET_KEY") ?? jwtSettings["SecretKey"] ?? throw new InvalidOperationException("JWT SecretKey is not configured");
            _issuer = Environment.GetEnvironmentVariable("JWT_ISSUER") ?? jwtSettings["Issuer"] ?? "https://sitechecker-api.com";
            _audience = Environment.GetEnvironmentVariable("JWT_AUDIENCE") ?? jwtSettings["Audience"] ?? "https://sitechecker-client.com";

            // Ensure these values are not placeholders
            if (_issuer.Contains("${") || _audience.Contains("${"))
            {
                Console.WriteLine("WARNING: JWT issuer or audience contains placeholder values in JwtService. Using default values.");
                _issuer = "https://sitechecker-api.com";
                _audience = "https://sitechecker-client.com";
            }

            _refreshTokenService = refreshTokenService;

            Console.WriteLine($"JwtService initialized with issuer: {_issuer}, audience: {_audience}");

            // Read AccessTokenExpiryMinutes from environment variable with fallback to configuration
            string? expiryStr = Environment.GetEnvironmentVariable("JWT_ACCESS_TOKEN_EXPIRY_MINUTES") ?? jwtSettings["AccessTokenExpiryMinutes"];

            if (!int.TryParse(expiryStr, out int expiryMinutes))
            {
                expiryMinutes = 60;
            }
            _accessTokenExpiryMinutes = expiryMinutes;

            Console.WriteLine($"JWT token expiry: {_accessTokenExpiryMinutes} minutes");

            // Create symmetric key from the secret key
            var keyBytes = Encoding.UTF8.GetBytes(_secretKey);

            // Vérifier si la clé est assez longue pour HS256 (au moins 32 octets / 256 bits)
            if (keyBytes.Length < 32)
            {
                Console.WriteLine($"Attention: La clé JWT est trop courte ({keyBytes.Length * 8} bits). Extension à 256 bits.");
                // Étendre la clé à 32 octets en répétant ou en ajoutant des données
                var extendedKeyBytes = new byte[32];

                // Copier les octets existants
                Array.Copy(keyBytes, extendedKeyBytes, Math.Min(keyBytes.Length, 32));

                // Remplir le reste avec des valeurs dérivées de la clé originale
                for (int i = keyBytes.Length; i < 32; i++)
                {
                    extendedKeyBytes[i] = (byte)(keyBytes[i % keyBytes.Length] ^ (i * 13));
                }

                keyBytes = extendedKeyBytes;
                Console.WriteLine($"Clé JWT étendue à {keyBytes.Length * 8} bits.");
            }

            _signingKey = new SymmetricSecurityKey(keyBytes)
            {
                KeyId = "sitechecker-key-1" // Key ID for key rotation
            };
        }

        public string GenerateAccessToken(User user)
        {
            // Create claims for the token
            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new Claim(JwtRegisteredClaimNames.Email, user.Email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(ClaimTypes.Role, user.IdRole.ToString()), // Numeric role (1, 2, 3)
                new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString())
            };

            // Add string role name for admin users
            if (user.IdRole == 3)
            {
                claims.Add(new Claim(ClaimTypes.Role, "Admin"));
            }

            var token = new JwtSecurityToken(
                issuer: _issuer,
                audience: _audience,
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(_accessTokenExpiryMinutes),
                signingCredentials: new SigningCredentials(_signingKey, SecurityAlgorithms.HmacSha256));

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public async Task<string> GenerateRefreshTokenAsync(User user)
        {
            return await _refreshTokenService.GenerateRefreshTokenAsync(user.Id);
        }

        public async Task<(string AccessToken, string RefreshToken)> GenerateTokensAsync(User user)
        {
            var accessToken = GenerateAccessToken(user);
            var refreshToken = await GenerateRefreshTokenAsync(user);
            return (accessToken, refreshToken);
        }

        public SecurityKey GetSigningKey()
        {
            return _signingKey;
        }

        public ClaimsPrincipal ValidateToken(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = _signingKey,
                ValidateIssuer = true,
                ValidIssuer = _issuer,
                ValidateAudience = true,
                ValidAudience = _audience,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            try
            {
                var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
                if (validatedToken is JwtSecurityToken jwtToken &&
                    jwtToken.Header.Alg.Equals(SecurityAlgorithms.HmacSha256, StringComparison.InvariantCultureIgnoreCase))
                {
                    return principal;
                }
            }
            catch
            {
                // Token validation failed
            }
            return null!; // Utilisation de null! pour indiquer au compilateur que nous acceptons le retour null
        }
    }
}
