import { Check, X } from "lucide-react";

// Interface pour les deux types de props possibles
interface PasswordCriteriaListProps {
  // Props pour l'utilisation originale
  criteria?: {
    length: boolean;
    uppercase: boolean;
    number: boolean;
    specialChar: boolean;
  };
  password: string;

  // Props pour l'utilisation dans UserProfile
  confirmPassword?: string;
}

export function PasswordCriteriaList({
  criteria,
  password,
  confirmPassword,
}: PasswordCriteriaListProps) {
  // Si le mot de passe est vide, ne rien afficher
  if (!password) return null;

  // Si criteria est fourni, utiliser le mode original
  if (criteria) {
    // Détermine le prochain critère à afficher basé sur la priorité
    const getActiveCriteria = () => {
      if (!criteria.length)
        return {
          message: "Minimum 8 caractères",
          isValid: criteria.length,
        };

      if (!criteria.uppercase)
        return {
          message: "Au moins une majuscule",
          isValid: criteria.uppercase,
        };

      if (!criteria.number)
        return {
          message: "Au moins un chiffre",
          isValid: criteria.number,
        };

      if (!criteria.specialChar)
        return {
          message: "Caractère spécial (!@#$%^&*)",
          isValid: criteria.specialChar,
        };

      return null;
    };

    const activeCriteria = getActiveCriteria();

    return (
      <div className="space-y-2 mt-2 mb-4">
        {/* Affiche uniquement le critère actif si le mot de passe n'est pas complet */}
        {activeCriteria ? (
          <div className="flex items-center gap-2 p-2 rounded-lg bg-white/50">
            <div
              className={`w-4 h-4 rounded-full flex items-center justify-center transition-colors duration-200 ${
                activeCriteria.isValid ? "bg-green-500" : "bg-red-300"
              }`}
            >
              {activeCriteria.isValid && (
                <Check className="w-3 h-3 text-white" />
              )}
            </div>
            <span
              className={`text-sm transition-colors duration-200 ${
                activeCriteria.isValid ? "text-green-600" : "text-red-500"
              }`}
            >
              {activeCriteria.message}
            </span>
          </div>
        ) : (
          /* Affiche un message de succès quand tous les critères sont remplis */
          <div className="flex items-center gap-2 p-2 rounded-lg bg-green-50">
            <div className="w-4 h-4 rounded-full flex items-center justify-center bg-green-500">
              <Check className="w-3 h-3 text-white" />
            </div>
            <span className="text-sm text-green-600">
              Mot de passe sécurisé
            </span>
          </div>
        )}
      </div>
    );
  }

  // Sinon, utiliser le mode pour UserProfile
  // Critères de validation
  const hasMinLength = password.length >= 8;
  const hasMaxLength = password.length <= 15;
  const hasUppercase = /[A-Z]/.test(password);
  const hasLowercase = /[a-z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSpecialChar = /[!@#$%^&*]/.test(password);
  const passwordsMatch = confirmPassword
    ? password === confirmPassword && password !== ""
    : true;

  // Tous les critères sont-ils remplis?
  const allCriteriaMet =
    hasMinLength &&
    hasMaxLength &&
    hasUppercase &&
    hasLowercase &&
    hasNumber &&
    hasSpecialChar &&
    passwordsMatch;

  return (
    <div className="space-y-2 mt-2 mb-4">
      <h3 className="text-sm font-medium text-gray-700 mb-2">
        Exigences du mot de passe:
      </h3>

      <div className="space-y-1">
        <CriteriaItem
          isValid={hasMinLength && hasMaxLength}
          text="Entre 8 et 15 caractères"
        />

        <CriteriaItem
          isValid={hasUppercase}
          text="Au moins une lettre majuscule"
        />

        <CriteriaItem
          isValid={hasLowercase}
          text="Au moins une lettre minuscule"
        />

        <CriteriaItem isValid={hasNumber} text="Au moins un chiffre" />

        <CriteriaItem
          isValid={hasSpecialChar}
          text="Au moins un caractère spécial (!@#$%^&*)"
        />

        {confirmPassword && (
          <CriteriaItem
            isValid={passwordsMatch}
            text="Les mots de passe correspondent"
          />
        )}
      </div>

      {allCriteriaMet && (
        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md text-green-700 text-sm">
          Votre mot de passe répond à toutes les exigences de sécurité.
        </div>
      )}
    </div>
  );
}

// Composant pour afficher un critère individuel
function CriteriaItem({ isValid, text }: { isValid: boolean; text: string }) {
  return (
    <div className="flex items-center gap-2">
      {isValid ? (
        <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center">
          <Check className="w-3 h-3 text-green-600" />
        </div>
      ) : (
        <div className="w-5 h-5 rounded-full bg-red-100 flex items-center justify-center">
          <X className="w-3 h-3 text-red-500" />
        </div>
      )}
      <span
        className={`text-sm ${isValid ? "text-green-600" : "text-red-500"}`}
      >
        {text}
      </span>
    </div>
  );
}
