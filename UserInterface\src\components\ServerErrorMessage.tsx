import React, { useEffect } from "react";

interface ServerErrorMessageProps {
  message?: string;
  autoScroll?: boolean;
  details?: string;
}

/**
 * A component to display server error messages consistently across the application
 */
const ServerErrorMessage: React.FC<ServerErrorMessageProps> = ({
  message = "Le service d'analyse n'est pas disponible",
  autoScroll = true,
  details,
}) => {
  useEffect(() => {
    // Auto-scroll to the error message if enabled
    if (autoScroll) {
      setTimeout(() => {
        const errorElement = document.getElementById("server-error-message");
        if (errorElement) {
          errorElement.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }, 100);
    }
  }, [autoScroll]);

  // Determine the title based on the message
  const getTitle = () => {
    if (message.startsWith("Domaine introuvable")) {
      return "Domaine introuvable";
    }
    if (message.startsWith("Site inaccessible")) {
      return "Site inaccessible";
    }
    return "Service d'analyse indisponible";
  };

  return (
    <div
      id="server-error-message"
      className="mt-6 p-4 rounded-lg bg-amber-100 text-amber-800 border border-amber-300"
    >
      <div className="flex items-center space-x-3">
        <div className="text-amber-500 flex-shrink-0">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"></path>
            <path d="M12 9v4"></path>
            <path d="M12 17h.01"></path>
          </svg>
        </div>
        <div className="flex-1">
          <div className="font-medium text-amber-800">{getTitle()}</div>
        </div>
      </div>
    </div>
  );
};

export default ServerErrorMessage;
