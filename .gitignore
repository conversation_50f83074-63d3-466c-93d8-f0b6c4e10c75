# .NET Core
bin/
obj/
*.user
*.userosscache
*.suo
*.userprefs

# Visual Studio
.vs/
*.swp
*.*~
project.lock.json
.DS_Store
*.pyc

# Node
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# Visual Studio cache/options directory
.vs/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# NuGet Packages
*.nupkg
# The packages folder can be ignored because of Package Restore
**/[Pp]ackages/*
# except build/, which is used as an MSBuild target.
!**/[Pp]ackages/build/
# NuGet v3's project.json files produces more ignorable files
*.nuget.props
*.nuget.targets

# Azure Functions localsettings file
local.settings.json

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json 

    

/ScanPy/scanner-env/
/ScanPy