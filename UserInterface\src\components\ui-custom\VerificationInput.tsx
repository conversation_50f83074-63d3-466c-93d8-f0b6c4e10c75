
import { cn } from "@/lib/utils";
import { KeyboardEvent, useEffect, useRef, useState } from "react";

interface VerificationInputProps {
  length?: number;
  onChange: (code: string) => void;
  className?: string;
}

export default function VerificationInput({
  length = 5,
  onChange,
  className,
}: VerificationInputProps) {
  const [code, setCode] = useState<string[]>(Array(length).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    onChange(code.join(""));
  }, [code, onChange]);

  const handleChange = (value: string, index: number) => {
    const newCode = [...code];
    newCode[index] = value.slice(-1);
    setCode(newCode);

    // Auto-focus next input
    if (value && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === "Backspace" && !code[index] && index > 0) {
      // Focus previous on backspace if current is empty
      const prevIndex = index - 1;
      inputRefs.current[prevIndex]?.focus();
      
      // Clear previous value
      const newCode = [...code];
      newCode[prevIndex] = "";
      setCode(newCode);
      
      e.preventDefault();
    } else if (e.key === "ArrowLeft" && index > 0) {
      inputRefs.current[index - 1]?.focus();
      e.preventDefault();
    } else if (e.key === "ArrowRight" && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
      e.preventDefault();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").slice(0, length);
    
    if (!/^\d+$/.test(pastedData)) return;
    
    const newCode = [...code];
    
    for (let i = 0; i < pastedData.length; i++) {
      if (i < length) {
        newCode[i] = pastedData[i];
      }
    }
    
    setCode(newCode);
    
    if (pastedData.length === length && inputRefs.current[length - 1]) {
      inputRefs.current[length - 1]?.focus();
    } else if (pastedData.length < length && inputRefs.current[pastedData.length]) {
      inputRefs.current[pastedData.length]?.focus();
    }
  };

  return (
    <div className={cn("flex justify-center gap-3", className)}>
      {Array.from({ length }).map((_, index) => (
        <input
          key={index}
          ref={(el) => (inputRefs.current[index] = el)}
          type="text"
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={1}
          value={code[index] || ""}
          onChange={(e) => handleChange(e.target.value, index)}
          onKeyDown={(e) => handleKeyDown(e, index)}
          onPaste={handlePaste}
          className="w-12 h-12 text-center text-xl font-medium bg-white/60 backdrop-blur-sm border border-white/20 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-sitechecker-blue/20 focus:border-sitechecker-blue input-focus-ring transition-all"
        />
      ))}
    </div>
  );
}
