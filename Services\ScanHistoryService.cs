using SiteCheckerApp.Data;
using SiteCheckerApp.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace SiteCheckerApp.Services
{
    public interface IScanHistoryService
    {
        Task<ScanHistory> AddScanHistoryAsync(ScanHistory scanHistory);
        Task<List<ScanHistory>> GetUserScanHistoryAsync(Guid userId);
        Task<int> GetTotalScansCountAsync();
        Task<int> GetSecurityScansCountAsync();
        Task<int> GetSeoScansCountAsync();
        Task<Dictionary<string, int>> GetScanCountsByDateRangeAsync(DateTime startDate, DateTime endDate);
    }

    public class ScanHistoryService : IScanHistoryService
    {
        private readonly AppDbContext _context;

        public ScanHistoryService(AppDbContext context)
        {
            _context = context;
        }

        public async Task<ScanHistory> AddScanHistoryAsync(ScanHistory scanHistory)
        {
            _context.ScanHistories.Add(scanHistory);
            await _context.SaveChangesAsync();
            return scanHistory;
        }

        public async Task<List<ScanHistory>> GetUserScanHistoryAsync(Guid userId)
        {
            return await _context.ScanHistories
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.ScanDate)
                .ToListAsync();
        }

        public async Task<int> GetTotalScansCountAsync()
        {
            return await _context.ScanHistories.CountAsync();
        }

        public async Task<int> GetSecurityScansCountAsync()
        {
            return await _context.ScanHistories
                .Where(s => s.ScanType.ToLower() == "security")
                .CountAsync();
        }

        public async Task<int> GetSeoScansCountAsync()
        {
            return await _context.ScanHistories
                .Where(s => s.ScanType.ToLower() == "seo")
                .CountAsync();
        }

        public async Task<Dictionary<string, int>> GetScanCountsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var scans = await _context.ScanHistories
                .Where(s => s.ScanDate >= startDate && s.ScanDate <= endDate)
                .GroupBy(s => s.ScanDate.Date)
                .Select(g => new { Date = g.Key, Count = g.Count() })
                .ToListAsync();

            return scans.ToDictionary(
                s => s.Date.ToString("yyyy-MM-dd"),
                s => s.Count
            );
        }
    }
}
