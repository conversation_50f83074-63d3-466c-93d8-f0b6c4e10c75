import React, { ButtonHTMLAttributes, forwardRef } from "react";
import { ArrowRight } from "lucide-react";

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  loading?: boolean;
  withArrow?: boolean;
  children?: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className = "",
      variant = "primary",
      size = "md",
      loading = false,
      withArrow = false,
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    const variantClasses = {
      primary:
        "button-gradient text-white shadow-button hover:shadow-button-hover",
      secondary: "bg-white text-sitechecker-blue shadow-sm hover:shadow-md",
      outline:
        "bg-transparent border border-white/30 text-white hover:bg-white/10",
      ghost: "bg-transparent text-white hover:bg-white/10",
    };

    const sizeClasses = {
      sm: "py-2 px-4 text-sm",
      md: "py-3 px-6 text-base",
      lg: "py-4 px-8 text-lg",
    };

    return (
      <button
        ref={ref}
        className={`relative rounded-full font-medium transition-all focus:outline-none focus:ring-2 focus:ring-sitechecker-blue/20 active:scale-[0.98] ${
          variantClasses[variant]
        } ${sizeClasses[size]} ${
          disabled || loading ? "opacity-70 cursor-not-allowed" : ""
        } ${className}`}
        disabled={disabled || loading}
        {...props}
      >
        <span className="flex items-center justify-center gap-2">
          {loading && (
            <span className="w-5 h-5 rounded-full border-2 border-transparent border-t-current animate-spin"></span>
          )}
          {children}
          {withArrow && !loading && <ArrowRight className="w-4 h-4" />}
        </span>
      </button>
    );
  }
);

Button.displayName = "Button";

export default Button;
