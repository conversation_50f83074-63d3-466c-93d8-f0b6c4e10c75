import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useRef,
} from "react";
import { useNavigate, useLocation } from "react-router-dom";
import axios from "axios";
import apiClient from "../services/apiClient";
import { removeToken, saveToken, getToken } from "../utils/tokenStorage";
import { safeStorage } from "../utils/storage";
import { toast } from "sonner";
import { apiService } from "../services/apiService";
import { ScanDepth, SecurityReport } from "../types/report";

const API_BASE_URL = import.meta.env.VITE_API_URL || "http://localhost:5012";

interface User {
  id: string;
  email: string;
  username?: string;
  role: string;
}

interface LoginResponse {
  message: string;
  userId?: string;
  email?: string;
  username?: string;
  role?: number;
  requiresTwoFactor?: boolean;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  setUser: (user: User | null) => void;
  setIsAuthenticated: (isAuthenticated: boolean) => void;
  setAccessToken: (token: string) => void;
  login: (
    email: string,
    password: string,
    captchaToken?: string,
    isV2Fallback?: boolean
  ) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  loading: boolean;
  refreshAuth: () => Promise<void>;
  fetchSecurityData: (
    url: string,
    scanDepth?: ScanDepth
  ) => Promise<SecurityReport>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Create a separate named component for the provider
export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [accessToken, setAccessToken] = useState<string>("");
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  const checkAuth = useCallback(async () => {
    try {
      const isResetPasswordFlow =
        location.pathname.startsWith("/reset-password");
      if (isResetPasswordFlow) {
        setLoading(false);
        return;
      }

      const isProtectedRoute =
        location.pathname.startsWith("/dashboard") ||
        location.pathname.startsWith("/report");

      if (!isProtectedRoute) {
        setLoading(false);
        return;
      }

      // Check if user has logged in or is in admin verification flow
      const hasLoggedIn = await safeStorage.getItem("hasLoggedIn");
      const isAdminLogin = await safeStorage.getItem("isAdminLogin");
      const isAdminVerified = await safeStorage.getItem("isAdminVerified");

      // If user is in admin verification flow, don't redirect
      if (isAdminLogin === "true") {
        console.log("[AuthContext] User is in admin login flow");

        // If on verify code page, don't redirect
        if (location.pathname.includes("/verify-code")) {
          setLoading(false);
          return;
        }

        // If admin is verified but on another page, continue with auth check
        if (isAdminVerified !== "true") {
          // If not on verify code page, redirect to login
          setIsAuthenticated(false);
          setUser(null);
          setLoading(false);

          if (!location.pathname.startsWith("/login")) {
            navigate("/login", { state: { from: location } });
          }
          return;
        }
      }

      // If not logged in and not in admin flow, exit early
      if (hasLoggedIn !== "true" && isAdminVerified !== "true") {
        setIsAuthenticated(false);
        setUser(null);
        setLoading(false);

        if (!location.pathname.startsWith("/login")) {
          navigate("/login", { state: { from: location } });
        }
        return;
      }

      // First try to get the current user
      try {
        // Make the request with credentials to include cookies
        const userResponse = await apiClient.get("/api/auth/me", {
          withCredentials: true,
        });

        if (userResponse.data && userResponse.data.id) {
          setUser({
            id: userResponse.data.id,
            email: userResponse.data.email,
            username: userResponse.data.username,
            role: userResponse.data.role,
          });
          setIsAuthenticated(true);
          setLoading(false);
          return;
        }
      } catch (error) {
        console.log("[AuthContext] Initial /me request failed, trying refresh");
      }

      // If we get here, we need to refresh the token
      try {
        // Use a direct axios call to avoid interceptors
        const refreshResponse = await axios.post(
          `${API_BASE_URL}/api/auth/refresh-token`,
          {},
          {
            withCredentials: true,
            timeout: 5000, // Add timeout to prevent hanging
          }
        );

        if (refreshResponse.data && refreshResponse.data.accessToken) {
          setAccessToken(refreshResponse.data.accessToken);

          // Try again with the new token
          const retryResponse = await axios.get(`${API_BASE_URL}/api/auth/me`, {
            withCredentials: true,
            headers: {
              Authorization: `Bearer ${refreshResponse.data.accessToken}`,
            },
            timeout: 5000, // Add timeout to prevent hanging
          });

          if (retryResponse.data && retryResponse.data.id) {
            setUser({
              id: retryResponse.data.id,
              email: retryResponse.data.email,
              username: retryResponse.data.username,
              role: retryResponse.data.role,
            });
            setIsAuthenticated(true);
            setLoading(false);
            return;
          }
        }
      } catch (refreshError) {
        console.error("[AuthContext] Token refresh failed:", refreshError);
        // Continue to the fallback below
      }

      // If we get here, authentication failed
      setIsAuthenticated(false);
      setUser(null);

      // Clear any stale login state
      await safeStorage.removeItem("hasLoggedIn");
      await safeStorage.removeItem("isAdminVerified");

      if (!location.pathname.startsWith("/login")) {
        navigate("/login", { state: { from: location } });
      }
    } catch (error) {
      console.error("[AuthContext] checkAuth error:", error);
      setIsAuthenticated(false);
      setUser(null);

      if (!location.pathname.startsWith("/login")) {
        navigate("/login", { state: { from: location } });
      }
    } finally {
      setLoading(false);
    }
  }, [location.pathname, navigate]);

  useEffect(() => {
    // Use setTimeout to avoid React state updates during render
    const initAuth = () => {
      if (
        location.pathname.startsWith("/dashboard") ||
        location.pathname.startsWith("/report")
      ) {
        checkAuth();
      } else {
        setLoading(false);
      }
    };

    // Delay the auth check slightly to avoid React warnings
    setTimeout(initAuth, 0);
  }, [checkAuth, location.pathname]);

  const login = useCallback(
    async (
      email: string,
      password: string,
      captchaToken?: string,
      isV2Fallback?: boolean
    ): Promise<LoginResponse> => {
      try {
        const payload: any = {
          email,
          password,
        };
        if (captchaToken) {
          payload.captchaToken = captchaToken;
        }
        const response = await apiClient.post("/api/auth/login", payload);

        if (response.data.requiresTwoFactor) {
          await safeStorage.setItem("adminEmail", email);
          await safeStorage.setItem("adminPassword", password);
          await safeStorage.setItem("isAdminLogin", "true");
          return response.data;
        }

        setUser({
          id: response.data.userId,
          email: response.data.email,
          username: response.data.username,
          role: response.data.role.toString(),
        });
        setIsAuthenticated(true);

        // With cookie-based auth, we don't need to manually store tokens
        // The server sets the cookies automatically
        // We just need to store the user info
        if (response.data.token) {
          // Keep the token in memory for this session
          setAccessToken(response.data.token);
        }

        await safeStorage.setItem("hasLoggedIn", "true");

        await checkAuth();
        return response.data;
      } catch (error: any) {
        await removeToken("access_token");
        await removeToken("refresh_token");
        await safeStorage.removeItem("hasLoggedIn");
        throw error;
      }
    },
    [checkAuth]
  );

  const logout = useCallback(async () => {
    try {
      // Call the logout endpoint which will clear the cookies on the server side
      await apiService.logout();
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      // Clear UI state
      await safeStorage.removeItem("isAdminLogin");
      await safeStorage.removeItem("adminEmail");
      await safeStorage.removeItem("adminPassword");
      await safeStorage.removeItem("hasLoggedIn");

      // Reset local state
      setUser(null);
      setIsAuthenticated(false);
      setAccessToken("");
      navigate("/login", { replace: true });
    }
  }, [navigate]);

  const register = useCallback(
    async (email: string, password: string) => {
      try {
        await apiClient.post("/api/auth/register", {
          email,
          password,
        });
        navigate("/login");
      } catch (error: any) {
        if (
          error.response &&
          error.response.data &&
          error.response.data.errors
        ) {
          const errors = error.response.data.errors;
          const errorMessages = Object.values(errors).flat();
          throw new Error(errorMessages.join("\n"));
        } else if (
          error.response &&
          error.response.data &&
          error.response.data.message
        ) {
          throw new Error(error.response.data.message);
        } else {
          throw new Error("Erreur de connexion au serveur");
        }
      }
    },
    [navigate]
  );

  const refreshAuth = useCallback(async () => {
    setLoading(true);
    await checkAuth();
  }, [checkAuth]);

  const apiClientInterceptor = useCallback(() => {
    apiClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          await logout();
        }
        return Promise.reject(error);
      }
    );
  }, [logout]);

  useEffect(() => {
    apiClientInterceptor();
  }, [apiClientInterceptor]);

  const fetchSecurityData = useCallback(
    async (url: string, scanDepth: ScanDepth = ScanDepth.BASIC) => {
      try {
        return await apiService.fetchSecurityData(url, scanDepth);
      } catch (error) {
        console.error("Error fetching security data:", error);
        throw error;
      }
    },
    []
  );

  const value = React.useMemo(
    () => ({
      isAuthenticated,
      user,
      setUser,
      setIsAuthenticated: (value: boolean) => setIsAuthenticated(value),
      setAccessToken,
      login,
      logout,
      register,
      loading,
      refreshAuth,
      fetchSecurityData,
    }),
    [
      isAuthenticated,
      user,
      setAccessToken,
      login,
      logout,
      register,
      loading,
      refreshAuth,
      fetchSecurityData,
    ]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Named export for the hook
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// Default export remains the Provider for backward compatibility
export default AuthProvider;
