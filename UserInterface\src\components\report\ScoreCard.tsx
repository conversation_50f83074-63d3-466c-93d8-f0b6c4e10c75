import React, { ReactNode, useEffect, useRef } from "react";
import { SecurityReport, SeoReport } from "@/types/report";
import { useTranslation } from "react-i18next";

interface ScoreCardProps {
  type: "security" | "seo";
  report: SecurityReport | SeoReport | null;
  icon: ReactNode;
}

const cardColors = {
  security: {
    primary: "rgba(155, 135, 245, 1)", // Purple color
    secondary: "rgba(30, 174, 219, 1)", // Blue color
    accent: "#9b87f5",
    cardBg: "bg-[#F0F8FF]", // Alice blue - cool light tone for security
    cardBorder: "border-blue-100",
    iconBg: "bg-blue-50",
  },
  seo: {
    primary: "rgba(217, 70, 239, 1)",
    secondary: "rgba(30, 174, 219, 1)",
    accent: "#d946ef",
    cardBg: "bg-[#FFF9F0]", // Soft ivory - warm light tone for SEO
    cardBorder: "border-gray-200",
    iconBg: "bg-purple-50",
  },
};

const ScoreCard: React.FC<ScoreCardProps> = ({ type, report, icon }) => {
  const { t } = useTranslation();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const score = report?.overall_score || 0;
  const colors = cardColors[type];

  // Create a copy of the categories array
  let categories = [...(report?.categories || [])];

  // For security report, add JavaScript and API security as custom categories if they exist
  if (type === "security" && report) {
    const securityReport = report as SecurityReport;

    // Add JavaScript Security category if score exists
    if (securityReport.js_security_score !== undefined) {
      categories.push({
        name: "JavaScript Security",
        score: securityReport.js_security_score,
        details: [],
      });
    }

    // Add API Security category if score exists
    if (securityReport.api_security_score !== undefined) {
      categories.push({
        name: "API Security",
        score: securityReport.api_security_score,
        details: [],
      });
    }
  }

  useEffect(() => {
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      const size = canvas.width;
      const centerX = size / 2;
      const centerY = size / 2;
      const radius = size * 0.4;
      const lineWidth = size * 0.05;

      ctx.clearRect(0, 0, size, size);

      // Track (background circle)
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.strokeStyle = "rgba(0, 0, 0, 0.1)";
      ctx.lineWidth = lineWidth;
      ctx.stroke();

      // Progress (filled circle)
      const startAngle = -Math.PI / 2;
      const endAngle = startAngle + (Math.PI * 2 * score) / 100;

      // Create a gradient that goes from top-left to bottom-right for better visual effect
      const progressGradient = ctx.createLinearGradient(0, 0, size, size);
      progressGradient.addColorStop(0, colors.primary);
      progressGradient.addColorStop(1, colors.secondary);

      // Draw progress arc without glow
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.strokeStyle = progressGradient;
      ctx.lineWidth = lineWidth;
      ctx.lineCap = "round";
      ctx.stroke();

      // Score text without glow
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.font = `bold ${size * 0.3}px sans-serif`;
      ctx.fillStyle = "#1a1f2c"; // Dark text for better contrast
      ctx.fillText(`${score}`, centerX, centerY - size * 0.01);

      // "/100" text
      ctx.font = `${size * 0.12}px sans-serif`;
      ctx.fillStyle = "rgba(26, 31, 44, 0.7)"; // Semi-transparent dark text
      ctx.fillText(t("common.score"), centerX, centerY + size * 0.15);
    }
  }, [score, colors]);

  return (
    <div
      className={`p-6 ${colors.cardBg} rounded-xl border ${colors.cardBorder} shadow-md`}
    >
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <div
            className={`rounded-full p-3 mr-4 ${
              type === "security" ? colors.iconBg : "bg-purple-50"
            }`}
          >
            {icon}
          </div>
          <h3 className="text-xl font-bold text-gray-800">
            {type === "security" ? t("security.score") : t("seo.score")}
          </h3>
        </div>
      </div>

      <div className="flex flex-col md:flex-row items-center">
        {/* Circular Progress with benchmark comparison */}
        <div className="flex flex-col justify-center items-center mb-6 md:mb-0 md:mr-6">
          <div className="relative" style={{ width: "160px" }}>
            <canvas
              ref={canvasRef}
              width={160}
              height={160}
              className="aspect-square"
            />
          </div>
        </div>

        {/* Category Scores */}
        <div className="w-full space-y-3 max-w-lg">
          {categories
            .filter((category) => {
              // Filter categories based on type
              if (type === "security") {
                // Show SSL/TLS, HTTP Headers, and Vulnerabilities sections for security card
                return (
                  category.name.toLowerCase().includes("ssl") ||
                  category.name.toLowerCase().includes("header") ||
                  category.name.toLowerCase().includes("vulnerab")
                );
              } else if (type === "seo") {
                // Only show Meta Tags, Mobile Optimization, and Performance for SEO card
                return (
                  category.name.toLowerCase().includes("meta") ||
                  category.name.toLowerCase().includes("mobile") ||
                  category.name.toLowerCase().includes("performance")
                );
              }
              return false;
            })
            .map((category, index) => {
              return (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700">
                      {t(
                        category.name.toLowerCase().includes("ssl")
                          ? "security.ssl"
                          : category.name.toLowerCase().includes("header")
                          ? "security.headers"
                          : category.name.toLowerCase().includes("vulnerab")
                          ? "security.vulnerabilities"
                          : category.name.toLowerCase().includes("meta")
                          ? "seo.meta"
                          : category.name.toLowerCase().includes("mobile")
                          ? "seo.mobile"
                          : category.name.toLowerCase().includes("performance")
                          ? "seo.performance"
                          : category.name
                      )}
                    </span>
                    <div className="flex items-center">
                      <span
                        className="text-xs font-bold"
                        style={{
                          color:
                            category.score >= 80
                              ? "#16a34a"
                              : category.score >= 50
                              ? "#ca8a04"
                              : "#dc2626",
                        }}
                      >
                        {category.score}/100
                      </span>
                    </div>
                  </div>
                  <div
                    className="h-2 w-full rounded-full bg-gray-100 overflow-hidden"
                    role="progressbar"
                    aria-valuenow={category.score}
                    aria-valuemin={0}
                    aria-valuemax={100}
                  >
                    <div
                      className="h-2 rounded-full transition-all duration-500"
                      style={{
                        width: `${category.score}%`,
                        background: `linear-gradient(90deg, ${colors.primary} 0%, ${colors.secondary} 100%)`,
                      }}
                    />
                  </div>
                </div>
              );
            })}
        </div>
      </div>
    </div>
  );
};

export default ScoreCard;
