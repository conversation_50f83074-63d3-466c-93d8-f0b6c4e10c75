using Microsoft.EntityFrameworkCore;
using SiteCheckerApp.Data;
using SiteCheckerApp.Models;

namespace SiteCheckerApp.Repositories
{
    public interface IUserRepository
    {
        Task<User?> GetByEmailAsync(string email);
        Task<User?> GetByUsernameAsync(string username);
        Task CreateAsync(User user);
        Task UpdateAsync(User user);
        Task<bool> VerifyUserAsync(string email, string verificationToken);
        Task<IEnumerable<User>> GetAllAsync();
        Task<User?> GetByVerificationTokenAsync(string token);
        Task<User?> GetByResetTokenAsync(string token);
        Task<User?> GetUserByOtpAsync(string otp);
        Task<User?> GetByIdAsync(Guid id);
        Task<User?> GetByRefreshTokenAsync(string refreshToken);
    }

    public class UserRepository : IUserRepository
    {
        private readonly AppDbContext _context;

        public UserRepository(AppDbContext context)
        {
            _context = context;
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == email);
            if (user != null)
            {
                user.CreatedAt = user.CreatedAt == default ? DateTime.UtcNow : user.CreatedAt;
                user.UpdatedAt = user.UpdatedAt == default ? DateTime.UtcNow : user.UpdatedAt;
            }
            return user;
        }

        public async Task<User?> GetByUsernameAsync(string username)
        {
            return await _context.Users.FirstOrDefaultAsync(u => u.Username == username);
        }

        public async Task CreateAsync(User user)
        {
            user.CreatedAt = DateTime.UtcNow;
            user.UpdatedAt = DateTime.UtcNow;
            try
            {
                await _context.Users.AddAsync(user);
                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Log exception details as needed
                Console.WriteLine("An error occurred while creating the user: " + ex.Message);
                throw new Exception("An error occurred while creating the user. Please check the logs for more details.");
            }
        }

        public async Task<bool> VerifyUserAsync(string email, string verificationToken)
        {
            var user = await GetByEmailAsync(email);
            if (user == null || user.VerificationToken != verificationToken)
            {
                return false;
            }

            user.IsVerified = true;
            user.VerificationToken = string.Empty; // Clear the token after verification
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task UpdateAsync(User user)
        {
            user.UpdatedAt = DateTime.UtcNow;
            _context.Users.Update(user);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<User>> GetAllAsync()
        {
            return await _context.Users.ToListAsync();
        }

        public async Task<User?> GetByVerificationTokenAsync(string token)
        {
            return await _context.Users.FirstOrDefaultAsync(u => u.VerificationToken == token);
        }

        public async Task<User?> GetByResetTokenAsync(string token)
        {
            return await _context.Users.FirstOrDefaultAsync(u => u.PasswordResetToken == token);
        }
        public async Task<User?> GetUserByOtpAsync(string otp)
        {
            if (string.IsNullOrEmpty(otp))
                throw new ArgumentNullException(nameof(otp), "OTP cannot be null or empty.");

            return await _context.Users
                .FirstOrDefaultAsync(u => u.Otp == otp && u.OtpExpiry >= DateTime.UtcNow);
        }

        public async Task<User?> GetByIdAsync(Guid id)
        {
            return await _context.Users.FirstOrDefaultAsync(u => u.Id == id);
        }

        public async Task<User?> GetByRefreshTokenAsync(string refreshToken)
        {
            return await _context.Users
                .FirstOrDefaultAsync(u => u.RefreshToken == refreshToken);
        }
    }
}
