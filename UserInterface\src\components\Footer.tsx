import React from "react";
import { Link } from "react-router-dom";
import FooterLink from "./FooterLink";

const Footer: React.FC = () => {
  return (
    <footer className="py-12 px-6 text-white bg-gray-800" style={{ minHeight: "300px" }}>
      <div className="container mx-auto max-w-6xl">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-sm font-semibold text-white mb-4">
              Plateforme
            </h3>
            <ul className="space-y-2">
              <FooterLink href="/features">Fonctionnalités</FooterLink>
              <FooterLink href="/pricing">Tarifs</FooterLink>
              <FooterLink href="/login">Connexion</FooterLink>
              <FooterLink href="/register">Inscription</FooterLink>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-semibold text-white mb-4">L<PERSON>gal</h3>
            <ul className="space-y-2">
              <FooterLink href="/terms">Conditions d'utilisation</FooterLink>
              <FooterLink href="/privacy">
                Politique de confidentialité
              </FooterLink>
              <FooterLink href="/cookies">Cookies</FooterLink>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-semibold text-white mb-4">
              Ressources
            </h3>
            <ul className="space-y-2">
              <FooterLink href="/blog">Blog</FooterLink>
              <FooterLink href="/guides">Guides</FooterLink>
              <FooterLink href="/support">Support</FooterLink>
            </ul>
          </div>

          <div>
            <h3 className="text-sm font-semibold text-white mb-4">Contact</h3>
            <ul className="space-y-2">
              <FooterLink href="/contact">Nous contacter</FooterLink>
              <FooterLink href="/about">À propos</FooterLink>
              <FooterLink href="/careers">Carrières</FooterLink>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-white/10 text-center">
          <p className="text-sm text-white/60">
            © {new Date().getFullYear()} SiteChecker. Tous droits réservés.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
