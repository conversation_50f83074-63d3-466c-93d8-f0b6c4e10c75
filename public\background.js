chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Handle incoming messages here
  console.log("Message received:", request);

  // Ensure a response is always sent
  if (request.action === "someAction") {
    // Perform some asynchronous operation if needed
    sendResponse({
      status: "success",
      message: "Message received successfully!",
    });
  } else {
    sendResponse({ status: "error", message: "Unknown action." });
    return; // Ensure the function exits after sending the response
  }
});
