import { toast } from "../hooks/use-toast";

/**
 * Shows a success toast message
 * @param message The message to display
 */
export const showSuccessToast = (message: string) => {
  if (typeof message !== "string") {
    console.error("Invalid message for success toast:", message);
    message = "Success!";
  }

  toast({
    title: "Success",
    description: message,
    variant: "default",
  });
};

/**
 * Shows an error toast message
 * @param message The message to display
 */
export const showErrorToast = (message: string) => {
  if (typeof message !== "string") {
    console.error("Invalid message for error toast:", message);
    message = "An error occurred.";
  }

  toast({
    title: "Error",
    description: message,
    variant: "destructive",
  });
};

/**
 * Shows an info toast message
 * @param message The message to display
 */
export const showInfoToast = (message: string) => {
  if (typeof message !== "string") {
    console.error("Invalid message for info toast:", message);
    message = "Information";
  }

  toast({
    title: "Info",
    description: message,
    variant: "default",
  });
};

/**
 * Shows a warning toast message
 * @param message The message to display
 */
export const showWarningToast = (message: string) => {
  if (typeof message !== "string") {
    console.error("Invalid message for warning toast:", message);
    message = "Warning";
  }

  toast({
    title: "Warning",
    description: message,
    variant: "default",
  });
};

/**
 * Shows a loading toast message that can be updated later
 * @param message The message to display
 * @returns A toast ID that can be used to update the toast
 */
export const showLoadingToast = (message: string) => {
  if (typeof message !== "string") {
    console.error("Invalid message for loading toast:", message);
    message = "Loading...";
  }

  return toast({
    title: "Loading",
    description: message,
    variant: "default",
  });
};

export const updateToast = (
  toastId: ReturnType<typeof toast>,
  title: string,
  message: string,
  variant: "default" | "destructive" = "default"
) => {
  if (toastId) {
    toast({
      ...toastId,
      title,
      description: message,
      variant,
    });
  }
};
