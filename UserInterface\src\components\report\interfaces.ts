import { ReactNode } from "react";

// Common interfaces for all technical sections
export interface Finding {
  status: "success" | "warning" | "error";
  label: string;
  impact: string;
}

export interface TechnicalDetail {
  parameter: string;
  value: string | ReactNode;
  impact: string;
  status?:
    | "Implemented"
    | "Not Implemented"
    | "Partially Implemented"
    | "Not Defined"
    | "Not Optimized"
    | "Too Small"
    | string;
  recommended?: string;
  fix?: string;
  tooltip?: string;
}

export interface ActionReference {
  count: number;
  sectionName: string;
  actionWord: string;
  targetId: string;
}

export interface ImpactStatement {
  text: string;
  positivePoints?: string[];
  negativePoints?: string[];
}

export interface TechnicalSectionItem {
  id: string;
  title: string;
  content?: ReactNode;
  score?: number;
  icon?: ReactNode;
  impactStatement?: ImpactStatement;
  findings?: Finding[];
  technicalDetails?: TechnicalDetail[];
  actionReference?: ActionReference;
  useUnifiedModel?: boolean;
}

// Specific section interfaces
export interface VulnerabilitySectionProps {
  vulnerabilities?: any[];
  score?: number;
}

export interface SslSectionProps {
  sslDetails?: any;
  score?: number;
}

export interface HttpHeadersSectionProps {
  headers?: any[];
  score?: number;
}

export interface JsSecuritySectionProps {
  libraries?: any[];
  totalVulnerabilities?: number;
  criticalVulnerabilities?: number;
  highVulnerabilities?: number;
  mediumVulnerabilities?: number;
  lowVulnerabilities?: number;
  score?: number;
}

export interface ApiSecuritySectionProps {
  endpoints?: any[];
  authIssues?: any[];
  apiDetected?: boolean;
  score?: number;
}

export interface MetaTagsSectionProps {
  metaTags?: any[];
  score?: number;
}

export interface MobileOptimizationSectionProps {
  mobileOptimization?: any;
  score?: number;
}

export interface ImageOptimizationSectionProps {
  imageAnalysis?: any;
  score?: number;
}

export interface PerformanceSectionProps {
  performanceMetrics?: any;
  score?: number;
}

export interface ContentSectionProps {
  contentAnalysis?: any;
  score?: number;
}
