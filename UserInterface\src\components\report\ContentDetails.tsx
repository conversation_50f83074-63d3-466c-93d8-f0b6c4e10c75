import React from "react";
import { ContentAnalysis } from "@/types/report";
import { Check, X, AlertTriangle, HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// InfoTooltip Component - Reusable tooltip for technical terms
const InfoTooltip: React.FC<{ term: string; explanation: string }> = ({
  term,
  explanation,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className="inline-flex items-center cursor-help">
          {term}
          <HelpCircle className="h-3.5 w-3.5 ml-1 text-gray-400" />
        </span>
      </TooltipTrigger>
      <TooltipContent className="max-w-xs bg-gray-800 text-white border-gray-700">
        <p className="text-xs">{explanation}</p>
      </TooltipContent>
    </Tooltip>
  );
};

// Section Introduction Component
const SectionIntro: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <div className="mb-4 p-3 neo-card bg-gray-50/50 border-l-4 border-blue-500/50">
      <p className="text-sm text-gray-600">{children}</p>
    </div>
  );
};

// Security Impact Component
const SecurityImpact: React.FC<{
  severity: "high" | "medium" | "low";
  children: React.ReactNode;
}> = ({ severity, children }) => {
  const bgColor =
    severity === "high"
      ? "bg-red-500/10 border-red-500/30"
      : severity === "medium"
      ? "bg-yellow-500/10 border-yellow-500/30"
      : "bg-blue-500/10 border-blue-500/30";

  const icon =
    severity === "high" ? (
      <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0" />
    ) : severity === "medium" ? (
      <AlertTriangle className="h-4 w-4 text-yellow-500 flex-shrink-0" />
    ) : (
      <HelpCircle className="h-4 w-4 text-blue-500 flex-shrink-0" />
    );

  return (
    <div className={`p-2 rounded-lg flex items-start gap-2 ${bgColor}`}>
      {icon}
      <p className="text-xs text-gray-700">{children}</p>
    </div>
  );
};

interface ContentDetailsProps {
  analysis?: ContentAnalysis;
}

const ContentDetails: React.FC<ContentDetailsProps> = ({ analysis }) => {
  if (!analysis) {
    return (
      <p className="text-gray-600">
        Aucune donnée d'analyse de contenu disponible.
      </p>
    );
  }

  // Content analysis explanations
  const contentExplanations = {
    wordCount: {
      term: "Nombre de mots",
      explanation:
        "Nombre total de mots sur la page. Les pages avec un contenu substantiel ont tendance à mieux se classer dans les résultats de recherche.",
      impact:
        "Un contenu trop court peut être considéré comme ayant peu de valeur par les moteurs de recherche, tandis qu'un contenu trop long peut diluer le message principal.",
      threshold: { min: 300, ideal: 1000 },
    },
    readabilityScore: {
      term: "Score de lisibilité",
      explanation:
        "Mesure de la facilité avec laquelle votre contenu peut être lu et compris. Un score plus élevé indique un contenu plus facile à lire.",
      impact:
        "Un contenu difficile à lire peut augmenter le taux de rebond et réduire l'engagement des utilisateurs, ce qui affecte négativement le référencement.",
      threshold: { min: 60, ideal: 80 },
    },
    headingsStructure: {
      term: "Structure des titres",
      explanation:
        "Organisation hiérarchique des titres (H1, H2, H3, etc.) sur la page. Une bonne structure aide les utilisateurs et les moteurs de recherche à comprendre l'organisation du contenu.",
      impact:
        "Une structure de titres mal organisée peut rendre le contenu difficile à parcourir et à comprendre, ce qui nuit à l'expérience utilisateur et au référencement.",
    },
    keywordDensity: {
      term: "Densité des mots-clés",
      explanation:
        "Fréquence d'apparition des mots-clés dans le contenu. Une densité optimale est généralement entre 1% et 3%.",
      impact:
        "Une densité de mots-clés trop faible peut limiter la visibilité dans les résultats de recherche, tandis qu'une densité trop élevée peut être considérée comme du bourrage de mots-clés.",
    },
    contentQualityScore: {
      term: "Score de qualité du contenu",
      explanation:
        "Évaluation globale de la qualité du contenu basée sur plusieurs facteurs comme l'originalité, la pertinence et la profondeur.",
      impact:
        "Un contenu de faible qualité peut nuire à la réputation de votre site et à son classement dans les résultats de recherche.",
      threshold: { min: 70, ideal: 90 },
    },
  };

  // Helper function to determine status based on thresholds
  const getContentStatus = (
    metric: string,
    value: number
  ): "success" | "warning" | "error" => {
    const thresholds: Record<string, { min: number; ideal: number }> = {
      wordCount: { min: 300, ideal: 1000 },
      readabilityScore: { min: 60, ideal: 80 },
      contentQualityScore: { min: 70, ideal: 90 },
    };

    if (!thresholds[metric]) return "success";

    if (value >= thresholds[metric].ideal) return "success";
    if (value >= thresholds[metric].min) return "warning";
    return "error";
  };

  // Determine status for each metric with null checks
  const wordCountStatus = analysis?.wordCount
    ? getContentStatus("wordCount", analysis.wordCount)
    : "success";
  const readabilityStatus = analysis?.readabilityScore
    ? getContentStatus("readabilityScore", analysis.readabilityScore)
    : "success";
  const contentQualityStatus = analysis?.contentQualityScore
    ? getContentStatus("contentQualityScore", analysis.contentQualityScore)
    : "success";

  // Helper function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "success":
        return <Check className="h-4 w-4 text-green-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "error":
        return <X className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  // Helper function to check heading structure
  const getHeadingStructureStatus = (): "success" | "warning" | "error" => {
    if (!analysis.headingsStructure) return "warning";

    const { h1Count, headingsInOrder } = analysis.headingsStructure;

    if (h1Count === 1 && headingsInOrder) return "success";
    if (h1Count === 1 || headingsInOrder) return "warning";
    return "error";
  };

  const headingStructureStatus = getHeadingStructureStatus();

  return (
    <div className="space-y-6">
      <SectionIntro>
        Le contenu est l'un des facteurs les plus importants pour le
        référencement. Un contenu de qualité, bien structuré et pertinent pour
        les utilisateurs améliore votre visibilité dans les résultats de
        recherche et l'engagement des visiteurs.
      </SectionIntro>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="neo-card p-4">
          <div className="flex items-center justify-between mb-2">
            <InfoTooltip
              term={contentExplanations.wordCount.term}
              explanation={contentExplanations.wordCount.explanation}
            />
            {getStatusIcon(wordCountStatus)}
          </div>
          <p className="text-gray-800 font-medium">
            {analysis?.wordCount || 0} mots
          </p>
        </div>

        {analysis.readabilityScore && (
          <div className="neo-card p-4">
            <div className="flex items-center justify-between mb-2">
              <InfoTooltip
                term={contentExplanations.readabilityScore.term}
                explanation={contentExplanations.readabilityScore.explanation}
              />
              {getStatusIcon(readabilityStatus)}
            </div>
            <p className="text-gray-800 font-medium">
              {analysis.readabilityScore}/100
            </p>
          </div>
        )}

        {analysis.contentQualityScore && (
          <div className="neo-card p-4">
            <div className="flex items-center justify-between mb-2">
              <InfoTooltip
                term={contentExplanations.contentQualityScore.term}
                explanation={
                  contentExplanations.contentQualityScore.explanation
                }
              />
              {getStatusIcon(contentQualityStatus)}
            </div>
            <p className="text-gray-800 font-medium">
              {analysis.contentQualityScore}%
            </p>
          </div>
        )}

        {analysis.headingsStructure && (
          <div className="neo-card p-4">
            <div className="flex items-center justify-between mb-2">
              <InfoTooltip
                term={contentExplanations.headingsStructure.term}
                explanation={contentExplanations.headingsStructure.explanation}
              />
              {getStatusIcon(headingStructureStatus)}
            </div>
            <div className="grid grid-cols-3 gap-2 mt-2">
              <div className="text-center">
                <span className="text-xs text-gray-600">H1</span>
                <p className="text-gray-800 font-medium">
                  {analysis.headingsStructure.h1Count}
                </p>
              </div>
              <div className="text-center">
                <span className="text-xs text-gray-600">H2</span>
                <p className="text-gray-800 font-medium">
                  {analysis.headingsStructure.h2Count}
                </p>
              </div>
              <div className="text-center">
                <span className="text-xs text-gray-600">H3+</span>
                <p className="text-gray-800 font-medium">
                  {analysis.headingsStructure.h3Count +
                    analysis.headingsStructure.h4Count +
                    analysis.headingsStructure.h5Count +
                    analysis.headingsStructure.h6Count}
                </p>
              </div>
            </div>
            <div className="mt-2 text-xs text-gray-600">
              Structure hiérarchique:{" "}
              {analysis.headingsStructure.headingsInOrder ? (
                <span className="text-green-600">Correcte</span>
              ) : (
                <span className="text-red-600">Incorrecte</span>
              )}
            </div>
          </div>
        )}
      </div>

      {analysis.keywordDensity &&
        Object.keys(analysis.keywordDensity).length > 0 && (
          <div className="neo-card p-4">
            <div className="flex items-center justify-between mb-3">
              <InfoTooltip
                term={contentExplanations.keywordDensity.term}
                explanation={contentExplanations.keywordDensity.explanation}
              />
            </div>
            <div className="space-y-2">
              {Object.entries(analysis.keywordDensity)
                .slice(0, 5)
                .map(([keyword, density], idx) => (
                  <div key={idx} className="flex justify-between items-center">
                    <span className="text-gray-700">{keyword}</span>
                    <div className="flex items-center">
                      <div className="w-24 h-2 bg-gray-200 rounded-full overflow-hidden mr-2">
                        <div
                          className={`h-full ${
                            density > 0.04
                              ? "bg-red-500"
                              : density > 0.03
                              ? "bg-yellow-500"
                              : "bg-green-500"
                          }`}
                          style={{
                            width: `${Math.min(density * 100 * 2, 100)}%`,
                          }}
                        ></div>
                      </div>
                      <span className="text-gray-800 font-medium">
                        {(density * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        )}

      {/* Impact section */}
      <div className="mt-4">
        <h4 className="text-sm font-medium mb-2 text-gray-700">
          Impact sur le référencement
        </h4>

        {wordCountStatus !== "success" && (
          <SecurityImpact
            severity={wordCountStatus === "error" ? "high" : "medium"}
          >
            {contentExplanations.wordCount.impact}
          </SecurityImpact>
        )}

        {readabilityStatus !== "success" && analysis.readabilityScore && (
          <SecurityImpact
            severity={readabilityStatus === "error" ? "high" : "medium"}
          >
            {contentExplanations.readabilityScore.impact}
          </SecurityImpact>
        )}

        {contentQualityStatus !== "success" && analysis.contentQualityScore && (
          <SecurityImpact
            severity={contentQualityStatus === "error" ? "high" : "medium"}
          >
            {contentExplanations.contentQualityScore.impact}
          </SecurityImpact>
        )}

        {headingStructureStatus !== "success" && analysis.headingsStructure && (
          <SecurityImpact
            severity={headingStructureStatus === "error" ? "high" : "medium"}
          >
            {contentExplanations.headingsStructure.impact}
          </SecurityImpact>
        )}
      </div>

      {/* Recommendations section */}
      <div className="neo-card p-4">
        <h4 className="text-gray-800 font-medium mb-3">Recommandations</h4>
        <ul className="space-y-3">
          {wordCountStatus !== "success" && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                {wordCountStatus === "error"
                  ? "Augmentez significativement la quantité de contenu pour atteindre au moins 300 mots."
                  : "Enrichissez votre contenu pour atteindre idéalement 1000 mots ou plus."}
              </span>
            </li>
          )}

          {readabilityStatus !== "success" && analysis.readabilityScore && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Améliorez la lisibilité en utilisant des phrases plus courtes,
                un vocabulaire plus simple et en structurant mieux votre
                contenu.
              </span>
            </li>
          )}

          {headingStructureStatus !== "success" &&
            analysis.headingsStructure && (
              <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
                <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 text-sm">
                  {analysis.headingsStructure.h1Count !== 1
                    ? "Assurez-vous d'avoir exactement un titre H1 sur votre page."
                    : "Structurez vos titres de manière hiérarchique (H1, puis H2, puis H3, etc.)."}
                </span>
              </li>
            )}

          {Object.entries(analysis.keywordDensity || {}).some(
            ([_, density]) => density > 0.04
          ) && (
            <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <span className="text-gray-700 text-sm">
                Réduisez la densité de certains mots-clés qui dépassent 4%, car
                cela pourrait être considéré comme du bourrage de mots-clés.
              </span>
            </li>
          )}

          {contentQualityStatus !== "success" &&
            analysis.contentQualityScore && (
              <li className="flex items-start gap-2 neo-card p-3 border border-yellow-500/30">
                <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 text-sm">
                  Améliorez la qualité globale du contenu en le rendant plus
                  informatif, original et pertinent pour votre public cible.
                </span>
              </li>
            )}

          {wordCountStatus === "success" &&
            (readabilityStatus === "success" || !analysis.readabilityScore) &&
            (contentQualityStatus === "success" ||
              !analysis.contentQualityScore) &&
            (headingStructureStatus === "success" ||
              !analysis.headingsStructure) &&
            !Object.entries(analysis.keywordDensity || {}).some(
              ([_, density]) => density > 0.04
            ) && (
              <li className="flex items-start gap-2 neo-card p-3 border border-green-500/30">
                <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 text-sm">
                  Votre contenu est bien optimisé. Continuez à le maintenir à
                  jour et pertinent pour votre audience.
                </span>
              </li>
            )}
        </ul>
      </div>
    </div>
  );
};

export default ContentDetails;
