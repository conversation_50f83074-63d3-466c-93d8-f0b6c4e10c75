import React from "react";
import { <PERSON><PERSON><PERSON>cle, AlertTriangle, XCircle } from "lucide-react";
import { StatusType } from "@/types/report";

interface StatusPillProps {
  status: StatusType;
}

const statusConfig = {
  success: {
    color: "bg-green-100 text-green-800",
    icon: <CheckCircle className="w-3 h-3 mr-1" />,
    label: "Bon",
  },
  warning: {
    color: "bg-amber-100 text-amber-800",
    icon: <AlertTriangle className="w-3 h-3 mr-1" />,
    label: "Attention",
  },
  error: {
    color: "bg-red-100 text-red-800",
    icon: <XCircle className="w-3 h-3 mr-1" />,
    label: "Problème",
  },
} as const;

// Type guard to check if a string is a valid status
const isValidStatus = (
  status: string | undefined
): status is keyof typeof statusConfig => {
  return status !== undefined && status in statusConfig;
};

const StatusPill: React.FC<StatusPillProps> = ({ status }) => {
  // Validate status and fallback to 'warning' if invalid
  const validStatus = isValidStatus(status) ? status : "warning";
  const config = statusConfig[validStatus];

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}
    >
      {config.icon}
      {config.label}
    </span>
  );
};

export default StatusPill;
